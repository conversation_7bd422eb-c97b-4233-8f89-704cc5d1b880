<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BillingController;
use App\Http\Controllers\TrafficCodeController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\VehicleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RedisExampleController;
use App\Http\Controllers\TripTemplateController;
use App\Http\Controllers\RoutingController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\OrderTypeController;
use App\Http\Controllers\RegionsController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\VatMasterController;
use App\Http\Controllers\RateServiceController;
use App\Http\Controllers\StatusMasterController;
use App\Http\Controllers\LanesMasterController;
use App\Http\Controllers\VehicleTypeController;
use App\Http\Controllers\DriverController;
use App\Http\Controllers\RateOfferingController;
use App\Http\Controllers\RateRecordController;
use App\Http\Controllers\VehicleProfileController;
use App\Http\Controllers\MasterController;
use App\Http\Controllers\CustomerProfileController;
use App\Http\Controllers\VendorProfileController;
use App\Http\Controllers\NotifymgmtController;
use App\Http\Controllers\TenderController;
use App\Http\Controllers\ShipmentPlanController;
use App\Jobs\RabbitmqHelloWorldJob;
use App\Http\Controllers\CarrierRatesController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CustomersController;
use App\Http\Controllers\CarriersController;
use App\Http\Controllers\PartiesController;
use App\Http\Controllers\VisibilityController;
use App\Http\Controllers\FleetviewController;
use App\Http\Controllers\CustomerFleetviewController;
use App\Http\Controllers\WorkbenchController;
use App\Http\Controllers\CustomerWorkbenchController;
use App\Http\Controllers\ActiveOrdersController;
use App\Http\Controllers\PendingOrdersController;
use App\Http\Controllers\CompletedOrdersController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\OrderRevenuesController;
use App\Http\Controllers\TripExpenseController;
use App\Http\Controllers\GeofenceController;
use App\Http\Controllers\TripController;
use App\Http\Controllers\ReturnTrucksController;
use App\Http\Controllers\ExcelImportOrdersController;
use App\Http\Controllers\CabReportsController;
use App\Http\Controllers\ChargesDistributionController;
use App\Http\Controllers\SendMultipleAconEdiController;
use App\Http\Controllers\RateTierController;
use App\Http\Controllers\RatePreferenceController;
use App\Http\Controllers\MassStatusUpdateController;
use App\Http\Controllers\AllRoundBigReportController;
use App\Http\Controllers\BillingControlReportController;
use App\Http\Controllers\TransitStatusReportController;
use App\Http\Controllers\GeoTierController;
use App\Http\Controllers\XBorderOrdersController;
use App\Http\Controllers\DocumentControlController;
use App\Http\Controllers\PalletizerController;
use App\Http\Controllers\DriverApiController;
use App\Http\Controllers\CustomerAddressController;
use App\Http\Controllers\CustomerGlobalSearchController;
use App\Http\Controllers\CustomerOrdersController;
use App\Http\Controllers\CustomerDashboardController;

Route::get('/ping', function () {
    return response()->json(['pong' => true]);
});



Route::get('/test-queue1', function () {
    RabbitmqHelloWorldJob::dispatch('Sent to queue1')->onQueue('queue1');
    return 'Dispatched to queue1!';
});

Route::get('/test-queue2', function () {
    RabbitmqHelloWorldJob::dispatch('Sent to queue2')->onQueue('queue2');
    return 'Dispatched to queue2!';
});

Route::post('register', [AuthController::class, 'register']); // Registration disabled
Route::post('login', [AuthController::class, 'login']);
Route::post('refresh', [AuthController::class, 'refresh'])->middleware('throttle:10,1');
Route::post('forgotpassword', [UserController::class, 'forgotPassword']);
Route::post('resetpassword', [UserController::class, 'resetPassword']);

// TODO: Uncomment for production - Authentication bypassed for testing
Route::middleware('auth:api')->group(function () {
    Route::post('logout', [AuthController::class, 'logout']);
    Route::get('me', [AuthController::class, 'me']);

    Route::get('shipment-types', [ShipmentController::class, 'index']);
    Route::post('shipment-types', [ShipmentController::class, 'store']);
    Route::get('shipment-types/{id}', [ShipmentController::class, 'show']);
    Route::put('shipment-types/{id}', [ShipmentController::class, 'update']);
    Route::delete('shipment-types/{id}', [ShipmentController::class, 'destroy']);

    Route::get('vehicle-types', [VehicleTypeController::class, 'index']);
    Route::post('vehicle-types', [VehicleTypeController::class, 'store']);
    Route::get('vehicle-types/{id}', [VehicleTypeController::class, 'show']);
    Route::put('vehicle-types/{id}', [VehicleTypeController::class, 'update']);
    Route::delete('vehicle-types/{id}', [VehicleTypeController::class, 'destroy']);

    Route::get('order-types', [OrderTypeController::class, 'index']);
    Route::post('order-types', [OrderTypeController::class, 'store']);
    Route::get('order-types/{id}', [OrderTypeController::class, 'show']);
    Route::put('order-types/{id}', [OrderTypeController::class, 'update']);
    Route::delete('order-types/{id}', [OrderTypeController::class, 'destroy']);

    Route::get('regions', [RegionsController::class, 'index']);
    Route::post('regions', [RegionsController::class, 'store']);
    Route::get('regions/{id}', [RegionsController::class, 'show']);
    Route::put('regions/{id}', [RegionsController::class, 'update']);
    Route::delete('regions/{id}', [RegionsController::class, 'destroy']);

    Route::get('vehicles', [VehicleController::class, 'index']);
    // Route::get('vehicles/add', [VehicleController::class, 'add']);
    Route::post('vehicles', [VehicleController::class, 'store']);
    // Route::get('vehicles/edit/{id}', [VehicleController::class, 'edit']);
    Route::get('vehicles/{id}', [VehicleController::class, 'show']);
    Route::put('vehicles/{id}', [VehicleController::class, 'update']);
    Route::delete('vehicles/{id}', [VehicleController::class, 'destroy']);

    Route::get('drivers', [DriverController::class, 'index']);
    // Route::get('drivers/add', [DriverController::class, 'add']);
    Route::post('drivers', [DriverController::class, 'store']);
    // Route::get('drivers/edit/{id}', [DriverController::class, 'edit']);
    Route::get('drivers/{id}', [DriverController::class, 'show']);
    Route::put('drivers/{id}', [DriverController::class, 'update']);
    Route::delete('drivers/{id}', [DriverController::class, 'destroy']);
    Route::get('drivers/assign/{id}', [DriverController::class, 'assign']);
    Route::post('drivers/deassign/{id}', [DriverController::class, 'deassign']);
    Route::get('drivers/vehicleassign/{id}', [DriverController::class, 'vehicleassign']);
    Route::post('drivers/check-trip', [DriverController::class, 'checkTrip']);
    Route::post('drivers/assign-driver', [DriverController::class, 'assigndriver']);

    Route::get('transport-modes', [MasterController::class, 'getmodeOfTransports']);
    Route::get('business-partners', [MasterController::class, 'partnerIndex']);
    // Route::get('businessPartners/form-meta/add', [MasterController::class, 'partnerAdd']);
    // Route::get('businessPartners/form-meta/edit/{id}', [MasterController::class, 'partnerEdit']);
    Route::post('business-partners', [MasterController::class, 'partnerCreate']);
    Route::get('business-partners/{id}', [MasterController::class, 'partnerShow']);
    Route::put('business-partners/{id}', [MasterController::class, 'partnerUpdate']);
    Route::delete('business-partners/{id}', [MasterController::class, 'partnerDestroy']);

    Route::get('shipmentplan', [ShipmentPlanController::class, 'index']);
    Route::post('shipmentplan/filterOrders', [ShipmentPlanController::class, 'filterOrders']);
    Route::post('shipmentplan/filterShipments', [ShipmentPlanController::class, 'filterShipments']);
    Route::post('shipmentplan/filterIntransitShipments', [ShipmentPlanController::class, 'filterIntransitShipments']);
    Route::post('shipmentplan/stopdetail', [ShipmentPlanController::class, 'stopdetail']);
    Route::post('shipmentplan/getorderdetail', [ShipmentPlanController::class, 'getorderdetail']);
    Route::post('shipmentplan/createorder', [ShipmentPlanController::class, 'createorder']);
    Route::get('shipmentplan/addshipment', [ShipmentPlanController::class, 'addshipment']);
    Route::post('shipmentplan/editShipment', [ShipmentPlanController::class, 'editShipment']);
    Route::get('shipmentplan/getdriversbyvendor', [ShipmentPlanController::class, 'getdriversbyvendor']);
    Route::post('shipmentplan/storeshipment', [ShipmentPlanController::class, 'storeshipment']);
    Route::post('shipmentplan/storeOrder', [ShipmentPlanController::class, 'storeorder']);
    Route::post('shipmentplan/orderIntoShipment', [ShipmentPlanController::class, 'orderIntoShipment']);
    Route::post('shipmentplan/linesIntoShipment', [ShipmentPlanController::class, 'linesIntoShipment']);
    Route::post('shipmentplan/freightTracker', [ShipmentPlanController::class, 'freightTracker']);
    Route::post('shipmentplan/deallocateOrder', [ShipmentPlanController::class, 'deallocateOrder']);
    Route::get('shipmentplan/shipmentFreightTracker/{shpid}', [ShipmentPlanController::class, 'shipmentFreightTracker']);

    Route::prefix('tripexpense')->group(function () {
        Route::get('/', [TripExpenseController::class, 'index']);
        Route::get('/trucks', [TripExpenseController::class, 'getTrucks']);
        Route::get('/expenses', [TripExpenseController::class, 'getExpenses']);
        Route::get('/{id}', [TripExpenseController::class, 'view']);
        Route::get('/{id}/edit', [TripExpenseController::class, 'edit']);
        Route::post('/', [TripExpenseController::class, 'store']);
        Route::put('/{id}', [TripExpenseController::class, 'update']);
        Route::delete('/{id}', [TripExpenseController::class, 'delete']);
        Route::post('/addcategory', [TripExpenseController::class, 'addcategory']);
        Route::post('/uploadexpences', [TripExpenseController::class, 'uploadexpences']);
    });

    Route::prefix('cabreports')->group(function () {
        Route::get('/slareports', [CabReportsController::class, 'slareports']);
        Route::get('/shiftreports', [CabReportsController::class, 'shiftreports']);
        Route::get('/smsreports', [CabReportsController::class, 'smsreports']);
        Route::get('/smsreports/list', [CabReportsController::class, 'smsreportsList']);
        Route::get('/ediTransaction', [CabReportsController::class, 'ediTransactionList']);
    });
    Route::prefix('allroundbigreport')->group(function () {
        Route::get('/', [AllRoundBigReportController::class, 'getsearchresult']);
    });

    Route::prefix('billingcontrolreport')->group(function () {
        Route::get('/', [BillingControlReportController::class, 'getsearchresult']);
    });

    Route::prefix('transitstatusreport')->group(function () {
        Route::get('/', [TransitStatusReportController::class, 'TransitStatusReports']);
    });
});

// TODO: Uncomment for production - Authentication bypassed for testing
Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/health', function () {
    return response()->json(['status' => 'OK'], 200);
});

// TODO: Uncomment for production - Authentication bypassed for testing
Route::middleware('auth:api')->get('/users', [UserController::class, 'index']);

Route::group(['prefix' => 'billing', 'middleware' => 'auth:api'], function () {
    Route::post('/add', [BillingController::class, 'add']);
});
Route::get('/users', [UserController::class, 'index']);

Route::group(['prefix' => 'billing'], function () {
    Route::post('/add', [BillingController::class, 'add']);
});

Route::middleware('auth:api')->group(function () {
    //Traffic codes
    Route::get('/traffic-codes', [TrafficCodeController::class, 'index'])->name('traffic-codes.index');
    // Route::get('/traffic-codes/create', [TrafficCodeController::class, 'create'])->name('traffic-codes.create');
    Route::post('/traffic-codes', [TrafficCodeController::class, 'store'])->name('traffic-codes.store');
    // Route::get('/traffic-codes/edit/{id}', [TrafficCodeController::class, 'edit'])->name('traffic-codes.edit');
    Route::put('/traffic-codes/{id}', [TrafficCodeController::class, 'update'])->name('traffic-codes.update');
    Route::delete('/traffic-codes/{id}', [TrafficCodeController::class, 'delete'])->name('traffic-codes.delete');
    Route::get('/traffic-codes/{id}', [TrafficCodeController::class, 'show'])->name('traffic-codes.show');

    //Trip templates
    Route::get('/triptemplates', [TripTemplateController::class, 'triptemplateindex'])->name('triptemplate.index');
    Route::get('/triptemplates/add', [TripTemplateController::class, 'addTemplate'])->name('triptemplate.addTemplate');
    Route::get('/triptemplates/view/{id}', [TripTemplateController::class, 'viewTemplate'])->name('triptemplate.view');
    Route::get('/triptemplates/edit/{id}', [TripTemplateController::class, 'editTemplate'])->name('triptemplate.edit');
    Route::post('/triptemplates/insert', [TripTemplateController::class, 'insertTemplate'])->name('triptemplate.store');
    Route::post('/triptemplates/delete/{id}', [TripTemplateController::class, 'deleteTripTemplateById'])->name('triptemplate.destroy');
    Route::post('/triptemplates/update/{id}', [TripTemplateController::class, 'updateTripWithTripsTemplate'])->name('triptemplate.update-trip');
    Route::get('/triptemplates/master-data', [TripTemplateController::class, 'getMasterDataForTripTemplate'])->name('triptemplate.master-data');
    Route::get('/triptemplates/waypoint-parties', [TripTemplateController::class, 'getWaypointParties'])->name('triptemplate.waypoint-parties');

    //Routing
    Route::get('/routing', [RoutingController::class, 'routingindex'])->name('routing.index');
    Route::get('/routing/add', [RoutingController::class, 'addrouting'])->name('routing.add');
    Route::get('/routing/view/{id}', [RoutingController::class, 'viewrouting'])->name('routing.view');
    Route::get('/routing/edit/{id}', [RoutingController::class, 'editrouting'])->name('routing.edit');
    Route::post('/routing/save', [RoutingController::class, 'saverouting'])->name('routing.save');
    Route::post('/routing/update/{id}', [RoutingController::class, 'updaterouting'])->name('routing.update');
    Route::delete('/routing/{id}', [RoutingController::class, 'deleterouting'])->name('routing.delete');
    Route::get('/routing/items', [RoutingController::class, 'getItemListID'])->name('routing.items');
    Route::get('/routing/cargo', [RoutingController::class, 'viewCargoList'])->name('routing.cargo');
    Route::get('/routing/party-types', [RoutingController::class, 'getAllPartyTypeList'])->name('routing.party-types');

    // VatMaster
    Route::get('/vatmaster', [VatMasterController::class, 'index']);
    Route::get('/vatmaster/add', [VatMasterController::class, 'add']);
    Route::post('/vatmaster/role-types', [VatMasterController::class, 'viewRoleTypeList']);
    Route::post('/vatmaster/partner-details', [VatMasterController::class, 'getPartnerDetailsById']);
    Route::post('/vatmaster/save-lane', [VatMasterController::class, 'saveLane']);
    Route::get('/vatmaster/recent-lanes', [VatMasterController::class, 'getRecentLanes']);
    Route::post('/vatmaster/lane/{id}', [VatMasterController::class, 'getLaneById']);
    Route::post('/vatmaster/update-lane', [VatMasterController::class, 'updateLane']);
    Route::post('/vatmaster/charge-description', [VatMasterController::class, 'getChargeDescription']);
    Route::post('/vatmaster/save-lane-vat', [VatMasterController::class, 'saveLaneVat']);
    Route::post('/vatmaster/lane-vats', [VatMasterController::class, 'getLaneVats']);
    Route::post('/vatmaster/charge/{id}', [VatMasterController::class, 'getChargeById']);
    Route::post('/vatmaster/update-vat', [VatMasterController::class, 'updateVat']);
    Route::post('/vatmaster/delete-charge', [VatMasterController::class, 'deleteCharge']);
    Route::post('/vatmaster/delete-lane', [VatMasterController::class, 'deleteLane']);
    Route::post('/vatmaster/insert-vat', [VatMasterController::class, 'insertVatData']);
    Route::post('/vatmaster/get-lanes', [VatMasterController::class, 'getLanes']);
    Route::get('/vatmaster/edit-vat/{id}', [VatMasterController::class, 'editVat']);
    Route::post('/vatmaster/update-vat-data', [VatMasterController::class, 'updateVatData']);
    Route::get('/vatmaster/view-vat/{id}', [VatMasterController::class, 'viewVat']);
    Route::post('/vatmaster/delete-vat-master', [VatMasterController::class, 'deleteVatMaster']);

    // Rate-Service
    Route::match(['get', 'post'], '/rateservice/index', [RateServiceController::class, 'index']);
    Route::get('/rateservice/add', [RateServiceController::class, 'add']);
    Route::post('/rateservice/getLaneById', [RateServiceController::class, 'getLaneById']);
    Route::post('/rateservice/saveLane', [RateServiceController::class, 'saveLane']);
    Route::post('/rateservice/showLaneDetails', [RateServiceController::class, 'showLaneDetails']);
    Route::post('/rateservice/updateLane/{id}', [RateServiceController::class, 'updateLane']);
    Route::post('/rateservice/deleteLaneDetails', [RateServiceController::class, 'deleteLaneDetails']);
    Route::post('/rateservice/checkServiceName', [RateServiceController::class, 'checkName']);
    Route::post('/rateservice/insertService', [RateServiceController::class, 'insertService']);
    Route::get('/rateservice/edit/{id}', [RateServiceController::class, 'edit']);
    Route::post('/rateservice/editlanedetails', [RateServiceController::class, 'editLaneDetails']);
    Route::post('/rateservice/serviceupdate', [RateServiceController::class, 'serviceUpdate']);
    Route::post('/rateservice/delete', [RateServiceController::class, 'delete']);

    //status master
    Route::get('/statusmaster', [StatusMasterController::class, 'index']);
    // Route::get('/statusmaster/add', [StatusMasterController::class, 'add']);
    Route::post('/statusmaster', [StatusMasterController::class, 'store']);
    // Route::get('/statusmaster/edit/{id}', [StatusMasterController::class, 'edit']);
    Route::put('/statusmaster/{id}', [StatusMasterController::class, 'update']);
    Route::get('/statusmaster/{id}', [StatusMasterController::class, 'show']);
    Route::delete('/statusmaster/{id}', [StatusMasterController::class, 'destroy']);

    //Lanes Master
    Route::get('/lanes', [LanesMasterController::class, 'index']);
    // Route::get('/lanes/add', [LanesMasterController::class, 'add']);
    Route::post('/lanes', [LanesMasterController::class, 'store']);
    Route::put('/lanes/{id}', [LanesMasterController::class, 'update']);
    // Route::get('/lanes/edit/{id}', [LanesMasterController::class, 'edit']);
    Route::get('/lanes/geo-master-data', [LanesMasterController::class, 'getGeoMasterData']);
    Route::get('/lanes/{id}', [LanesMasterController::class, 'show']);
    Route::delete('/lanes/{id}', [LanesMasterController::class, 'destroy']);


    Route::match(['get', 'post'], '/rateoffering', [RateOfferingController::class, 'index']);
    Route::match(['get', 'post'], '/rateoffering/add', [RateOfferingController::class, 'add']);
    Route::post('/rateoffering/conversionlist', [RateOfferingController::class, 'conversionlist']);
    Route::post('/rateoffering/getrateservicelanes', [RateOfferingController::class, 'getrateservicelanes']);
    Route::post('/rateoffering/getvehiclepflist', [RateOfferingController::class, 'getvehiclepflist']);
    Route::post('/rateoffering/getvendorpflist', [RateOfferingController::class, 'getvendorpflist']);
    Route::post('/rateoffering/getcargopflist', [RateOfferingController::class, 'getcargopflist']);
    Route::match(['get', 'post'], '/rateoffering/edit/{rowId}', [RateOfferingController::class, 'edit']);
    Route::post('/rateoffering/getdocs', [RateOfferingController::class, 'getdocs']);
    Route::post('/rateoffering/getremarktypes', [RateOfferingController::class, 'getremarktypes']);
    Route::post('/rateoffering/getservices', [RateOfferingController::class, 'getservices']);
    Route::post('/rateoffering/getvasname', [RateOfferingController::class, 'getvasname']);
    Route::post('/rateoffering/saveservice/{id?}', [RateOfferingController::class, 'saveservice']);
    Route::post('/rateoffering/deleteservice', [RateOfferingController::class, 'deleteservice']);
    Route::post('/rateoffering/getdoctypename', [RateOfferingController::class, 'getdoctypename']);
    Route::post('/rateoffering/savedoc/{id?}', [RateOfferingController::class, 'savedoc']);
    Route::get('/rateoffering/view/{id}', [RateOfferingController::class, 'view']);
    Route::post('/rateoffering/deleterateoffering', [RateOfferingController::class, 'deleterateoffering']);

    // Rate-Record
    Route::match(['get', 'post'], '/raterecord', [RateRecordController::class, 'index']);
    Route::match(['get', 'post'], '/raterecord/add', [RateRecordController::class, 'add']);
    Route::post('/raterecord/getrefval', [RateRecordController::class, 'getrefval']);
    Route::post('/raterecord/conditionssave/{id?}', [RateRecordController::class, 'conditionssave']);
    Route::post('/raterecord/showcondetails', [RateRecordController::class, 'showcondetails']);
    Route::post('/raterecord/deletecondetails', [RateRecordController::class, 'deletecondetails']);
    Route::post('/raterecord/get-vas-name', [RateRecordController::class, 'getVasName']);
    Route::post('/raterecord/save-service/{id?}', [RateRecordController::class, 'saveService']);
    Route::post('/raterecord/show-services', [RateRecordController::class, 'showServices']);
    Route::post('/raterecord/delete-ser-details', [RateRecordController::class, 'deleteSerDetails']);
    Route::post('/raterecord/save-rr-charge/{id?}', [RateRecordController::class, 'saveRrCharge']);
    Route::post('/raterecord/show-charge-details', [RateRecordController::class, 'showChargeDetails']);
    Route::post('/raterecord/check-name', [RateRecordController::class, 'checkName']);
    Route::post('/raterecord/insert', [RateRecordController::class, 'rateRecordInsert']);
    Route::post('/raterecord/edit/{id}', [RateRecordController::class, 'edit']);
    Route::post('/raterecord/update', [RateRecordController::class, 'rateRecordUpdate']);
    Route::post('/raterecord/view/{id}', [RateRecordController::class, 'view']);
    Route::post('/raterecord/delete/{id}', [RateRecordController::class, 'delete']);

    // Billing
    Route::get('/billing', [BillingController::class, 'index']);
    Route::post('/billing/add', [BillingController::class, 'newBill']);
    Route::post('/billing/viewRoleTypeList', [BillingController::class, 'viewRoleTypeList']);
    Route::post('/billing/getPartnerDetailsById', [BillingController::class, 'getPartnerDetailsById']);
    Route::post('/billing/billsfilter', [BillingController::class, 'billsfilter']);
    Route::post('/billing/insertbilldata', [BillingController::class, 'insertbilldata']);
    Route::post('/billing/getchargeswithorder', [BillingController::class, 'getchargeswithorder']);
    Route::post('/billing/getcharges', [BillingController::class, 'getcharges']);
    Route::get('/billing/edit/{id}', [BillingController::class, 'editbill']);
    Route::post('/billing/update', [BillingController::class, 'updatebill']);
    Route::get('/billing/view/{id}', [BillingController::class, 'viewbill']);
    Route::delete('/billing/delete/{id}', [BillingController::class, 'deletebill']);

    Route::get('/getcustomers', [CustomersController::class, 'getCustomers']);
    Route::get('/getcarriers', [CarriersController::class, 'getCarriers']);
    Route::get('/getparties', [PartiesController::class, 'getParties']);
    Route::get('/visibility/shipments', [VisibilityController::class, 'shipments']);
    Route::post('/visibility/assignvehicle', [VisibilityController::class, 'openAssignVehicle']);
    Route::post('/visibility/historyanddocuments', [VisibilityController::class, 'getHistoryAndDocuments']);
    Route::post('/visibility/billingdetails', [VisibilityController::class, 'billingDetails']);
    Route::post('/visibility/securelink', [VisibilityController::class, 'secureLink']);
    Route::post('/visibility/calculateemissions', [VisibilityController::class, 'calculateEmissions']);
    Route::post('/visibility/tripetnshipment', [VisibilityController::class, 'tripEtnshipment']);
    Route::post('/visibility/generatetwb', [VisibilityController::class, 'generateTWB']);
    Route::post('/visibility/shownearbyvehicles', [VisibilityController::class, 'showNearbyVehicles']);
    Route::delete('/visibility/rollbacktripsandshipmentdelete/{id}', [VisibilityController::class, 'rollBackOrderTripDetailsAndShipmentDelete']);
    Route::delete('/visibility/shipmentdelete/{id}', [VisibilityController::class, 'shipmentDelete']);

    Route::get('/getstructuresequences', [UserController::class, 'getStructureSequences']);
});

Route::get('/redis/set', [RedisExampleController::class, 'set']);
Route::get('/redis/get', [RedisExampleController::class, 'get']);

// Vehicle Profile
Route::get('/vehicle-profiles', [VehicleProfileController::class, 'index']);
// Route::get('/addvehicleprofile', [VehicleProfileController::class, 'add']);
Route::post('/vehicle-profiles', [VehicleProfileController::class, 'store']);
Route::put('/vehicle-profiles/{id}', [VehicleProfileController::class, 'update']);
// Route::get('/editvehicleprofile/{id}', [VehicleProfileController::class, 'edit']);
Route::get('/vehicle-profiles/{id}', [VehicleProfileController::class, 'show']);
Route::delete('/vehicle-profiles/{id}', [VehicleProfileController::class, 'destroy']);

// Customer Profile
Route::get('/customer-profiles', [CustomerProfileController::class, 'index'])->name('customer-profiles.index');
// Route::get('/customer-profiles/create', [CustomerProfileController::class, 'create'])->name('customer-profiles.create');
Route::post('/customer-profiles', [CustomerProfileController::class, 'store'])->name('customer-profiles.store');
Route::get('/customer-profiles/{id}', [CustomerProfileController::class, 'show'])->name('customer-profiles.show');
// Route::get('/customer-profiles/edit/{id}', [CustomerProfileController::class, 'edit'])->name('customer-profiles.edit');
Route::put('/customer-profiles/{id}', [CustomerProfileController::class, 'update'])->name('customer-profiles.update');
Route::delete('/customer-profiles/{id}', [CustomerProfileController::class, 'destroy'])->name('customer-profiles.destroy');


// Vendor Profile
Route::get('/vendor-profiles', [VendorProfileController::class, 'index'])->name('vendor-profiles.index');
//Route::get('/vendor-profiles/create', [VendorProfileController::class, 'create'])->name('vendor-profiles.create');
Route::post('/vendor-profiles', [VendorProfileController::class, 'store'])->name('vendor-profiles.store');
Route::get('/vendor-profiles/{id}', [VendorProfileController::class, 'show'])->name('vendor-profiles.show');
//Route::get('/vendor-profiles/edit/{id}', [VendorProfileController::class, 'edit'])->name('vendor-profiles.edit');
Route::put('/vendor-profiles/{id}', [VendorProfileController::class, 'update'])->name('vendor-profiles.update');
Route::delete('/vendor-profiles/{id}', [VendorProfileController::class, 'destroy'])->name('vendor-profiles.destroy');

// Notification Management
Route::get('/notification-management', [NotifymgmtController::class, 'index'])->name('notification-management.index');
// Route::get('/notification-management/create', [NotifymgmtController::class, 'create'])->name('notification-management.create');
Route::post('/notification-management', [NotifymgmtController::class, 'store'])->name('notification-management.store');
Route::get('/notification-management/{id}', [NotifymgmtController::class, 'show'])->name('notification-management.show');
// Route::get('/notification-management/edit/{id}', [NotifymgmtController::class, 'edit'])->name('notification-management.edit');
Route::put('/notification-management/{id}', [NotifymgmtController::class, 'update'])->name('notification-management.update');
Route::delete('/notification-management/{id}', [NotifymgmtController::class, 'destroy'])->name('notification-management.destroy');

// Notification Management additional routes
Route::post('/notification-management/get-notify-data', [NotifymgmtController::class, 'getNotifyData']);
Route::post('/notification-management/check-combination', [NotifymgmtController::class, 'checkCombination']);
Route::post('/notification-management/get-customer-notify-data', [NotifymgmtController::class, 'getCustomerNotifyData']);
Route::post('/notification-management/get-notification-ids', [NotifymgmtController::class, 'getNotificationIds']);
Route::post('/notification-management/get-organizations', [NotifymgmtController::class, 'getOrganizations']);
Route::post('/notification-management/get-customers', [NotifymgmtController::class, 'getCustomers']);
Route::post('/notification-management/get-customers-list', [NotifymgmtController::class, 'getCustomersList']);
Route::post('/notification-management/get-business-entities', [NotifymgmtController::class, 'getBusinessEntities']);
Route::get('/notification-management/test', [NotifymgmtController::class, 'test']);
Route::get('/notification-management/tpl', [NotifymgmtController::class, 'tpl']);
Route::get('/notification-management/test-mail', [NotifymgmtController::class, 'testMail']);

// Tender routes
Route::get('/tenders', [TenderController::class, 'index'])->name('tenders.index');
Route::get('/tenders/create', [TenderController::class, 'create'])->name('tenders.create');
Route::post('/tenders', [TenderController::class, 'store'])->name('tenders.store');
Route::get('/tenders/{id}', [TenderController::class, 'show'])->name('tenders.show');
Route::get('/tenders/edit/{id}', [TenderController::class, 'edit'])->name('tenders.edit');
Route::put('/tenders/{id}', [TenderController::class, 'update'])->name('tenders.update');
Route::delete('/tenders/{id}', [TenderController::class, 'destroy'])->name('tenders.destroy');

// Carrier Rates
Route::get('/carrier-rates', [CarrierRatesController::class, 'index']);
Route::get('/carrier-rates/create', [CarrierRatesController::class, 'create']);
Route::post('/carrier-rates', [CarrierRatesController::class, 'store']);
Route::get('/carrier-rates/{id}', [CarrierRatesController::class, 'show']);
Route::get('/carrier-rates/edit/{id}', [CarrierRatesController::class, 'edit']);
Route::put('/carrier-rates/{id}', [CarrierRatesController::class, 'update']);
Route::delete('/carrier-rates/{id}', [CarrierRatesController::class, 'destroy']);
Route::post('/carrier-rates/upload-excel', [CarrierRatesController::class, 'uploadRateExcel']);
Route::post('/carrier-rates/admin-counter', [CarrierRatesController::class, 'admin_submit_complete_counter']);
Route::post('/carrier-rates/cargo-details/add', [CarrierRatesController::class, 'addnewcargodetails']);
Route::post('/carrier-rates/cargo-details/get', [CarrierRatesController::class, 'getnewcargodetails']);
Route::post('/carrier-rates/cargo-details/update', [CarrierRatesController::class, 'updatenewcargodetails']);
Route::post('/carrier-rates/tender-financial', [CarrierRatesController::class, 'addTenderOrderFinanicalDetails']);

Route::middleware('auth:api')->get('/menu', [UserController::class, 'menu']);

// =============================================================================
// ORDER CRUD OPERATIONS
// =============================================================================

// Order Basic CRUD
// Route::get('/orderslist/{id?}', [OrderController::class, 'index']);
Route::get('/orders', [OrderController::class, 'index']);
// Route::get('/orders/new', [OrderController::class, 'neworder'])->name('orders.new');
Route::post('/orders', [OrderController::class, 'store'])->name('orders.store');
// Route::get('/orders/edit/{id}', [OrderController::class, 'editOrder'])->name('orders.edit');
Route::put('/orders/{id}', [OrderController::class, 'update'])->name('orders.update');
Route::get('/orders/{id}', [OrderController::class, 'show'])->name('orders.show');
Route::delete('/orders/{id}', [OrderController::class, 'destroy'])->name('orders.destroy');
Route::get('/copy-orders/{id}', [OrderController::class, 'copyOrder']);
Route::get('/reverse-orders/{id}', [OrderController::class, 'reverseOrder']);
Route::get('/orders-cancel/{id}', [OrderController::class, 'cancelOrder']);

// =============================================================================
// ORDER PARTIES MANAGEMENT
// =============================================================================

// Order Parties
Route::post('/orders/parties-list', [OrderController::class, 'viewPartyList'])->name('parties.list');
Route::post('/orders/parties/delete', [OrderController::class, 'deleteOrderPartyDetails'])->name('orders.parties.delete');
Route::post('/orders/parties/add/{order_id}', [OrderController::class, 'addInvolvedPartyForOrder'])->name('orders.parties.add.with_order');
Route::post('/orders/parties/{id}', [OrderController::class, 'getOrderInvolvedParties'])->name('orders.parties');
Route::post('/orders/getPartyDetailsListById', [OrderController::class, 'getPartyDetailsListById'])->name('orders.parties.details');

// Shippers
Route::post('/orders/shippers-list', [OrderController::class, 'getShipperListID'])->name('shippers.list');
Route::post('/orders/shippers-details', [OrderController::class, 'getShipperDetailsByID'])->name('shippers.details');
Route::post('/orders/shipper-save', [OrderController::class, 'saveShipper'])->name('shipper.save');

// Consignees
Route::post('/orders/consignees-details', [OrderController::class, 'getConsigneeDetailsListByID'])->name('consignees.details');

// =============================================================================
// ORDER CARGO MANAGEMENT
// =============================================================================

// Cargo Details
Route::get('/orders/cargodetails-listing', [OrderController::class, 'ajaxListing'])->name('cargodetails.listing');
Route::post('/orders/cargo-save', [OrderController::class, 'saveCargo'])->name('cargo.save');
Route::post('/order-cargo/delete', [OrderController::class, 'deleteOrderCargoDetails'])->name('order.cargo.delete');

// Items Management
Route::post('/orders/items-list', [OrderController::class, 'viewItemsList'])->name('items.list');
Route::post('/orders/items-details', [OrderController::class, 'getItemDetailsList'])->name('items.details');
Route::post('/orders/items-add', [OrderController::class, 'addItem'])->name('items.add');

// Inner Cargo
Route::post('/orders/inner-cargo/list', [OrderController::class, 'getInnerCargo'])->name('inner.cargo.list');
Route::post('/orders/inner-cargo/save/{id}', [OrderController::class, 'saveInnerCargo'])->name('inner.cargo.update');

// =============================================================================
// ORDER REVENUE & CHARGES MANAGEMENT
// =============================================================================

// Revenue Management
Route::post('/orders/revenues', [OrderRevenuesController::class, 'getOrderRevenueParties'])->name('orders.revenues');
Route::post('/orders/revenues-save/{revenue_id}', [OrderRevenuesController::class, 'saveRevenue'])->name('revenues.update');
Route::post('/orders/revenue-delete', [OrderRevenuesController::class, 'deleteOrderRevenue'])->name('orders.revenue.delete');

// Cost Management
Route::post('/orders/cost-parties', [OrderRevenuesController::class, 'getCostByOrder'])->name('orders.cost.parties');
Route::post('/orders/cost-revenues', [OrderRevenuesController::class, 'getAllCostRevenues'])->name('orders.cost.revenues');
Route::post('/orders/cost-add-by-calendar', [OrderRevenuesController::class, 'addCostByCalendar'])->name('cost.add-by-calendar');

// Charges Management
Route::post('/orders/charges-by-id', [OrderRevenuesController::class, 'getAllChargesById'])->name('charges.by-id');
Route::post('/orders/charges-for-revenue', [OrderRevenuesController::class, 'getChargesForRevenue'])->name('charges.for-revenue');
Route::post('/orders/charges-desc-vat', [OrderRevenuesController::class, 'getChargeDescAndVat'])->name('charges.desc-vat');
Route::post('/orders/charges-save/{charge_id}', [OrderRevenuesController::class, 'saveCharge'])->name('charges.update');

// VAS Management
Route::post('/orders/vas-details', [OrderRevenuesController::class, 'getOrderVasDetails'])->name('orders.vas-details');
Route::post('/orders/vas/add', [OrderRevenuesController::class, 'addVasOrderDetails'])->name('orders.vas.add');
Route::post('/orders/vas/delete', [OrderRevenuesController::class, 'deleteVasDetailsByOrder'])->name('orders.vas.delete');

// Rate & Calendar
Route::post('/orders/rate-calendar', [OrderRevenuesController::class, 'getRateCalendar'])->name('rate-calendar');
Route::post('/orders/bu-jfr-from-pq', [OrderRevenuesController::class, 'getBuJfrFromPq'])->name('orders.bu-jfr-from-pq');
Route::post('/orders/roles-type-list', [OrderRevenuesController::class, 'viewRoleTypeList'])->name('roles.type-list');
Route::post('/orders/vat-category-details', [OrderRevenuesController::class, 'getVatCategoryDetails'])->name('vat-category.details');

// Invoice & Financial
Route::post('/orders/make-selected-as-invoice', [OrderRevenuesController::class, 'makeSelectedAsInvoice']);
Route::post('/orders/validate-consolidation', [OrderRevenuesController::class, 'validateConsolidation']);

// =============================================================================
// ORDER REFERENCES MANAGEMENT
// =============================================================================

Route::post('/orders/other-reference-details', [OrderController::class, 'getOtherReferenceDetails'])->name('other-reference.details');
Route::post('/orders/add-reference-details', [OrderController::class, 'addReferenceDetails']);
Route::post('/orders/delete-order-reference-details', [OrderController::class, 'deleteOrderReferenceDetails']);

// =============================================================================
// ORDER TRIP & SHIPMENT MANAGEMENT
// =============================================================================

Route::post('/orders/trip-order-to-shipment', [OrderController::class, 'triporderintoshipment']);
Route::post('/trips-cancel/{orderId}', [TripController::class, 'cancel']);
Route::post('/orders/check-trip', [OrderController::class, 'checkTrip']);
Route::post('/orders/vehicle-details-by-carrier', [OrderRevenuesController::class, 'getVehicleDetailsByCarrierInfo']);
Route::get('/orders/get-drivers-by-vendor', [ShipmentPlanController::class, 'getdriversbyvendor']);
Route::post('/orders/driver/{id}', [OrderController::class, 'getDriverId']);

// =============================================================================
// ORDER MASTER DATA & VALIDATION
// =============================================================================

Route::post('/orders/order-status', [OrderController::class, 'getOrderStatus']);
Route::post('/orders/shipment-types', [OrderController::class, 'getShipmentTypes']);
Route::post('/orders/check-exceeded', [OrderRevenuesController::class, 'checkOrderExceededOrNot'])->name('orders.check-exceeded');
Route::post('/orders/allocation-rule-priority', [OrderController::class, 'allocationRulePriority']);

// =============================================================================
// ORDER CHARGES DISTRIBUTION
// =============================================================================

Route::post('/orders/charges-distribution', [ChargesDistributionController::class, 'index']);
Route::post('/orders/charges-distribution/apply-logic', [ChargesDistributionController::class, 'applyLogic']);

// =============================================================================
// ORDER IMPORT & EXPORT
// =============================================================================

Route::post('/orders/import-costs', [ExcelImportOrdersController::class, 'orderCost']);
Route::post('/orders/acon-edi-file-transfer', [SendMultipleAconEdiController::class, 'fileTransferForSelectedOrders']);

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index']);

// Fleet Management
Route::get('/fleetview', [FleetviewController::class, 'fleetview']);
Route::get('/fleet/vehicle-info', [FleetviewController::class, 'loadVehicleInfo']);

// Workbench routes
Route::get('/workbench', [WorkbenchController::class, 'index']);
Route::get('/workbench/data', [WorkbenchController::class, 'getWorkbenchData']);
Route::get('/workbench/stats', [WorkbenchController::class, 'getBookingStats']);

// Active Orders
Route::get('/activeorders', [ActiveOrdersController::class, 'activeorders']);
Route::post('/activeorders', [ActiveOrdersController::class, 'activeorders']);
Route::post('/activeorders/orderdetails', [ActiveOrdersController::class, 'orderdetails']);
Route::post('/activeorders/statusviewdetails', [ActiveOrdersController::class, 'statusviewdetails']);
Route::post('/activeorders/orddocsdetails', [ActiveOrdersController::class, 'orddocsdetails']);
Route::post('/activeorders/downloadCompletedOrder', [ActiveOrdersController::class, 'downloadCompletedOrder']);
Route::post('/activeorders/livetrackshipment', [ActiveOrdersController::class, 'livetrackshipment']);

//Pending Orders
Route::get('/pendingorders', [PendingOrdersController::class, 'pendingorders']);
Route::post('/pendingorders', [PendingOrdersController::class, 'pendingorders']);
Route::post('/pendingorders/orderdetails', [PendingOrdersController::class, 'orderdetails']);
Route::post('/pendingorders/orddocsdetails', [PendingOrdersController::class, 'orddocsdetails']);

// Completed Orders
Route::get('/completedorders', [CompletedOrdersController::class, 'doneorders']);
Route::post('/completedorders', [CompletedOrdersController::class, 'doneorders']);
Route::post('/completedorders/orderdetails', [CompletedOrdersController::class, 'orderdetails']);
Route::post('/completedorders/statusviewdetails', [CompletedOrdersController::class, 'statusviewdetails']);
Route::post('/completedorders/orddocsdetails', [CompletedOrdersController::class, 'orddocsdetails']);
Route::post('/completedorders/livetrackshipment', [CompletedOrdersController::class, 'livetrackshipment']);

//Profile
Route::get('/profile/{id}', [ProfileController::class, 'index']);
Route::post('/profile/update/{id}', [ProfileController::class, 'update']);
Route::get('/settings', [ProfileController::class, 'settings']);

// Master Data
Route::get('/party-types', [MasterController::class, 'partyTypes']);
Route::get('/countries', [MasterController::class, 'getCountries']);
Route::get('/txnactions', [MasterController::class, 'getTransactionActions']);
Route::get('/incoterms', [MasterController::class, 'getIncoTerms']);
Route::get('/products', [MasterController::class, 'getProducts']);
Route::get('/services', [MasterController::class, 'getServices']);
Route::get('/delivery-terms', [MasterController::class, 'getDeliveryTerms']);
Route::get('/claim-types', [MasterController::class, 'getClaimType']);
Route::get('/claim-priorities', [MasterController::class, 'getClaimPriority']);
Route::get('/claim-statuses', [MasterController::class, 'getClaimStatus']);
Route::get('/fuel-types', [MasterController::class, 'getFuelType']);
Route::get('/group-types', [MasterController::class, 'getGroupTypes']);
Route::get('/reference-types', [MasterController::class, 'getReferenceTypes']);

// Geofence routes
Route::get('/geofence/add', [GeofenceController::class, 'addgeofence']);
Route::get('/geofence/list', [GeofenceController::class, 'geofencelist']);
Route::post('/geofence/search', [GeofenceController::class, 'geofencelist']);
Route::post('/geofence/save', [GeofenceController::class, 'savegeofence']);
Route::get('/geofence/view/{id}', [GeofenceController::class, 'viewGeoFence']);
Route::delete('/geofence/delete/{id}', [GeofenceController::class, 'deletegeofence']);
Route::get('/geofence/assign-trips/{id}', [GeofenceController::class, 'assignTripsGeo']);
Route::get('/geofence/assign-vehicles/{id}', [GeofenceController::class, 'assignVehiclesGeo']);

// Return Trucks routes
Route::get('/return-trucks', [ReturnTrucksController::class, 'index'])->name('return-trucks.index');

// Rate Tier
Route::post('/rate-tiers', [RateTierController::class, 'index']);
Route::post('/rate-tiers/add', [RateTierController::class, 'add']);
Route::post('/rate-tiers/generate-drop-down', [RateTierController::class, 'generateDropDown']);
Route::post('/rate-tiers/check-name', [RateTierController::class, 'checkName']);
Route::post('/rate-tiers/insert', [RateTierController::class, 'tierInsert']);
Route::post('/rate-tiers/{id}/edit', [RateTierController::class, 'edit']);
Route::post('/rate-tiers/{id}/update', [RateTierController::class, 'update']);
Route::post('/rate-tiers/{id}/view', [RateTierController::class, 'view']);
Route::post('/rate-tiers/delete', [RateTierController::class, 'destroy']);

//Rate Preference
Route::post('/rate-preferences', [RatePreferenceController::class, 'index']);
Route::post('/rate-preferences/add', [RatePreferenceController::class, 'add']);
Route::post('/rate-preferences/offerings', [RatePreferenceController::class, 'getRateOfferings']);
Route::post('/rate-preferences/records', [RatePreferenceController::class, 'getRateRecords']);
Route::post('/rate-preferences/services', [RatePreferenceController::class, 'selectRateServiceOnTariffType']);
Route::post('/rate-preferences/source', [RatePreferenceController::class, 'saveSource']);
Route::post('/rate-preferences/source-list', [RatePreferenceController::class, 'showSourceList']);
Route::post('/rate-preferences/destination', [RatePreferenceController::class, 'saveDestination']);
Route::post('/rate-preferences/destination-list', [RatePreferenceController::class, 'showDestinationList']);
Route::post('/rate-preferences/reference-name', [RatePreferenceController::class, 'getReferenceName']);
Route::post('/rate-preferences/reference-type', [RatePreferenceController::class, 'saveReferenceType']);
Route::post('/rate-preferences/reference-type-list', [RatePreferenceController::class, 'showReferenceTypeList']);
Route::post('/rate-preferences/order-type-name', [RatePreferenceController::class, 'getOrderTypeName']);
Route::post('/rate-preferences/order-type', [RatePreferenceController::class, 'saveOrderType']);
Route::post('/rate-preferences/order-type-list', [RatePreferenceController::class, 'showOrderTypeList']);
Route::post('/rate-preferences/service-master-name', [RatePreferenceController::class, 'getServiceMasterName']);
Route::post('/rate-preferences/service', [RatePreferenceController::class, 'saveService']);
Route::post('/rate-preferences/service-list', [RatePreferenceController::class, 'showServiceList']);
Route::post('/rate-preferences/product-master-name', [RatePreferenceController::class, 'getProductMasterName']);
Route::post('/rate-preferences/product', [RatePreferenceController::class, 'saveProduct']);
Route::post('/rate-preferences/product-list', [RatePreferenceController::class, 'getProductList']);
Route::match(['get', 'post'], '/rate-preferences/edit/{id}', [RatePreferenceController::class, 'edit']);
Route::post('/rate-preferences/view/{id}', [RatePreferenceController::class, 'view']);
Route::post('/rate-preferences/delete/{id}', [RatePreferenceController::class, 'deleteRatePreference']);
Route::post('/rate-preferences/delete-preference-details', [RatePreferenceController::class, 'deletePreferenceDetails']);
Route::post('/rate-preferences/delete-destination', [RatePreferenceController::class, 'deleteDestination']);
Route::post('/rate-preferences/delete-ref-type', [RatePreferenceController::class, 'deleteRefType']);
Route::post('/rate-preferences/delete-order-type', [RatePreferenceController::class, 'deleteOrderType']);
Route::post('/rate-preferences/delete-service', [RatePreferenceController::class, 'deleteService']);
Route::post('/rate-preferences/delete-product', [RatePreferenceController::class, 'deleteProduct']);

// Geo Tier
Route::post('/geo-tiers', [GeoTierController::class, 'index']);
Route::post('/geo-tiers/add', [GeoTierController::class, 'add']);
Route::post('/geo-tiers/generate-drop-down', [GeoTierController::class, 'generateDropDown']);
Route::post('/geo-tiers/min-max-records-count', [GeoTierController::class, 'getMinMaxRecordsCount']);
Route::post('/geo-tiers/check-name', [GeoTierController::class, 'checkName']);
Route::post('/geo-tiers/insert', [GeoTierController::class, 'insertTier']);
Route::post('/geo-tiers/update', [GeoTierController::class, 'updateTier']);
Route::get('/geo-tiers/view/{id}', [GeoTierController::class, 'view']);
Route::post('/geo-tiers/delete/{id}', [GeoTierController::class, 'delete']);

// Mass Status Update
Route::get('/massstatus/orders', [MassStatusUpdateController::class, 'getOrdersWithStatus']);
Route::post('/massstatus/update-status', [MassStatusUpdateController::class, 'updateOrderStatus']);
Route::post('/massstatus/order-docs', [MassStatusUpdateController::class, 'orddocsdetails']);
Route::post('/massstatus/upload-document', [MassStatusUpdateController::class, 'uploadDocument']);

// XBorder Orders
Route::get('/xborder-orders/list', [XBorderOrdersController::class, 'orderslist']);
Route::post('/xborder-orders/search', [XBorderOrdersController::class, 'orderslist']);
Route::get('/xborder-orders/get-delivery-terms', [XBorderOrdersController::class, 'getDeliveryTerms']);
Route::get('/xborder-orders/copy-orders/{id}', [XBorderOrdersController::class, 'copyOrder']);
Route::get('/xborder-orders/reverse-orders/{id}', [XBorderOrdersController::class, 'reverseOrder']);
Route::get('/xborder-orders/edit-orders/{id}', [XBorderOrdersController::class, 'editOrder']);
Route::get('/xborder-orders/view-orders/{id}', [XBorderOrdersController::class, 'viewOrder']);
Route::post('/xborder-orders/update-orders', [XBorderOrdersController::class, 'updateOrder']);

// Document Control
Route::get('/document-control', [DocumentControlController::class, 'index']);
Route::get('/document-control/{id}', [DocumentControlController::class, 'index']);
Route::post('/document-control', [DocumentControlController::class, 'doclist']);
Route::post('/document-control/update-status', [DocumentControlController::class, 'updateOrderStatus']);
Route::post('/document-control/bulk-update-status', [DocumentControlController::class, 'bulkUpdateOrderStatus']);

// Palletizer routes
Route::get('/palletizer', [PalletizerController::class, 'index']);
Route::get('/palletizer/pallet-types', [PalletizerController::class, 'getPalletTypes']);
Route::post('/palletizer/search-by-container', [PalletizerController::class, 'searchByContainer']);
Route::post('/palletizer/search-by-order-id', [PalletizerController::class, 'searchByOrderId']);

//------------------------------------------------------------------------------------------------
// CUSTOMER PORTAL ROUTES
//------------------------------------------------------------------------------------------------

// Customer Address Book Routes
Route::get('/customer-addresses', [CustomerAddressController::class, 'index'])->name('customer-addresses.index');
Route::post('/customer-addresses', [CustomerAddressController::class, 'store'])->name('customer-addresses.store');
Route::get('/customer-addresses/{id}', [CustomerAddressController::class, 'show'])->name('customer-addresses.show');
Route::put('/customer-addresses/{id}', [CustomerAddressController::class, 'update'])->name('customer-addresses.update');
Route::delete('/customer-addresses/{id}', [CustomerAddressController::class, 'destroy'])->name('customer-addresses.destroy');

// Additional customer address routes
Route::post('/customer-addresses/fetch-provinces', [CustomerAddressController::class, 'fetchProvinces'])->name('customer-addresses.fetch-provinces');
Route::post('/customer-addresses/fetch-cities', [CustomerAddressController::class, 'fetchCities'])->name('customer-addresses.fetch-cities');
Route::post('/customer-addresses/fetch-pin-codes', [CustomerAddressController::class, 'fetchPinCodes'])->name('customer-addresses.fetch-pin-codes');
Route::post('/customer-addresses/check-name', [CustomerAddressController::class, 'checkName'])->name('customer-addresses.check-name');
Route::get('/customer-addresses/countries', [CustomerAddressController::class, 'getCountries'])->name('customer-addresses.countries');


// Customer Global Search Routes
Route::post('/customer-global-search', [CustomerGlobalSearchController::class, 'orderGlobalSearch'])->name('customer.global-search');
Route::get('/customer-global-search/order-types', [CustomerGlobalSearchController::class, 'getOrderTypes'])->name('customer.global-search.order-types');
Route::get('/customer-global-search/status-options', [CustomerGlobalSearchController::class, 'getOrderStatusOptions'])->name('customer.global-search.status-options');
Route::get('/customer-global-search/reference-types', [CustomerGlobalSearchController::class, 'getReferenceTypes'])->name('customer.global-search.reference-types');

// Add Customer Global Search Routes
Route::post('/customer-global-search/status-view-details', [CustomerGlobalSearchController::class, 'statusViewDetails'])->name('customer.global-search.status-view-details');
Route::post('/customer-global-search/order-documents-details', [CustomerGlobalSearchController::class, 'orderDocumentsDetails'])->name('customer.global-search.order-documents-details');

// eBooking Routes
Route::get('/customer-orders', [CustomerOrdersController::class, 'index'])->name('customer.orders.index');
Route::post('/customer-orders/edit-order', [CustomerOrdersController::class, 'editOrderInPopup'])->name('customer.orders.edit-order');
Route::post('/customer-orders/view-order/{id?}', [CustomerOrdersController::class, 'viewOrder'])->name('customer.orders.view-order');

// Customer Dashboard Routes
Route::get('/customer-dashboard', [CustomerDashboardController::class, 'index'])->name('customer.dashboard.index');
Route::post('/customer-dashboard/active-trips', [CustomerDashboardController::class, 'getActiveTrips'])->name('customer.dashboard.active-trips');
Route::post('/customer-dashboard/map-vehicles', [CustomerDashboardController::class, 'loadMapVehicles'])->name('customer.dashboard.map-vehicles');
Route::post('/customer-dashboard/pie-chart', [CustomerDashboardController::class, 'getPieChartInfo'])->name('customer.dashboard.pie-chart');
Route::post('/customer-dashboard/gauge-chart', [CustomerDashboardController::class, 'getBandsGaugeChartDetails'])->name('customer.dashboard.gauge-chart');

// Customer Fleet View
Route::get('/customer/fleetview', [CustomerFleetviewController::class, 'fleetview']);

// Customer Workbench routes
Route::get('/customer-workbench', [CustomerWorkbenchController::class, 'index']);


//------------------------------------------------------------------------------------------------
//------------------------------------------------------------------------------------------------
// Add to routes/api.php
Route::get('/debug-routes', function () {
    $routes = collect(Route::getRoutes())->filter(function ($route) {
        return $route->getName() === 'parties.list';
    })->map(function ($route) {
        return [
            'uri' => $route->uri(),
            'name' => $route->getName(),
            'action' => $route->getActionName(),
        ];
    });
    return response()->json($routes);
});
// Driver App APIs
Route::get('/driver-index', [DriverApiController::class, 'index']);
Route::post('/driver-login', [DriverApiController::class, 'driverLogin']);
Route::post('/get-shipments', [DriverApiController::class, 'getShipments']);
Route::post('/start-multiple-shipments', [DriverApiController::class, 'startMultipleShipments']);
Route::post('/get-shipment-stops', [DriverApiController::class, 'getShipmentStops']);
Route::post('/get-ship-statuses', [DriverApiController::class, 'getShipStatuses']);
Route::post('/close-trip', [DriverApiController::class, 'closeTrip']);
Route::post('/set-ship-abort', [DriverApiController::class, 'setShipAbort']);
Route::post('/reschedule-shift', [DriverApiController::class, 'rescheduleShift']);
Route::post('/set-ship-reject', [DriverApiController::class, 'setShipReject']);
Route::post('/parties', [DriverApiController::class, 'parties']);
Route::post('/driver-collection', [DriverApiController::class, 'driverCollection']);
Route::post('/get-shipment-documents', [DriverApiController::class, 'getShipmentDocuments']);
Route::post('/update-password', [DriverApiController::class, 'updatePassword']);
Route::post('/set-ship-stop-status', [DriverApiController::class, 'setShipStopStatus']);
Route::post('/get-expense-types', [DriverApiController::class, 'getExpenseTypes']);
Route::post('/get-driver-expense-data', [DriverApiController::class, 'getDriverExpenseData']);
Route::post('/get-vehicle-inspections-data-by-trip-id', [DriverApiController::class, 'getVehicleInspectionsDataByTripId']);
Route::post('/update-driver-expenses', [DriverApiController::class, 'updateDriverExpenses']);
Route::post('/add-driver-expenses', [DriverApiController::class, 'addDriverExpenses']);
Route::get('/get-inspection-types', [DriverApiController::class, 'getInspectionTypes']);
Route::post('/set-trip-vehicle-inspections', [DriverApiController::class, 'setTripVehicleInspections']);
Route::post('/driver-checkin', [DriverApiController::class, 'driverCheckIn']);
Route::post('/get-driver-daily-logs', [DriverApiController::class, 'getDriverDailyLogs']);
Route::post('/get-accept-trips', [DriverApiController::class, 'getAcceptTrips']);
Route::post('/get-checkin-out-status', [DriverApiController::class, 'getCheckInOutStatus']);
Route::post('/check-odometer-readings', [DriverApiController::class, 'checkOdometerReadings']);
Route::post('/get-company-code', [DriverApiController::class, 'getCompanyCode']);

Route::post('/crossborder-change', [DriverApiController::class, 'crossborderChange']);
Route::post('/get-driver-data', [DriverApiController::class, 'getDriverData']);
