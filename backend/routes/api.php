<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BillingController;
use App\Http\Controllers\TrafficCodeController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\VehicleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RedisExampleController;
use App\Http\Controllers\TripTemplateController;
use App\Http\Controllers\RoutingController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\VatMasterController;
use App\Http\Controllers\RateServiceController;
use App\Http\Controllers\StatusMasterController;
use App\Http\Controllers\LanesMasterController;
use App\Http\Controllers\DriverController;
use App\Http\Controllers\RateOfferingController;
use App\Http\Controllers\RateRecordController;
use App\Http\Controllers\VehicleProfileController;
use App\Http\Controllers\MasterController;
use App\Http\Controllers\CustomerProfileController;
use App\Http\Controllers\VendorProfileController;
use App\Http\Controllers\NotifymgmtController;
use App\Http\Controllers\TenderController;
use App\Http\Controllers\ShipmentPlanController;
use App\Jobs\RabbitmqHelloWorldJob;
use App\Http\Controllers\CarrierRatesController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CustomersController;
use App\Http\Controllers\CarriersController;
use App\Http\Controllers\PartiesController;
use App\Http\Controllers\VisibilityController;
use App\Http\Controllers\FleetviewController;
use App\Http\Controllers\WorkbenchController;
use App\Http\Controllers\ActiveOrdersController;
use App\Http\Controllers\PendingOrdersController;
use App\Http\Controllers\CompletedOrdersController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\OrderRevenuesController;
use App\Http\Controllers\TripExpenseController;
use App\Http\Controllers\GeofenceController;
use App\Http\Controllers\TripController;
use App\Http\Controllers\ReturnTrucksController;
use App\Http\Controllers\ExcelImportOrdersController;
use App\Http\Controllers\CabReportsController;
use App\Http\Controllers\ChargesDistributionController;
use App\Http\Controllers\SendMultipleAconEdiController;

Route::get('/ping', function () {
    return response()->json(['pong' => true]);
});


Route::get('/test-queue1', function () {
    RabbitmqHelloWorldJob::dispatch('Sent to queue1')->onQueue('queue1');
    return 'Dispatched to queue1!';
});

Route::get('/test-queue2', function () {
    RabbitmqHelloWorldJob::dispatch('Sent to queue2')->onQueue('queue2');
    return 'Dispatched to queue2!';
});

Route::post('register', [AuthController::class, 'register']); // Registration disabled
Route::post('login', [AuthController::class, 'login']);
Route::post('refresh', [AuthController::class, 'refresh'])->middleware('throttle:10,1');
Route::post('forgotpassword', [UserController::class, 'forgotPassword']);
Route::post('resetpassword', [UserController::class, 'resetPassword']);

// TODO: Uncomment for production - Authentication bypassed for testing
Route::middleware('auth:api')->group(function () {
    Route::post('logout', [AuthController::class, 'logout']);
    Route::get('me', [AuthController::class, 'me']);

    Route::get('shipmentTypes', [ShipmentController::class, 'shipmentTypeIndex']);
    Route::post('shipmentTypes/add', [ShipmentController::class, 'shipmentTypeAdd']);
    Route::post('shipmentTypes', [ShipmentController::class, 'shipmentTypeCreate']);
    Route::get('shipmentTypes/{id}', [ShipmentController::class, 'shipmentTypeShow']);
    Route::post('shipmentTypes/edit/{id}', [ShipmentController::class, 'shipmentTypeEdit']);
    Route::put('shipmentTypes/{id}', [ShipmentController::class, 'shipmentTypeUpdate']);
    Route::delete('shipmentTypes/{id}', [ShipmentController::class, 'shipmentTypeDestroy']);

    Route::get('truckTypes', [VehicleController::class, 'vehicleTypeIndex']);
    Route::get('truckTypes/{id}', [VehicleController::class, 'vehicleTypeShow']);
    Route::get('truckTypes/add', [VehicleController::class, 'vehicleTypeAdd']);
    Route::get('truckTypes/edit/{id}', [VehicleController::class, 'vehicleTypeEdit']);
    Route::post('truckTypes', [VehicleController::class, 'vehicleTypeCreate']);
    Route::put('truckTypes/{id}', [VehicleController::class, 'vehicleTypeUpdate']);
    Route::delete('truckTypes/{id}', [VehicleController::class, 'vehicleTypeDestroy']);

    Route::get('orderTypes', [OrderController::class, 'orderTypeIndex']);
    Route::get('orderTypes/{id}', [OrderController::class, 'orderTypeShow']);
    Route::get('orderTypes/add', [OrderController::class, 'orderTypeAdd']);
    Route::get('orderTypes/edit/{id}', [OrderController::class, 'orderTypeEdit']);
    Route::post('orderTypes', [OrderController::class, 'orderTypeCreate']);
    Route::put('orderTypes/{id}', [OrderController::class, 'orderTypeUpdate']);
    Route::delete('orderTypes/{id}', [OrderController::class, 'orderTypeDestroy']);

    Route::get('regions', [LocationController::class, 'regionIndex']);
    Route::get('regions/{id}', [LocationController::class, 'regionShow']);
    Route::get('regions/add', [LocationController::class, 'regionAdd']);
    Route::get('regions/edit/{id}', [LocationController::class, 'regionEdit']);
    Route::post('regions', [LocationController::class, 'regionStore']);
    Route::put('regions/{id}', [LocationController::class, 'regionUpdate']);
    Route::delete('regions/{id}', [LocationController::class, 'regionDestroy']);

    Route::get('vehicles', [VehicleController::class, 'index']);
    Route::get('vehicles/add', [VehicleController::class, 'add']);
    Route::post('vehicles', [VehicleController::class, 'store']);
    Route::get('vehicles/{id}', [VehicleController::class, 'show']);
    Route::get('vehicles/edit/{id}', [VehicleController::class, 'edit']);
    Route::put('vehicles/{id}', [VehicleController::class, 'update']);
    Route::delete('vehicles/{id}', [VehicleController::class, 'destroy']);

    Route::get('drivers', [DriverController::class, 'index']);
    Route::get('drivers/add', [DriverController::class, 'add']);
    Route::post('drivers', [DriverController::class, 'create']);
    Route::get('drivers/{id}', [DriverController::class, 'show']);
    Route::get('drivers/edit/{id}', [DriverController::class, 'edit']);
    Route::put('drivers/{id}', [DriverController::class, 'update']);
    Route::delete('drivers/{id}', [DriverController::class, 'delete']);

    Route::get('businessPartners', [MasterController::class, 'partners']);
    Route::get('businessPartners/{id}', [MasterController::class, 'partnerShow']);
    Route::post('businessPartners/add', [MasterController::class, 'partnerAdd']);
    Route::post('businessPartners/edit/{id}', [MasterController::class, 'partnerEdit']);
    Route::post('businessPartners', [MasterController::class, 'partnerCreate']);
    Route::put('businessPartners/{id}', [MasterController::class, 'partnerUpdate']);
    Route::delete('businessPartners/{id}', [MasterController::class, 'partnerDestroy']);

    Route::get('shipmentplan', [ShipmentPlanController::class, 'index']);
    Route::post('shipmentplan/filterOrders', [ShipmentPlanController::class, 'filterOrders']);
    Route::post('shipmentplan/filterShipments', [ShipmentPlanController::class, 'filterShipments']);
    Route::post('shipmentplan/filterIntransitShipments', [ShipmentPlanController::class, 'filterIntransitShipments']);
    Route::post('shipmentplan/stopdetail', [ShipmentPlanController::class, 'stopdetail']);
    Route::post('shipmentplan/getorderdetail', [ShipmentPlanController::class, 'getorderdetail']);
    Route::post('shipmentplan/createorder', [ShipmentPlanController::class, 'createorder']);
    Route::get('shipmentplan/addshipment', [ShipmentPlanController::class, 'addshipment']);
    Route::post('shipmentplan/editShipment', [ShipmentPlanController::class, 'editShipment']);
    Route::get('shipmentplan/getdriversbyvendor', [ShipmentPlanController::class, 'getdriversbyvendor']);
    Route::post('shipmentplan/storeshipment', [ShipmentPlanController::class, 'storeshipment']);
    Route::post('shipmentplan/storeOrder', [ShipmentPlanController::class, 'storeorder']);
    Route::post('shipmentplan/orderIntoShipment', [ShipmentPlanController::class, 'orderIntoShipment']);
    Route::post('shipmentplan/linesIntoShipment', [ShipmentPlanController::class, 'linesIntoShipment']);
    Route::post('shipmentplan/freightTracker', [ShipmentPlanController::class, 'freightTracker']);
    Route::post('shipmentplan/deallocateOrder', [ShipmentPlanController::class, 'deallocateOrder']);
    Route::get('shipmentplan/shipmentFreightTracker/{shpid}', [ShipmentPlanController::class, 'shipmentFreightTracker']);
    
    Route::prefix('tripexpense')->group(function () {
        Route::get('/', [TripExpenseController::class, 'index']);
        Route::get('/trucks', [TripExpenseController::class, 'getTrucks']);
        Route::get('/expenses', [TripExpenseController::class, 'getExpenses']);
        Route::get('/{id}', [TripExpenseController::class, 'view']);
        Route::get('/{id}/edit', [TripExpenseController::class, 'edit']);
        Route::post('/', [TripExpenseController::class, 'store']);
        Route::put('/{id}', [TripExpenseController::class, 'update']);
        Route::delete('/{id}', [TripExpenseController::class, 'delete']);
        Route::post('/addcategory', [TripExpenseController::class, 'addcategory']);
        Route::post('/uploadexpences', [TripExpenseController::class, 'uploadexpences']);
    });

    Route::prefix('cabreports')->group(function () {
        Route::get('/slareports', [CabReportsController::class, 'slareports']);
        Route::get('/shiftreports', [CabReportsController::class, 'shiftreports']);
    });
});

// TODO: Uncomment for production - Authentication bypassed for testing
Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/health', function () {
    return response()->json(['status' => 'OK'], 200);
});

// TODO: Uncomment for production - Authentication bypassed for testing
Route::middleware('auth:api')->get('/users', [UserController::class, 'index']);

Route::group(['prefix' => 'billing', 'middleware' => 'auth:api'], function () {
    Route::post('/add', [BillingController::class, 'add']);
});
Route::get('/users', [UserController::class, 'index']);

Route::group(['prefix' => 'billing'], function () {
    Route::post('/add', [BillingController::class, 'add']);
});

Route::middleware('auth:api')->group(function () {
    //Traffic codes
    Route::get('/traffic-codes', [TrafficCodeController::class, 'index'])->name('traffic-codes.index');
    Route::get('/traffic-codes/create', [TrafficCodeController::class, 'create'])->name('traffic-codes.create');
    Route::post('/traffic-codes/insert', [TrafficCodeController::class, 'insert'])->name('traffic-codes.insert');
    Route::get('/traffic-codes/edit/{id}', [TrafficCodeController::class, 'edit'])->name('traffic-codes.edit');
    Route::put('/traffic-codes/{id}', [TrafficCodeController::class, 'update'])->name('traffic-codes.update');
    Route::delete('/traffic-codes/{id}', [TrafficCodeController::class, 'destroy'])->name('traffic-codes.delete');
    Route::get('/traffic-codes/show/{id}', [TrafficCodeController::class, 'show'])->name('traffic-codes.show');

    //Trip templates
    Route::get('/triptemplates', [TripTemplateController::class, 'triptemplateindex'])->name('triptemplate.index');
    Route::get('/triptemplates/add', [TripTemplateController::class, 'addTemplate'])->name('triptemplate.addTemplate');
    Route::get('/triptemplates/view/{id}', [TripTemplateController::class, 'viewTemplate'])->name('triptemplate.view');
    Route::get('/triptemplates/edit/{id}', [TripTemplateController::class, 'editTemplate'])->name('triptemplate.edit');
    Route::post('/triptemplates/insert', [TripTemplateController::class, 'insertTemplate'])->name('triptemplate.store');
    Route::post('/triptemplates/delete/{id}', [TripTemplateController::class, 'deleteTripTemplateById'])->name('triptemplate.destroy');
    Route::post('/triptemplates/update/{id}', [TripTemplateController::class, 'updateTripWithTripsTemplate'])->name('triptemplate.update-trip');
    Route::get('/triptemplates/master-data', [TripTemplateController::class, 'getMasterDataForTripTemplate'])->name('triptemplate.master-data');
    Route::get('/triptemplates/waypoint-parties', [TripTemplateController::class, 'getWaypointParties'])->name('triptemplate.waypoint-parties');

    //Routing
    Route::get('/routing', [RoutingController::class, 'routingindex'])->name('routing.index');
    Route::get('/routing/add', [RoutingController::class, 'addrouting'])->name('routing.add');
    Route::get('/routing/view/{id}', [RoutingController::class, 'viewrouting'])->name('routing.view');
    Route::get('/routing/edit/{id}', [RoutingController::class, 'editrouting'])->name('routing.edit');
    Route::post('/routing/save', [RoutingController::class, 'saverouting'])->name('routing.save');
    Route::post('/routing/update/{id}', [RoutingController::class, 'updaterouting'])->name('routing.update');
    Route::delete('/routing/{id}', [RoutingController::class, 'deleterouting'])->name('routing.delete');
    Route::get('/routing/items', [RoutingController::class, 'getItemListID'])->name('routing.items');
    Route::get('/routing/cargo', [RoutingController::class, 'viewCargoList'])->name('routing.cargo');
    Route::get('/routing/party-types', [RoutingController::class, 'getAllPartyTypeList'])->name('routing.party-types');

    // VatMaster
    Route::get('/vatmaster', [VatMasterController::class, 'index']);
    Route::get('/vatmaster/add', [VatMasterController::class, 'add']);
    Route::post('/vatmaster/role-types', [VatMasterController::class, 'viewRoleTypeList']);
    Route::post('/vatmaster/partner-details', [VatMasterController::class, 'getPartnerDetailsById']);
    Route::post('/vatmaster/save-lane', [VatMasterController::class, 'saveLane']);
    Route::get('/vatmaster/recent-lanes', [VatMasterController::class, 'getRecentLanes']);
    Route::post('/vatmaster/lane/{id}', [VatMasterController::class, 'getLaneById']);
    Route::post('/vatmaster/update-lane', [VatMasterController::class, 'updateLane']);
    Route::post('/vatmaster/charge-description', [VatMasterController::class, 'getChargeDescription']);
    Route::post('/vatmaster/save-lane-vat', [VatMasterController::class, 'saveLaneVat']);
    Route::post('/vatmaster/lane-vats', [VatMasterController::class, 'getLaneVats']);
    Route::post('/vatmaster/charge/{id}', [VatMasterController::class, 'getChargeById']);
    Route::post('/vatmaster/update-vat', [VatMasterController::class, 'updateVat']);
    Route::post('/vatmaster/delete-charge', [VatMasterController::class, 'deleteCharge']);
    Route::post('/vatmaster/delete-lane', [VatMasterController::class, 'deleteLane']);
    Route::post('/vatmaster/insert-vat', [VatMasterController::class, 'insertVatData']);
    Route::post('/vatmaster/get-lanes', [VatMasterController::class, 'getLanes']);
    Route::get('/vatmaster/edit-vat/{id}', [VatMasterController::class, 'editVat']);
    Route::post('/vatmaster/update-vat-data', [VatMasterController::class, 'updateVatData']);
    Route::get('/vatmaster/view-vat/{id}', [VatMasterController::class, 'viewVat']);
    Route::post('/vatmaster/delete-vat-master', [VatMasterController::class, 'deleteVatMaster']);

    // Rate-Service
    Route::match(['get', 'post'], '/rateservice/index', [RateServiceController::class, 'index']);
    Route::get('/rateservice/add', [RateServiceController::class, 'add']);
    Route::post('/rateservice/getLaneById', [RateServiceController::class, 'getLaneById']);
    Route::post('/rateservice/saveLane', [RateServiceController::class, 'saveLane']);
    Route::post('/rateservice/showLaneDetails', [RateServiceController::class, 'showLaneDetails']);
    Route::post('/rateservice/updateLane/{id}', [RateServiceController::class, 'updateLane']);
    Route::post('/rateservice/deleteLaneDetails', [RateServiceController::class, 'deleteLaneDetails']);
    Route::post('/rateservice/checkServiceName', [RateServiceController::class, 'checkName']);
    Route::post('/rateservice/insertService', [RateServiceController::class, 'insertService']);
    Route::get('/rateservice/edit/{id}', [RateServiceController::class, 'edit']);
    Route::post('/rateservice/editlanedetails', [RateServiceController::class, 'editLaneDetails']);
    Route::post('/rateservice/serviceupdate', [RateServiceController::class, 'serviceUpdate']);
    Route::post('/rateservice/delete', [RateServiceController::class, 'delete']);

    //status master
    Route::get('/statusmaster', [StatusMasterController::class, 'index']);
    Route::get('/statusmaster/add', [StatusMasterController::class, 'add']);
    Route::post('/statusmaster/store', [StatusMasterController::class, 'store']);
    Route::get('/statusmaster/edit/{id}', [StatusMasterController::class, 'edit']);
    Route::put('/statusmaster/update/{id}', [StatusMasterController::class, 'update']);
    Route::get('/statusmaster/view/{id}', [StatusMasterController::class, 'view']);
    Route::delete('/statusmaster/delete/{id}', [StatusMasterController::class, 'delete']);
    Route::post('/statusmaster/checkname', [StatusMasterController::class, 'checkName']);
    Route::post('/statusmaster/checkcode', [StatusMasterController::class, 'checkCode']);
    Route::post('/statusmaster/upload-excel', [StatusMasterController::class, 'uploadStatusMasterExcel']);
    Route::post('/statusmaster/deletestatus', [StatusMasterController::class, 'deleteStatus']);

    //Lanes Master
    Route::get('/lanes', [LanesMasterController::class, 'index']);
    Route::get('/lanes/add', [LanesMasterController::class, 'add']);
    Route::post('/lanes/store', [LanesMasterController::class, 'store']);
    Route::put('/lanes/update/{id}', [LanesMasterController::class, 'update']);
    Route::get('/lanes/edit/{id}', [LanesMasterController::class, 'edit']);
    Route::get('/lanes/view/{id}', [LanesMasterController::class, 'view']);
    Route::delete('/lanes/delete/{id}', [LanesMasterController::class, 'delete']);
    Route::post('/lanes/check-lane', [LanesMasterController::class, 'checkLane']);
    Route::post('/lanes/get-lane-id', [LanesMasterController::class, 'getLaneId']);


    Route::match(['get', 'post'], '/rateoffering', [RateOfferingController::class, 'index']);
    Route::match(['get', 'post'], '/rateoffering/add', [RateOfferingController::class, 'add']);
    Route::post('/rateoffering/conversionlist', [RateOfferingController::class, 'conversionlist']);
    Route::post('/rateoffering/getrateservicelanes', [RateOfferingController::class, 'getrateservicelanes']);
    Route::post('/rateoffering/getvehiclepflist', [RateOfferingController::class, 'getvehiclepflist']);
    Route::post('/rateoffering/getvendorpflist', [RateOfferingController::class, 'getvendorpflist']);
    Route::post('/rateoffering/getcargopflist', [RateOfferingController::class, 'getcargopflist']);
    Route::match(['get', 'post'], '/rateoffering/edit/{rowId}', [RateOfferingController::class, 'edit']);
    Route::post('/rateoffering/getdocs', [RateOfferingController::class, 'getdocs']);
    Route::post('/rateoffering/getremarktypes', [RateOfferingController::class, 'getremarktypes']);
    Route::post('/rateoffering/getservices', [RateOfferingController::class, 'getservices']);
    Route::post('/rateoffering/getvasname', [RateOfferingController::class, 'getvasname']);
    Route::post('/rateoffering/saveservice/{id?}', [RateOfferingController::class, 'saveservice']);
    Route::post('/rateoffering/deleteservice', [RateOfferingController::class, 'deleteservice']);
    Route::post('/rateoffering/getdoctypename', [RateOfferingController::class, 'getdoctypename']);
    Route::post('/rateoffering/savedoc/{id?}', [RateOfferingController::class, 'savedoc']);
    Route::get('/rateoffering/view/{id}', [RateOfferingController::class, 'view']);
    Route::post('/rateoffering/deleterateoffering', [RateOfferingController::class, 'deleterateoffering']);

    // Rate-Record
    Route::match(['get', 'post'], '/raterecord', [RateRecordController::class, 'index']);
    Route::match(['get', 'post'], '/raterecord/add', [RateRecordController::class, 'add']);
    Route::post('/raterecord/getrefval', [RateRecordController::class, 'getrefval']);
    Route::post('/raterecord/conditionssave/{id?}', [RateRecordController::class, 'conditionssave']);
    Route::post('/raterecord/showcondetails', [RateRecordController::class, 'showcondetails']);
    Route::post('/raterecord/deletecondetails', [RateRecordController::class, 'deletecondetails']);

    // Billing
    Route::get('/billing', [BillingController::class, 'index']);
    Route::post('/billing/add', [BillingController::class, 'newBill']);
    Route::post('/billing/viewRoleTypeList', [BillingController::class, 'viewRoleTypeList']);
    Route::post('/billing/getPartnerDetailsById', [BillingController::class, 'getPartnerDetailsById']);
    Route::post('/billing/billsfilter', [BillingController::class, 'billsfilter']);
    Route::post('/billing/insertbilldata', [BillingController::class, 'insertbilldata']);
    Route::post('/billing/getchargeswithorder', [BillingController::class, 'getchargeswithorder']);
    Route::post('/billing/getcharges', [BillingController::class, 'getcharges']);
    Route::get('/billing/edit/{id}', [BillingController::class, 'editbill']);
    Route::post('/billing/update', [BillingController::class, 'updatebill']);
    Route::get('/billing/view/{id}', [BillingController::class, 'viewbill']);
    Route::delete('/billing/delete/{id}', [BillingController::class, 'deletebill']);

    Route::get('/getcustomers', [CustomersController::class, 'getCustomers']);
    Route::get('/getcarriers', [CarriersController::class, 'getCarriers']);
    Route::get('/getparties', [PartiesController::class, 'getParties']);
    Route::get('/visibility/shipments', [VisibilityController::class, 'shipments']);
    Route::post('/visibility/assignvehicle', [VisibilityController::class, 'openAssignVehicle']);
    Route::post('/visibility/historyanddocuments', [VisibilityController::class, 'getHistoryAndDocuments']);

    Route::get('/getstructuresequences', [UserController::class, 'getStructureSequences']);
});

Route::get('/redis/set', [RedisExampleController::class, 'set']);
Route::get('/redis/get', [RedisExampleController::class, 'get']);

// Vehicle Profile
Route::get('/vehicleprofileindex', [VehicleProfileController::class, 'index']);
Route::get('/addvehicleprofile', [VehicleProfileController::class, 'add']);
Route::post('/savevehicleprofile', [VehicleProfileController::class, 'save']);
Route::post('/updatevehicleprofile/{id}', [VehicleProfileController::class, 'update']);
Route::get('/editvehicleprofile/{id}', [VehicleProfileController::class, 'edit']);
Route::get('/viewvehicleprofile/{id}', [VehicleProfileController::class, 'view']);
Route::delete('/deletevehicleprofile/{id}', [VehicleProfileController::class, 'delete']);
Route::get('/gettrucktypes', [VehicleProfileController::class, 'getTruckTypes']);
Route::get('/getprofileids', [VehicleProfileController::class, 'getProfileIds']);
Route::get('/checkvehiclename', [VehicleProfileController::class, 'checkVehicleName']);
Route::get('/getvehicleprofileid', [VehicleProfileController::class, 'getVehicleProfileId']);
Route::post('/savevehicletype/{id?}', [VehicleProfileController::class, 'saveVehicleType']);
Route::get('/showprofilelist', [VehicleProfileController::class, 'showProfileList']);
Route::delete('/deleteprofiledetails', [VehicleProfileController::class, 'deleteProfileDetails']);
Route::get('/getprofilelist', [VehicleProfileController::class, 'getProfileList']);

// Customer Profile
Route::get('/customer-profiles', [CustomerProfileController::class, 'index'])->name('customer-profiles.index');
Route::get('/customer-profiles/create', [CustomerProfileController::class, 'create'])->name('customer-profiles.create');
Route::post('/customer-profiles', [CustomerProfileController::class, 'store'])->name('customer-profiles.store');
Route::get('/customer-profiles/show/{id}', [CustomerProfileController::class, 'show'])->name('customer-profiles.show');
Route::get('/customer-profiles/edit/{id}', [CustomerProfileController::class, 'edit'])->name('customer-profiles.edit');
Route::put('/customer-profiles/{id}', [CustomerProfileController::class, 'update'])->name('customer-profiles.update');
Route::delete('/customer-profiles/{id}', [CustomerProfileController::class, 'destroy'])->name('customer-profiles.destroy');

// Customer Profile additional routes
Route::post('/customer-profiles/get-cust-profile-id', [CustomerProfileController::class, 'getCustProfileId']);
Route::post('/customer-profiles/get-party-master', [CustomerProfileController::class, 'getPartyMaster']);
Route::post('/customer-profiles/save-customer/{id?}', [CustomerProfileController::class, 'saveCustomer']);
Route::post('/customer-profiles/show-profile-list', [CustomerProfileController::class, 'showProfileList']);
Route::post('/customer-profiles/get-profile-list', [CustomerProfileController::class, 'getProfileList']);
Route::post('/customer-profiles/delete-profile-details', [CustomerProfileController::class, 'deleteProfileDetails']);
Route::get('/customer-profiles/get-cust-info/{id}', [CustomerProfileController::class, 'getCustInfo']);
Route::post('/customer-profiles/check-name', [CustomerProfileController::class, 'checkName']);

// Vendor Profile
Route::get('/vendor-profiles', [VendorProfileController::class, 'index'])->name('vendor-profiles.index');
Route::get('/vendor-profiles/create', [VendorProfileController::class, 'create'])->name('vendor-profiles.create');
Route::post('/vendor-profiles', [VendorProfileController::class, 'store'])->name('vendor-profiles.store');
Route::get('/vendor-profiles/show/{id}', [VendorProfileController::class, 'show'])->name('vendor-profiles.show');
Route::get('/vendor-profiles/edit/{id}', [VendorProfileController::class, 'edit'])->name('vendor-profiles.edit');
Route::put('/vendor-profiles/{id}', [VendorProfileController::class, 'update'])->name('vendor-profiles.update');
Route::delete('/vendor-profiles/{id}', [VendorProfileController::class, 'destroy'])->name('vendor-profiles.destroy');

// Vendor Profile additional routes
Route::post('/vendor-profiles/get-vendor-profile-id', [VendorProfileController::class, 'getVendorProfileId']);
Route::post('/vendor-profiles/get-party-master', [VendorProfileController::class, 'getPartyMaster']);
Route::post('/vendor-profiles/save-vendor/{id?}', [VendorProfileController::class, 'saveVendor']);
Route::post('/vendor-profiles/show-profile-list', [VendorProfileController::class, 'showProfileList']);
Route::post('/vendor-profiles/get-profile-list', [VendorProfileController::class, 'getProfileList']);
Route::post('/vendor-profiles/delete-profile-details', [VendorProfileController::class, 'deleteProfileDetails']);
Route::get('/vendor-profiles/get-vendor-info/{id}', [VendorProfileController::class, 'getVendorInfo']);
Route::post('/vendor-profiles/check-vendor-name', [VendorProfileController::class, 'checkVendorName']);

// Notification Management
Route::get('/notification-management', [NotifymgmtController::class, 'index'])->name('notification-management.index');
Route::get('/notification-management/create', [NotifymgmtController::class, 'create'])->name('notification-management.create');
Route::post('/notification-management', [NotifymgmtController::class, 'store'])->name('notification-management.store');
Route::get('/notification-management/show/{id}', [NotifymgmtController::class, 'show'])->name('notification-management.show');
Route::get('/notification-management/edit/{id}', [NotifymgmtController::class, 'edit'])->name('notification-management.edit');
Route::put('/notification-management/{id}', [NotifymgmtController::class, 'update'])->name('notification-management.update');
Route::delete('/notification-management/{id}', [NotifymgmtController::class, 'destroy'])->name('notification-management.destroy');

// Notification Management additional routes
Route::post('/notification-management/get-notify-data', [NotifymgmtController::class, 'getNotifyData']);
Route::post('/notification-management/check-combination', [NotifymgmtController::class, 'checkCombination']);
Route::post('/notification-management/get-customer-notify-data', [NotifymgmtController::class, 'getCustomerNotifyData']);
Route::post('/notification-management/get-notification-ids', [NotifymgmtController::class, 'getNotificationIds']);
Route::post('/notification-management/get-organizations', [NotifymgmtController::class, 'getOrganizations']);
Route::post('/notification-management/get-customers', [NotifymgmtController::class, 'getCustomers']);
Route::post('/notification-management/get-customers-list', [NotifymgmtController::class, 'getCustomersList']);
Route::post('/notification-management/get-business-entities', [NotifymgmtController::class, 'getBusinessEntities']);
Route::get('/notification-management/test', [NotifymgmtController::class, 'test']);
Route::get('/notification-management/tpl', [NotifymgmtController::class, 'tpl']);
Route::get('/notification-management/test-mail', [NotifymgmtController::class, 'testMail']);

// Tender routes
Route::get('/tenders', [TenderController::class, 'index'])->name('tenders.index');
Route::get('/tenders/create', [TenderController::class, 'create'])->name('tenders.create');
Route::post('/tenders', [TenderController::class, 'store'])->name('tenders.store');
Route::get('/tenders/{id}', [TenderController::class, 'show'])->name('tenders.show');
Route::get('/tenders/edit/{id}', [TenderController::class, 'edit'])->name('tenders.edit');
Route::put('/tenders/{id}', [TenderController::class, 'update'])->name('tenders.update');
Route::delete('/tenders/{id}', [TenderController::class, 'destroy'])->name('tenders.destroy');

// Carrier Rates
Route::get('/carrier-rates', [CarrierRatesController::class, 'index']);
Route::get('/carrier-rates/create', [CarrierRatesController::class, 'create']);
Route::post('/carrier-rates', [CarrierRatesController::class, 'store']);
Route::get('/carrier-rates/{id}', [CarrierRatesController::class, 'show']);
Route::get('/carrier-rates/edit/{id}', [CarrierRatesController::class, 'edit']);
Route::put('/carrier-rates/{id}', [CarrierRatesController::class, 'update']);
Route::delete('/carrier-rates/{id}', [CarrierRatesController::class, 'destroy']);
Route::post('/carrier-rates/upload-excel', [CarrierRatesController::class, 'uploadRateExcel']);
Route::post('/carrier-rates/admin-counter', [CarrierRatesController::class, 'admin_submit_complete_counter']);
Route::post('/carrier-rates/cargo-details/add', [CarrierRatesController::class, 'addnewcargodetails']);
Route::post('/carrier-rates/cargo-details/get', [CarrierRatesController::class, 'getnewcargodetails']);
Route::post('/carrier-rates/cargo-details/update', [CarrierRatesController::class, 'updatenewcargodetails']);
Route::post('/carrier-rates/tender-financial', [CarrierRatesController::class, 'addTenderOrderFinanicalDetails']);

Route::middleware('auth:api')->get('/menu', [UserController::class, 'menu']);

// Route::get('/orderslist/{id?}', [OrderController::class, 'index']);
Route::get('/orderslist', [OrderController::class, 'index']);
Route::get('/orders/new', [OrderController::class, 'neworder'])->name('orders.new');
Route::post('/orders/insertorder', [OrderController::class, 'insertorder'])->name('orders.store');
Route::get('/orders/edit/{id}', [OrderController::class, 'editOrder'])->name('orders.edit');
Route::post('/orders/check-exceeded', [OrderRevenuesController::class, 'checkOrderExceededOrNot'])->name('orders.check-exceeded');
Route::post('/orders/vas-details', [OrderRevenuesController::class, 'getOrderVasDetails'])->name('orders.vas-details');
Route::post('/orders/shippers-list', [OrderController::class, 'getShipperListID'])->name('shippers.list');
Route::post('/orders/shippers-details', [OrderController::class, 'getShipperDetailsByID'])->name('shippers.details');
Route::post('/orders/parties-list', [OrderController::class, 'viewPartyList'])->name('parties.list');
Route::post('/orders/consignees-details', [OrderController::class, 'getConsigneeDetailsListByID'])->name('consignees.details');
Route::post('/orders/update', [OrderController::class, 'updateOrder'])->name('orders.update');
Route::post('/orders/shipper-save', [OrderController::class, 'saveShipper'])->name('shipper.save');
Route::get('/orders/cargodetails-listing', [OrderController::class, 'ajaxListing'])->name('cargodetails.listing');
Route::post('/orders/cargo-save', [OrderController::class, 'saveCargo'])->name('cargo.save');
Route::post('/orders/items-list', [OrderController::class, 'viewItemsList'])->name('items.list');
Route::post('/orders/items-details', [OrderController::class, 'getItemDetailsList'])->name('items.details');
Route::post('/orders/inner-cargo/list', [OrderController::class, 'getInnerCargo'])->name('inner.cargo.list');
Route::post('/orders/items-add', [OrderController::class, 'addItem'])->name('items.add');
Route::post('/orders/inner-cargo/save/{id}', [OrderController::class, 'saveInnerCargo'])->name('inner.cargo.update');
Route::post('/order-cargo/delete', [OrderController::class, 'deleteOrderCargoDetails'])->name('order.cargo.delete');
Route::post('/orders/parties', [OrderController::class, 'getOrderInvolvedParties'])->name('orders.parties');
Route::post('/orders/getPartyDetailsListById', [OrderController::class, 'getPartyDetailsListById'])->name('parties.list');
Route::post('/orders/parties/add/{order_id}', [OrderController::class, 'addInvolvedPartyForOrder'])->name('orders.parties.add.with_order');
Route::post('/orders/parties/delete', [OrderController::class, 'deleteOrderPartyDetails'])->name('orders.parties.delete');
Route::post('/orders/vas/add', [OrderRevenuesController::class, 'addVasOrderDetails'])->name('orders.vas.add');
Route::post('/orders/vas/delete', [OrderRevenuesController::class, 'deleteVasDetailsByOrder'])->name('orders.vas.delete');
Route::post('/orders/revenues', [OrderRevenuesController::class, 'getOrderRevenueParties'])->name('orders.revenues');
Route::post('/orders/cost-parties', [OrderRevenuesController::class, 'getCostByOrder'])->name('orders.cost.parties');
Route::post('/orders/cost-revenues', [OrderRevenuesController::class, 'getAllCostRevenues'])->name('orders.cost.revenues');
Route::post('/orders/charges-by-id', [OrderRevenuesController::class, 'getAllChargesById'])->name('charges.by-id');
Route::post('/orders/bu-jfr-from-pq', [OrderRevenuesController::class, 'getBuJfrFromPq'])->name('orders.bu-jfr-from-pq');
Route::post('/orders/roles-type-list', [OrderRevenuesController::class, 'viewRoleTypeList'])->name('roles.type-list');
Route::post('/orders/revenues-save/{revenue_id}', [OrderRevenuesController::class, 'saveRevenue'])->name('revenues.update');
Route::post('/orders/charges-for-revenue', [OrderRevenuesController::class, 'getChargesForRevenue'])->name('charges.for-revenue');
Route::post('/orders/charges-desc-vat', [OrderRevenuesController::class, 'getChargeDescAndVat'])->name('charges.desc-vat');
Route::post('/orders/vat-category-details', [OrderRevenuesController::class, 'getVatCategoryDetails'])->name('vat-category.details');
Route::post('/orders/charges-save/{charge_id}', [OrderRevenuesController::class, 'saveCharge'])->name('charges.update');
Route::post('/orders/revenue-delete', [OrderRevenuesController::class, 'deleteOrderRevenue'])->name('orders.revenue.delete');
Route::post('/orders/rate-calendar', [OrderRevenuesController::class, 'getRateCalendar'])->name('rate-calendar');
Route::post('/orders/cost-add-by-calendar', [OrderRevenuesController::class, 'addCostByCalendar'])->name('cost.add-by-calendar');
Route::post('/orders/other-reference-details', [OrderController::class, 'getOtherReferenceDetails'])->name('other-reference.details');
Route::post('/orders/trip-order-to-shipment', [OrderController::class, 'triporderintoshipment']);// Trip Creation
Route::post('/orders/product-master-data', [OrderController::class, 'getProductMasterData']);
Route::post('/orders/order-status', [OrderController::class, 'getOrderStatus']);
Route::post('/orders/service-master', [OrderController::class, 'getServiceMasterData']);
Route::post('/orders/transport-mode', [OrderController::class, 'getTransportMode']);
Route::post('/orders/shipment-types', [OrderController::class, 'getShipmentTypes']);
Route::post('/orders/regions', [OrderController::class, 'getRegions']);
Route::get('/viewOrder/{id}', [OrderController::class, 'viewOrder']);
Route::get('/copy-orders/{id}', [OrderController::class, 'copyOrder']);
Route::get('/orders-cancel/{id}', [OrderController::class, 'cancelOrder']);
Route::get('/trips-cancel/{orderId}', [TripController::class, 'cancel']);
Route::post('/orders/check-trip', [OrderController::class, 'checkTrip']);
Route::get('/orders/allocation-rule-priority', [OrderController::class, 'allocationRulePriority']);
Route::post('/orders/vehicle-details-by-carrier', [OrderRevenuesController::class, 'getVehicleDetailsByCarrierInfo']);
Route::get('/orders/get-drivers-byv-endor', [ShipmentPlanController::class, 'getdriversbyvendor']);
Route::get('/orders/driver/{id}', [OrderController::class, 'getDriverId']);
Route::post('/orders/add-reference-details', [OrderController::class, 'addReferenceDetails']);
Route::post('/orders/delete-order-reference-details', [OrderController::class, 'deleteOrderReferenceDetails']);
Route::post('/orders/import-costs', [ExcelImportOrdersController::class, 'orderCost']);
Route::post('/orders/make-selected-as-invoice', [OrderRevenuesController::class, 'makeSelectedAsInvoice']);
Route::get('/orders/charges-distribution', [ChargesDistributionController::class, 'index']);
Route::post('/orders/charges-distribution/apply-logic', [ChargesDistributionController::class, 'applyLogic']);
Route::post('/orders/acon-edi-file-transfer', [SendMultipleAconEdiController::class, 'fileTransferForSelectedOrders']);
Route::post('/orders/validate-consolidation', [OrderRevenuesController::class, 'validateConsolidation']);

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'dashboard'])->name('dashboard.index');
Route::get('/dashboard/ajax', [DashboardController::class, 'ajaxdashboard'])->name('dashboard.ajax');
Route::get('/dashboard/mapview/{veh}', [DashboardController::class, 'mapview'])->name('dashboard.mapview');

// Fleet Management
Route::get('/fleetview', [FleetviewController::class, 'fleetview'])->name('fleet.fleetview');

// Workbench routes
Route::get('/workbench', [WorkbenchController::class, 'index']);
Route::get('/workbench/data', [WorkbenchController::class, 'getWorkbenchData']);
Route::get('/workbench/stats', [WorkbenchController::class, 'getBookingStats']);

// Active Orders
Route::get('/activeorders', [ActiveOrdersController::class, 'activeorders']);
Route::post('/activeorders', [ActiveOrdersController::class, 'activeorders']);
Route::post('/activeorders/orderdetails', [ActiveOrdersController::class, 'orderdetails']);
Route::post('/activeorders/statusviewdetails', [ActiveOrdersController::class, 'statusviewdetails']);
Route::post('/activeorders/orddocsdetails', [ActiveOrdersController::class, 'orddocsdetails']);
Route::post('/activeorders/downloadCompletedOrder', [ActiveOrdersController::class, 'downloadCompletedOrder']);
Route::post('/activeorders/livetrackshipment', [ActiveOrdersController::class, 'livetrackshipment']);

//Pending Orders
Route::get('/pendingorders', [PendingOrdersController::class, 'pendingorders']);
Route::post('/pendingorders', [PendingOrdersController::class, 'pendingorders']);
Route::post('/pendingorders/orderdetails', [PendingOrdersController::class, 'orderdetails']);
Route::post('/pendingorders/orddocsdetails', [PendingOrdersController::class, 'orddocsdetails']);

// Completed Orders
Route::get('/completedorders', [CompletedOrdersController::class, 'doneorders']);
Route::post('/completedorders', [CompletedOrdersController::class, 'doneorders']);
Route::post('/completedorders/orderdetails', [CompletedOrdersController::class, 'orderdetails']);
Route::post('/completedorders/statusviewdetails', [CompletedOrdersController::class, 'statusviewdetails']);
Route::post('/completedorders/orddocsdetails', [CompletedOrdersController::class, 'orddocsdetails']);
Route::post('/completedorders/livetrackshipment', [CompletedOrdersController::class, 'livetrackshipment']);

//Profile
Route::get('/profile/{id}', [ProfileController::class, 'index']);
Route::post('/profile/update/{id}', [ProfileController::class, 'update']);
Route::get('/country-codes', [ProfileController::class, 'countryCodes']);
Route::get('/settings', [ProfileController::class, 'settings']);

// Geofence routes
Route::get('/geofence/add', [GeofenceController::class, 'addgeofence']);
Route::get('/geofence/list', [GeofenceController::class, 'geofencelist']);
Route::post('/geofence/search', [GeofenceController::class, 'geofencelist']);
Route::post('/geofence/save', [GeofenceController::class, 'savegeofence']);
Route::get('/geofence/view/{id}', [GeofenceController::class, 'viewGeoFence']);
Route::delete('/geofence/delete/{id}', [GeofenceController::class, 'deletegeofence']);
Route::get('/geofence/assign-trips/{id}', [GeofenceController::class, 'assignTripsGeo']);
Route::get('/geofence/assign-vehicles/{id}', [GeofenceController::class, 'assignVehiclesGeo']);

// Return Trucks routes
Route::get('/return-trucks', [ReturnTrucksController::class, 'index'])->name('return-trucks.index');