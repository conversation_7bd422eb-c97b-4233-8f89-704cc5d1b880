<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class ReturnTruck extends Model
{
    protected $table = 'return_trucks';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'trip_id',
        'vehicle_id',
        'driver_id',
        'vendor_id',
        'customer_id',
        'shift_leg_id',
        'origin_id',
        'destination_id',
        'routetemplate_id',
        'zone_id',
        'stime',
        'etime',
        'splace',
        'slat',
        'slng',
        'eplace',
        'elat',
        'elng',
        'scity',
        'dcity',
        'empshift_start',
        'empshift_end',
        'trip_type',
        'startdate',
        'enddate',
        'shipment_name',
        'shipmentid',
        'shipment_id',
        'transport_mode',
        'domainname',
        'schedule_date',
        'vehicle_type',
        'user_id',
        'org_id',
        'be_value',
        'border_type',
        'carrier_instructions',
        'carrier_type',
        'txnid',
        'weight',
        'volume',
        'units',
        'interchange_control_reference',
        'weight_capacity',
        'volume_capacity',
        'additional_conditions',
        'temperature_regime',
        'time_for_loading_penality_rate',
        'is_carrier_notified',
        'aborted',
        'status',
    ];

    protected $casts = [
        'trip_type' => 'integer',
        'startdate' => 'timestamp',
        'enddate' => 'timestamp',
        'schedule_date' => 'timestamp',
        'weight' => 'decimal:10,2',
        'volume' => 'decimal:10,2',
        'units' => 'decimal:10,2',
        'border_type' => 'tinyInteger',
        'carrier_type' => 'tinyInteger',
        'aborted' => 'boolean',
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function trip()
    {
        return $this->belongsTo(Trip::class, 'trip_id');
    }

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class, 'vehicle_id');
    }

    public function driver()
    {
        return $this->belongsTo(Driver::class, 'driver_id');
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }
}