<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CarrierAllocationRule extends Model
{
    protected $table = 'carrier_allocation_rules';
    protected $primaryKey = 'id';
    protected $fillable = [
        'rule_name',
        'customer_id',
        'description',
        'shipment_type',
        'min_weight',
        'max_weight',
        'regions',
        'priority_type',
        'org_id',
        'be_value',
        'order_id',
        'status',
    ];
}
