<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SxUserPrevilegeAccessRule extends Model
{
    use SoftDeletes;

    protected $table = 'sx_user_previlege_access_rules';
    protected $primaryKey = 'id';
    public $timestamps = true;
    protected $dates = ['deleted_at'];

    protected $fillable = [
        'user_id',
        'previllege_id',
        'previllege_module_id',
        'feature_id',
        'created_by',
        'updated_by',
        'org_id',
        'add_access',
        'modify_access',
        'delete_access',
        'view_access',
        'status',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'previllege_id' => 'integer',
        'previllege_module_id' => 'integer',
        'feature_id' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'org_id' => 'integer',
        'add_access' => 'integer',
        'modify_access' => 'integer',
        'delete_access' => 'integer',
        'view_access' => 'integer',
        'status' => 'integer',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(SxUsers::class, 'user_id', 'id');
    }

    public function privilege()
    {
        return $this->belongsTo(SxPrevilleges::class, 'previllege_id', 'id');
    }

    public function privilegeModule()
    {
        return $this->belongsTo(PrivilegeModule::class, 'previllege_module_id', 'id');
    }

    public function feature()
    {
        return $this->belongsTo(ModuleFeature::class, 'feature_id', 'id');
    }

    public function organization()
    {
        return $this->belongsTo(SxOrganization::class, 'org_id', 'id');
    }

    // Scopes for filtering by access type
    public function scopeWithAddAccess($query)
    {
        return $query->where('add_access', 1);
    }

    public function scopeWithModifyAccess($query)
    {
        return $query->where('modify_access', 1);
    }

    public function scopeWithDeleteAccess($query)
    {
        return $query->where('delete_access', 1);
    }

    public function scopeWithViewAccess($query)
    {
        return $query->where('view_access', 1);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForOrganization($query, $orgId)
    {
        return $query->where('org_id', $orgId);
    }

    // Helper methods
    public function hasAddAccess()
    {
        return $this->add_access == 1;
    }

    public function hasModifyAccess()
    {
        return $this->modify_access == 1;
    }

    public function hasDeleteAccess()
    {
        return $this->delete_access == 1;
    }

    public function hasViewAccess()
    {
        return $this->view_access == 1;
    }

    public function isActive()
    {
        return $this->status == 1;
    }
}
