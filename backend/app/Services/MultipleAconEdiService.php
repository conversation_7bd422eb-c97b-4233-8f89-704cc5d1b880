<?php

namespace App\Services;

use App\Models\Revenue;
use App\Models\XborderCountry;
use Illuminate\Support\Facades\DB;

class MultipleAconEdiService
{
    public function checkMultipleOrdersInternalBu(array $ids): array
    {
        $response = [];

        if (!empty($ids)) {
            $revenues = Revenue::whereIn('order_id', $ids)
                ->where('recipient_role', 'LIKE', 'Internal BU')
                ->where('amount', '>', 0)
                ->where('status', '1')
                ->select('id', 'order_id', 'invoice_status')
                ->get();

            foreach ($revenues as $res) {
                $response[] = [
                    'order_id' => $res->order_id,
                    'rev_id' => $res->id,
                    'invoice_status' => $res->invoice_status,
                ];
            }
        }

        return $response;
    }

    public function getBuDetailsForRevenue(array $ids, string $where, int $orgId): array
    {
        $revenues = [];
        $xborderCode = XborderCountry::where('org_id', $orgId)
            ->value('xborder_code') ?? '';

        $query = Revenue::select([
            'revenues.id',
            'revenues.type',
            'revenues.order_id',
            'revenues.recipient_role',
            'revenues.recipient_code',
            'revenues.debtor_jfr',
            'revenues.bu_jfr',
            'revenues.invoice_number',
            'revenues.invoice_date',
            'revenues.amount',
            'revenues.currency',
            'revenues.exchange_rate',
            'revenues.foreign_currency',
            'revenues.remarks',
            'party_master.name',
            'party_master.code',
            'party_master.customeridentifier',
            'party_master.location_id',
            'party_master.country',
            'party_master.street',
            'party_master.pincode',
            'party_master.acon_debitor_code',
        ])
            ->leftJoin('party_master', function ($join) {
                $join->on('revenues.recipient_code', '=', 'party_master.code')
                    ->on('revenues.recipient_name', '=', 'party_master.name');
            })
            ->whereIn('revenues.id', $ids)
            ->whereRaw($where);

        if (!$xborderCode) {
            $query->where('party_master.org_id', $orgId);
        }

        $revenues = $query->groupBy('revenues.id')->get()->toArray();

        return $revenues;
    }

    public function updateMultipleRevs(array $revIds, array $data): bool
    {
        if (!empty($revIds) && !empty($data)) {
            return Revenue::whereIn('id', $revIds)->update($data) > 0;
        }
        return false;
    }

    public function checkRevenueExistsOrNot(array $ids): array
    {
        $norevOrders = $ids;
        $revOrders = [];

        $revenues = Revenue::whereIn('order_id', $ids)
            ->where('recipient_role', 'LIKE', 'Internal BU')
            ->where('amount', '>', 0)
            ->where('status', '1')
            ->select('order_id')
            ->distinct()
            ->pluck('order_id')
            ->toArray();

        $revOrders = $revenues;
        $norevOrders = array_diff($ids, $revOrders);

        return [
            'norev_orders' => $norevOrders,
            'rev_orders' => $revOrders,
        ];
    }
}
