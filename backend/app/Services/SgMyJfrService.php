<?php

namespace App\Services;

use App\Models\ShiporderStopSequence;

class SgMyJfrService
{
    public function checkActiveTripOrNot(array $ids): int
    {
        $status = 1;

        if (!empty($ids)) {
            $trips = ShiporderStopSequence::whereIn('shift_id', $ids)
                ->where('status', '1')
                ->select('trip_id')
                ->get();

            foreach ($trips as $row) {
                if ($row->trip_id <= 0) {
                    $status = 0;
                    break;
                }
            }
        }

        return $status;
    }
}
