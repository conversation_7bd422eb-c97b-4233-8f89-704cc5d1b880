<?php

namespace App\Services;

use App\Models\SxPartyMembers;
use App\Models\Revenue;
use App\Models\Charge;
use App\Models\RaterecordServices;
use App\Models\RatepreferRefTypes;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\VendorProfile;
use App\Models\RatepreferProduct;
use App\Models\RatepreferService;
use App\Models\RatepreferOrdertype;
use App\Models\RateOffering;
use App\Models\RateRecord;
use App\Models\RateService;
use App\Models\LanesMaster;
use App\Models\RateRecordCondition;
use App\Models\ReferenceMaster;
use App\Models\OrderReference;
use App\Models\ChargeCode;
use App\Models\RateOfferingService;
use App\Models\RaterecordCharges;
use App\Models\RateExchangeSheet;
use App\Models\TierMaster;
use App\Models\OrderCargoDetail;
use App\Models\SxuomConversion;
use App\Models\OrderVas;
use App\Models\VendorProfileList;
use App\Models\DistanceMaster;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Services\OrderList\OrderProcessor;


class RateManagement
{
    protected $orderProcessor;

    // public function __construct(OrderProcessor $orderProcessor)
    // {
    //     $this->orderProcessor = $orderProcessor;
    // }

    public function setOrderProcessor(OrderProcessor $orderProcessor)
    {
        $this->orderProcessor = $orderProcessor;
    }


    public function addrecodfortripinsertion($arr)
    {
        $user = Auth::user();
        $cdate = now()->toDateTimeString();
        $orgId = $user->org_id ?? $arr['org_id'] ?? 0;
        $userId = $user->id ?? $arr['user_id'] ?? 0;
        $orderService = $orderProduct = $serviceChk = $productChk = $orderOtype = $refcheck = $chkRef = $chkRateService = $rateServiceId = $rateOffering = $rateRecord = $modechk = 0;
        $vendorServices = $serviceRowIds = [];
        $orderMod = $offeringType = "";

        if (!empty($arr)) {
            $orderIds = $arr['ordid'] ?? "0";
            $orderIds = empty($orderIds) ? 0 : $orderIds;
            $vendorId = $arr['carrierid'] ?? "0";
            $vendorId = empty($vendorId) ? 0 : $vendorId;

            if ($orderIds != "0" && $vendorId != "0") {
                $getVendorCode = SxPartyMembers::where('id', $vendorId)->value('code');
                if ($getVendorCode) {
                    $vendorCode = $getVendorCode;
                    $getProfileId = $this->getvendorprofileforpreference($vendorCode, $userId);

                    if ($getProfileId->isNotEmpty()) {
                        foreach ($getProfileId as $profile) {
                            $vendProfileId = $profile->id;
                            if ($vendProfileId) {
                                $ordeschk = explode(",", $orderIds);
                                foreach ($ordeschk as $ord) {
                                    $modechk = 0;
                                    $orderMod = $offeringType = "";
                                    $getOrderMod = Order::where('id', $ord)->where('status', '!=', 0)->value('transport_mode');
                                    if ($getOrderMod) {
                                        $orderMod = $getOrderMod;
                                        if (empty($orderMod)) {
                                            $modechk = 1;
                                        }
                                        if (is_null($orderMod)) {
                                            $orderMod = "";
                                            $modechk = 1;
                                        }
                                    }
                                    $getOrderDetails = Order::where('id', $ord)
                                        ->where('status', '!=', 0)
                                        ->select('pickup_country', 'delivery_country', 'pickup_city', 'delivery_city', 'pickup_pincode', 'delivery_pincode', 'pickup_address2', 'delivery_address2', 'customer_id', 'product')
                                        ->first();
                                    if ($getOrderDetails) {
                                        $getServices = OrderDetail::where('order_row_id', $ord)
                                            ->where('status', 1)
                                            ->select('order_type', 'service')
                                            ->first();
                                        if ($getServices) {
                                            $orderService = $getServices->service;
                                            $orderOtype = $getServices->order_type;
                                        }
                                        $productName = $getOrderDetails->product;
                                        $customerId = $getOrderDetails->customer_id;
                                        if ($productName) {
                                            if (in_array($orgId, ['ZAKN', 'VNKN'], true)) {
                                                $getProductId = Product::where(['name' => $productName, 'status' => 1, 'org_id' => $orgId])->value('id');
                                                $orderProduct = $getProductId;
                                            } else {
                                                $getProductId = Product::where('name', $productName)
                                                    ->whereRaw("(org_id = '' OR org_id IS NULL)")
                                                    ->where('status', 1)
                                                    ->value('id');
                                                if ($getProductId) {
                                                    $orderProduct = $getProductId;
                                                }
                                            }
                                        }
                                        $pickup = [
                                            'country' => trim($getOrderDetails->pickup_country),
                                            'state' => trim($getOrderDetails->pickup_address2),
                                            'city' => trim($getOrderDetails->pickup_city),
                                            'pincode' => $getOrderDetails->pickup_pincode
                                        ];
                                        $delivery = [
                                            'country' => trim($getOrderDetails->delivery_country),
                                            'state' => trim($getOrderDetails->delivery_address2),
                                            'city' => trim($getOrderDetails->delivery_city),
                                            'pincode' => $getOrderDetails->delivery_pincode
                                        ];
                                        $preference = $this->getvendorpreferencebyorderdetails($vendProfileId, $userId, $pickup, $delivery, [], $arr['rateRecordId'] ?? "");
                                        foreach ($preference as $pre) {
                                            $preferenceId = $pre['id'];
                                            $rateServiceId = $pre['rate_service_id'];
                                            $rateOffering = $pre['rate_offering_id'];
                                            $rateRecordId = $pre['rate_record_id'];
                                            $autoBill = $pre['auto_bill'];
                                            $info = ['order_id' => $ord, 'product' => $productName];
                                            $vendorServices = $this->getvendorprofiledetailsbyid($vendorId, $orderService, $info);
                                            foreach ($vendorServices as $serv) {
                                                $serviceRowIds[] = $serv['service_row_id'];
                                            }
                                            $checkLanes = $this->getlaneids_byservice($rateServiceId, $ord, [
                                                'consignee_country' => $delivery['country'],
                                                'shipper_country' => $pickup['country'],
                                                'shipper_city' => $pickup['city'],
                                                'consignee_city' => $delivery['city'],
                                                'shipper_zipcode' => $pickup['pincode'],
                                                'consignee_zipcode' => $delivery['pincode']
                                            ]);
                                            $chkRateService = in_array($rateServiceId, $serviceRowIds) ? 1 : 0;
                                            if (empty($checkLanes)) {
                                                $chkRateService = 0;
                                            }
                                            if ($chkRateService == 1) {
                                                $refcheck = $this->checkrefandorcondition($ord, $rateRecordId);
                                                if ($orderProduct > 0) {
                                                    $productChk = 1;
                                                    $chkPreferProduct = RatepreferProduct::where(['rate_prefer_id' => $preferenceId, 'status' => '1'])->pluck('product_id')->toArray();
                                                    if ($chkPreferProduct) {
                                                        $productChk = in_array($orderProduct, $chkPreferProduct) ? 1 : 0;
                                                    }
                                                } else {
                                                    $productChk = 1;
                                                }
                                                if ($orderService > 0) {
                                                    $serviceChk = 1;
                                                    $chkPreferService = RatepreferService::where(['rate_prefer_id' => $preferenceId, 'status' => '1'])->pluck('service_id')->toArray();
                                                    if ($chkPreferService) {
                                                        $serviceChk = in_array($orderService, $chkPreferService) ? 1 : 0;
                                                    }
                                                } else {
                                                    $serviceChk = 1;
                                                }
                                                if ($orderOtype > 0) {
                                                    $ordertypeChk = 1;
                                                    $chkPreferOtype = RatepreferOrdertype::where(['rate_prefer_id' => $preferenceId, 'user_id' => $userId, 'status' => '1'])->pluck('order_type_id')->toArray();
                                                    if ($chkPreferOtype) {
                                                        $ordertypeChk = in_array($orderOtype, $chkPreferOtype) ? 1 : 0;
                                                    }
                                                } else {
                                                    $ordertypeChk = 1;
                                                }
                                                if ($rateOffering > 0) {
                                                    $getOfferingType = RateOffering::where(['id' => $rateOffering, 'status' => '1'])->select('offering_type', 'vendor_profile_id')->first();
                                                    if ($getOfferingType) {
                                                        $vendorProfileId = $getOfferingType->vendor_profile_id;
                                                        if ($vendorProfileId > 0) {
                                                            $checkProfile = VendorProfileList::where(['vp_id' => $vendorProfileId, 'profile_id' => $vendorCode, 'status' => '1'])->exists();
                                                            if (!$checkProfile) {
                                                                $serviceChk = 0;
                                                            }
                                                        }
                                                        $modechk = 0;
                                                        $offeringType = $getOfferingType->offering_type;
                                                        if (empty($offeringType)) {
                                                            $modechk = 1;
                                                        }
                                                        if (is_null($offeringType)) {
                                                            $modechk = 1;
                                                            $offeringType = "";
                                                        }
                                                        if ($modechk == 0 && !empty($offeringType) && !empty($orderMod)) {
                                                            $modechk = strtoupper($offeringType) == strtoupper($orderMod) ? 1 : 0;
                                                        }
                                                    }
                                                }
                                                $chkRef = $this->preference_checkrefandorcondition($ord, $preferenceId);
                                                if ($chkRef == '1' && $ordertypeChk == '1' && $serviceChk == '1' && $productChk == '1' && $refcheck == '1' && $modechk == '1') {
                                                    $data = [
                                                        'rate_record_id' => $rateRecordId,
                                                        'rate_offering' => $rateOffering,
                                                        'rate_autobill' => $autoBill,
                                                        'order_id' => $ord,
                                                        'vendor_code' => $vendorCode,
                                                        'rate_service_id' => $rateServiceId
                                                    ];
                                                    $this->add_buypreference($data);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public function getvendorprofileforpreference($code, $userId)
    {
        return VendorProfile::select('vp.id')
            ->from('vendor_profiles as vp')
            ->leftJoin('vendor_profile_lists as vpl', 'vpl.vp_id', '=', 'vp.id')
            ->where('vpl.profile_id', $code)
            ->where('vp.user_id', $userId)
            ->where('vp.status', 1)
            ->where('vpl.status', 1)
            ->get();
    }

    public function getvendorpreferencebyorderdetails($vendProfileId, $userId, $pickup, $delivery, $vendorProfiles = [], $rateRecord = "")
    {
        $whr = "";
        $result = [];
        if (!empty($pickup) && !empty($delivery)) {
            $whr = " (rps.country LIKE '" . $pickup['country'] . "' OR rps.state LIKE '" . $pickup['state'] . "' OR rps.city LIKE '" . $pickup['city'] . "' OR rps.pincode LIKE '" . $pickup['pincode'] . "') AND rps.user_id='" . $userId . "' AND rps.status='1' AND (rds.country LIKE '" . $delivery['country'] . "' OR rds.state LIKE '" . $delivery['state'] . "' OR rds.city LIKE '" . $delivery['city'] . "' OR rds.pincode LIKE '" . $delivery['pincode'] . "' ) AND rds.user_id ='" . $userId . "' AND rds.status='1' AND rp.status='1'";
        }
        $rateRecordRowId = 0;
        if ($whr != "") {
            if ($rateRecord != "") {
                $getRateId = DB::selectOne("SELECT id from rate_records where rate_id = ?", [$rateRecord]);
                $rateRecordRowId = $getRateId->id ?? 0;
            }
            $query = DB::table('rate_preferences as rp')
                ->select('rp.id', 'rp.rate_prefer_id', 'rp.rate_service_id', 'rp.rate_offering_id', 'rp.rate_record_id', 'rp.auto_bill')
                ->leftJoin('rateprefer_sources as rps', 'rps.rate_prefer_id', '=', 'rp.id')
                ->leftJoin('rateprefer_destinations as rds', 'rds.rate_prefer_id', '=', 'rp.id');
            if ($rateRecordRowId > 0) {
                $query->where('rp.rate_record_id', $rateRecordRowId);
            }
            if (!empty($vendorProfiles)) {
                $query->whereIn('rp.vend_profile_id', $vendorProfiles);
            } else {
                $query->where('rp.vend_profile_id', $vendProfileId);
            }
            $query->where('rp.tariff_type', '1')
                ->groupBy('rp.id')
                ->whereRaw($whr);
            $result = $query->get()->toArray();
        }
        return $result;
    }

    public function getvendorprofiledetailsbyid($vendorId, $serviceId, $info)
    {
        $vendorCode = SxPartyMembers::where('id', $vendorId)->value('code');
        $services = [];
        if ($vendorCode) {
            $getServices = $this->getVendorbasedservices($vendorCode, $serviceId);
            foreach ($getServices as $res) {
                $serviceName = $res->service_name;
                $service = $res->service_id;
                if ($serviceName) {
                    $service = $res->service_id . "-" . $serviceName;
                }
                $services[] = [
                    'service_row_id' => $res->id,
                    'service_id' => $service,
                    'vendor_code' => $vendorCode
                ];
            }
        }
        return $services;
    }

    public function getVendorbasedservices($vendorCode, $serviceId)
    {
        return RateService::select('rs.id', 'rs.service_id', 'rs.service_name')
            ->from('rate_services as rs')
            ->leftJoin('rate_offerings as ro', 'rs.id', '=', 'ro.rate_service_id')
            ->leftJoin('vendor_profiles as vp', 'vp.id', '=', 'ro.vendor_profile_id')
            ->leftJoin('vendor_profile_lists as vpl', 'vp.id', '=', 'vpl.vp_id')
            ->where('vpl.profile_id', $vendorCode)
            ->where('rs.service_type', $serviceId)
            ->where('rs.status', 1)
            ->where('vp.status', 1)
            ->where('vpl.status', 1)
            ->where('ro.status', 1)
            ->groupBy('rs.id')
            ->orderBy('rs.id', 'DESC')
            ->get();
    }

    public function getlaneids_byservice($serviceId, $orderId, $mainsource)
    {
        $lanes = [];
        $pickupCountry = $deliveryCountry = $pickupCity = $deliveryCity = $pickupZcode = $deliveryZcode = "";
        $pickupState = $deliveryState = $pickupSubDistrict = $pickupDivisionName = $deliverySubDistrict = $deliveryDivisionName = "";

        if ($orderId) {
            $getDetails = Order::where('id', $orderId)
                ->select('pickup_country', 'delivery_country', 'pickup_city', 'delivery_city', 'pickup_pincode', 'delivery_pincode', 'pickup_custid', 'pickup_partyid', 'drop_custid', 'drop_partyid', 'delivery_address2', 'pickup_address2')
                ->first();
            if ($getDetails) {
                $pickupCountry = $getDetails->pickup_country;
                $deliveryCountry = $getDetails->delivery_country;
                $pickupCity = $getDetails->pickup_city;
                $deliveryCity = $getDetails->delivery_city;
                $pickupZcode = $getDetails->pickup_pincode;
                $deliveryZcode = $getDetails->delivery_pincode;
                $pickupState = $getDetails->pickup_address2;
                $deliveryState = $getDetails->delivery_address2;

                $pwhr = "(id = '{$getDetails->pickup_custid}' OR code = '{$getDetails->pickup_custid}' OR customeridentifier = '{$getDetails->pickup_custid}')";
                $getPickupSubDetails = SxPartyMembers::whereRaw($pwhr)->select('sub_district', 'division_name')->first();
                if ($getPickupSubDetails) {
                    $pickupSubDistrict = $getPickupSubDetails->sub_district;
                    $pickupDivisionName = $getPickupSubDetails->division_name;
                }

                $dwhr = "(id = '{$getDetails->drop_custid}' OR code = '{$getDetails->drop_custid}' OR customeridentifier = '{$getDetails->drop_custid}')";
                $getDropSubDetails = SxPartyMembers::whereRaw($dwhr)->select('sub_district', 'division_name')->first();
                if ($getDropSubDetails) {
                    $deliverySubDistrict = $getDropSubDetails->sub_district;
                    $deliveryDivisionName = $getDropSubDetails->division_name;
                }
            }
        } else {
            $pickupCountry = $mainsource['shipper_country'];
            $deliveryCountry = $mainsource['consignee_country'];
            $pickupCity = $mainsource['shipper_city'];
            $deliveryCity = $mainsource['consignee_city'];
            $pickupZcode = $mainsource['shipper_zipcode'];
            $deliveryZcode = $mainsource['consignee_zipcode'];
        }

        $source = [
            'pickup_country' => $pickupCountry,
            'delivery_country' => $deliveryCountry,
            'pickup_city' => $pickupCity,
            'delivery_city' => $deliveryCity,
            'pickup_zcode' => $pickupZcode,
            'delivery_zcode' => $deliveryZcode,
            'pickup_sub_district' => $pickupSubDistrict,
            'pickup_division_name' => $pickupDivisionName,
            'delivery_sub_district' => $deliverySubDistrict,
            'delivery_division_name' => $deliveryDivisionName,
            'pickup_state' => $pickupState,
            'delivery_state' => $deliveryState
        ];

        $getLanes = $this->getlanes_byservice($source, $serviceId);
        foreach ($getLanes as $eachLane) {
            $laneId = $eachLane['lane_id'];
            $laneName = $eachLane['lane_name'];
            $lane_id = $laneName ? $laneId . "-" . $laneName : $laneId;
            $lanes[] = ['lane_row_id' => $eachLane['id'], 'lane_id' => $lane_id];
        }

        if ($pickupCountry == 'AUSTRALIA') {
            $getLanesByRange = $this->getlanes_byservicerange($serviceId);
            foreach ($getLanesByRange as $res) {
                $laneId = $res->lane_id;
                $laneName = $res->lane_name;
                $lane_id = $laneName ? $laneId . "-" . $laneName : $laneId;
                $lanes[] = ['lane_row_id' => $res->id, 'lane_id' => $lane_id];
            }
        }

        return $lanes;
    }

    public function getlanes_byservice($source, $serviceId)
    {
        $response = [];
        if (!empty($source)) {
            $whereCondition = $this->generateWhereConditionForGeo($source);
            if (!empty($whereCondition)) {
                $whereString = "( " . implode(' OR ', $whereCondition) . " )";
                $response = LanesMaster::select('l.id', 'l.lane_id', 'l.lane_name')
                    ->from('lanes_masters as l')
                    ->leftJoin('rateservice_lanes as rl', 'l.id', '=', 'rl.lane_id')
                    ->whereRaw($whereString)
                    ->where('rl.rate_id', $serviceId)
                    ->where('rl.status', 1)
                    ->where('l.status', 1)
                    ->groupBy('l.id')
                    ->orderBy('l.id', 'DESC')
                    ->get()
                    ->toArray();
            }
        }
        return $response;
    }

    public function getlanes_byservicerange($serviceId)
    {
        return LanesMaster::select('l.id', 'l.lane_id', 'l.lane_name')
            ->from('lanes_masters as l')
            ->leftJoin('rateservice_lanes as rl', 'l.id', '=', 'rl.lane_id')
            ->where('rl.rate_id', $serviceId)
            ->where('l.source_geo', 1)
            ->where('l.destination_geo', 1)
            ->where('rl.status', 1)
            ->where('l.status', 1)
            ->groupBy('l.id')
            ->orderBy('l.id', 'DESC')
            ->get();
    }

    public function checkrefandorcondition($orderId, $record)
    {
        $refcheck = 1;
        if ($orderId && $record > 0) {
            $noand = $noor = 1;
            $orwhr = $andwhr = "";
            $orar = $andar = [];
            $chkref = RateRecordCondition::where(['rate_record_id' => $record, 'status' => '1'])
                ->select('ref_id', 'ref_name', 'ref_value', 'condition_type')
                ->get();
            if ($chkref->isNotEmpty()) {
                $chkrefCount = $chkref->count();
                $refcheck = 0;
                $refcheknumRows = $andcond = $orcond = 1;
                $a = 0;
                foreach ($chkref as $ref) {
                    $conditionType = $chkrefCount == 1 ? 'AND' : $ref->condition_type;
                    $refPid = $ref->ref_id;
                    $refValue = $ref->ref_value;
                    if ($conditionType == "AND") {
                        $noand = 0;
                        $andar[] = ['ref_id' => $refPid, 'ref_value' => $refValue];
                        $a++;
                    } else if ($conditionType == 'OR') {
                        $noor = 0;
                        $orar[] = ['ref_id' => $refPid, 'ref_value' => $refValue];
                    }
                }
                if (!empty($andar)) {
                    $andwhr .= " AND ( ";
                    foreach ($andar as $i => $item) {
                        $refPid = $item['ref_id'];
                        $refValue = $item['ref_value'];
                        $andwhr .= $i == 0
                            ? "( r.id='{$refPid}' AND o.reference_id = r.name AND o.ref_value='{$refValue}' AND o.status ='1' )"
                            : " OR ( r.id='{$refPid}' AND o.reference_id = r.name AND o.ref_value='{$refValue}' AND o.status ='1' )";
                    }
                    $andwhr .= " ) ";
                }
                if (!empty($orar)) {
                    if (count($orar) > 1) {
                        $orwhr .= " AND ( ";
                        foreach ($orar as $i => $item) {
                            $refPid = $item['ref_id'];
                            $refValue = $item['ref_value'];
                            $orwhr .= $i == 0
                                ? "( r.id='{$refPid}' AND o.reference_id = r.name AND o.ref_value='{$refValue}' AND o.status ='1' )"
                                : " OR ( r.id='{$refPid}' AND o.reference_id = r.name AND o.ref_value='{$refValue}' AND o.status ='1' )";
                        }
                        $orwhr .= " ) ";
                    } else {
                        $noor = 1;
                    }
                }
                if ($refcheknumRows > 0) {
                    $andrecordmatch = $andwhr ? $this->checkandorconditionforref($orderId, $andwhr) : ($noand == 1 ? 1 : 0);
                    $andrecordmatch = $andwhr && $andrecordmatch == $a ? 1 : 0;
                    $orrecordmatch = $orwhr ? $this->checkandorconditionforref($orderId, $orwhr) : ($noor == 1 ? 1 : 0);
                    $refcheck = ($orrecordmatch > 0 && $andrecordmatch > 0) ? 1 : 0;
                }
            }
        }
        return $refcheck;
    }

    public function checkandorconditionforref($orderId, $whr)
    {
        return DB::table('order_references as o')
            ->join('reference_masters as r', 'o.reference_id', '=', 'r.name')
            ->where('o.order_id', $orderId)
            ->whereRaw($whr)
            ->count();
    }

    public function preference_checkrefandorcondition($orderId, $id)
    {
        $refcheck = 1;
        if ($orderId && $id > 0) {
            $noand = $noor = 1;
            $orwhr = $andwhr = "";
            $orar = $andar = [];
            $chkref = RatepreferRefTypes::where(['rate_prefer_id' => $id, 'status' => '1'])
                ->select('ref_id', 'ref_value', 'ref_condition')
                ->get();
            if ($chkref->isNotEmpty()) {
                $chkrefCount = $chkref->count();
                $refcheck = 0;
                $refcheknumRows = $andcond = $orcond = 1;
                $a = 0;
                $fileLineId = ReferenceMaster::where('name', 'FI')->value('id');
                foreach ($chkref as $ref) {
                    $conditionType = $chkrefCount == 1 ? 'AND' : $ref->ref_condition;
                    $refPid = $ref->ref_id;
                    $refValue = $ref->ref_value;
                    if ($chkrefCount == 1 && $refPid == $fileLineId) {
                        $getOrderFileRef = OrderReference::where(['order_id' => $orderId, 'reference_id' => 'FI', 'status' => '1'])->value('ref_value');
                        if (empty($getOrderFileRef)) {
                            return 1;
                        }
                        return $getOrderFileRef == $refValue ? 1 : 0;
                    }
                    if ($conditionType == "AND") {
                        $noand = 0;
                        $andar[] = ['ref_id' => $refPid, 'ref_value' => $refValue];
                        $a++;
                    } else if ($conditionType == 'OR') {
                        $noor = 0;
                        $orar[] = ['ref_id' => $refPid, 'ref_value' => $refValue];
                    }
                }
                if (!empty($andar)) {
                    $andwhr .= " AND ( ";
                    foreach ($andar as $i => $item) {
                        $refPid = $item['ref_id'];
                        $refValue = $item['ref_value'];
                        $andwhr .= $i == 0
                            ? "( r.id='{$refPid}' AND o.reference_id = r.name AND o.ref_value='{$refValue}' AND o.status ='1' )"
                            : " OR ( r.id='{$refPid}' AND o.reference_id = r.name AND o.ref_value='{$refValue}' AND o.status ='1' )";
                    }
                    $andwhr .= " ) ";
                }
                if (!empty($orar)) {
                    if (count($orar) > 1) {
                        $orwhr .= " AND ( ";
                        foreach ($orar as $i => $item) {
                            $refPid = $item['ref_id'];
                            $refValue = $item['ref_value'];
                            $orwhr .= $i == 0
                                ? "( r.id='{$refPid}' AND o.reference_id = r.name AND o.ref_value='{$refValue}' AND o.status ='1' )"
                                : " OR ( r.id='{$refPid}' AND o.reference_id = r.name AND o.ref_value='{$refValue}' AND o.status ='1' )";
                        }
                        $orwhr .= " ) ";
                    } else {
                        $noor = 1;
                    }
                }
                if ($refcheknumRows > 0) {
                    $andrecordmatch = $andwhr ? $this->checkandorconditionforref($orderId, $andwhr) : ($noand == 1 ? 1 : 0);
                    $andrecordmatch = $andwhr && $andrecordmatch == $a ? 1 : 0;
                    $orrecordmatch = $orwhr ? $this->checkandorconditionforref($orderId, $orwhr) : ($noor == 1 ? 1 : 0);
                    $refcheck = ($orrecordmatch > 0 && $andrecordmatch > 0) ? 1 : 0;
                }
            }
        }
        return $refcheck;
    }

    public function add_buypreference($arr)
    {
        $user = Auth::user();
        $cdate = now()->toDateTimeString();
        $userId = $user->id ?? $arr['user_id'] ?? 0;
        $orgId = $user->org_id ?? $arr['org_id'] ?? 0;
        $userCurrency = $user->usr_tzone['currency'] ?? 'USD';
        $exchangeRate = $foreignCurrency = "";
        $chargeOfCountry = $vasCharges = $chargeArVas = [];
        $type = '1';
        $offeringGid = 1;
        $recordId = 1;

        if (!empty($arr)) {
            $rateRecord = $arr['rate_record_id'];
            $rateOffering = $arr['rate_offering'];
            $rateAutobill = $arr['rate_autobill'];
            $rateServiceId = $arr['rate_service_id'];
            if ($rateOffering && $rateOffering != "0") {
                $getRateRecordId = RateRecord::where(['id' => $rateRecord, 'status' => '1'])->value('rate_id');
                if ($getRateRecordId) {
                    $recordId = $getRateRecordId;
                }
                if ($arr['order_id'] && $arr['order_id'] != "0") {
                    $vasCharges = $this->getchargesthrougvasbyrecord($rateOffering, $rateRecord, $arr['order_id'], $userId, $type);
                }
                foreach ($vasCharges as $vas) {
                    $vasChargeId = $vas['charge_id'];
                    $laneChargerowId = $laneChargename = "";
                    if ($vasChargeId && $vasChargeId != "0") {
                        $getChargeForLane = ChargeCode::where(['id' => $vasChargeId, 'status' => '1'])->select('id', 'name')->first();
                        if ($getChargeForLane) {
                            $laneChargerowId = $getChargeForLane->id;
                            $laneChargename = $getChargeForLane->name;
                        }
                    } else {
                        $getChargeForLane = ChargeCode::where(['charge_code' => 'CSC', 'status' => '1'])->select('id', 'name')->first();
                        if ($getChargeForLane) {
                            $laneChargerowId = $getChargeForLane->id;
                            $laneChargename = $getChargeForLane->name;
                        }
                    }
                    $chargeArVas[] = [
                        'charge_code' => $laneChargerowId,
                        'description' => $laneChargename,
                        'quantity_unit' => '1',
                        'value' => '1',
                        'rate_id' => $recordId,
                        'amount' => $vas['amount'],
                        'currency' => $vas['currency'],
                        'user_id' => $userId,
                        'createdon' => $cdate
                    ];
                }
                $getOfferingId = RateOffering::where(['id' => $rateOffering, 'status' => '1'])->value('offering_id');
                if ($getOfferingId) {
                    $offeringGid = $getOfferingId;
                }
                $offeringVasCharges = $this->getchargesthrougvasbyoffering($rateOffering, $type, $arr['order_id'], $userId);
                foreach ($offeringVasCharges as $res) {
                    $getChargeForLane = ChargeCode::where(['id' => $res['charge_id'], 'status' => '1'])->select('id', 'name')->first();
                    $laneChargerowId = $getChargeForLane->id ?? "";
                    $laneChargename = $getChargeForLane->name ?? "";
                    $chargeOfCountry[] = [
                        'charge_code' => $res['charge_id'],
                        'description' => $laneChargename,
                        'quantity_unit' => '1',
                        'value' => '1',
                        'rate_id' => $offeringGid,
                        'amount' => $res['amount'],
                        'currency' => $res['currency'],
                        'rate_service_id' => $rateServiceId,
                        'rate_offering_id' => $rateOffering,
                        'rate_record_id' => $rateRecord,
                        'user_id' => $userId,
                        'createdon' => $cdate
                    ];
                }
            }
            if ($rateRecord && $rateRecord != "0") {
                $getRateRecordId = RateRecord::where(['id' => $rateRecord, 'status' => '1'])->value('rate_id');
                if ($getRateRecordId) {
                    $recordId = $getRateRecordId;
                }
                $chargeAr = $this->getraterecordcharges($rateOffering, $rateRecord, $type, $arr['order_id'], $recordId, $userId);
                $invoiceStatus = 0;
                $debtorJfr = $invoiceNumber = $creditNoteNumber = "";
                $invoiceDate = '0000-00-00 00:00:00';
                $invCreatedDate = '0000-00-00 00:00:00';
                $invReceivedDate = '0000-00-00 00:00:00';
                $customerName = 'Vendor';
                if ($orgId == 'RUKN') {
                    $getCustomerDetails = SxPartyMembers::where(['code' => $arr['vendor_code'], 'status' => '1', 'org_id' => $orgId])->value('name');
                    if ($getCustomerDetails) {
                        $customerName = $getCustomerDetails;
                    }
                } else {
                    $getCustomerDetails = SxPartyMembers::where(['code' => $arr['vendor_code'], 'status' => '1', 'user_id' => $userId])->value('name');
                    if ($getCustomerDetails) {
                        $customerName = $getCustomerDetails;
                    }
                }
                $currency = $chargeAr[0]['currency'] ?? "";
                $foreignCurrency = $chargeAr[0]['foreign_currency'] ?? "";
                $exchangeRate = $chargeAr[0]['exchange_amount'] ?? 0;
                foreach ($chargeAr as &$charge) {
                    unset($charge['foreign_currency'], $charge['exchange_amount']);
                    $charge['rate_service_id'] = $rateServiceId;
                    $charge['rate_offering_id'] = $rateOffering;
                    $charge['rate_record_id'] = $rateRecord;
                }
                $revenueAr = [
                    'order_id' => $arr['order_id'],
                    'type' => '1',
                    'recipient_role' => 'Carrier',
                    'recipient_code' => $arr['vendor_code'],
                    'recipient_name' => $customerName,
                    'debtor_jfr' => $debtorJfr,
                    'invoice_number' => $invoiceNumber,
                    'credit_note_number' => $creditNoteNumber,
                    'invoice_date' => $invoiceDate,
                    'invoice_creation_date' => $invCreatedDate,
                    'invoice_receivdon_date' => $invReceivedDate,
                    'amount' => '0',
                    'currency' => $currency,
                    'status' => 1,
                    'user_id' => $userId,
                    'createdon' => $cdate,
                    'exchange_rate' => $exchangeRate,
                    'invoice_status' => $invoiceStatus,
                    'foreign_currency' => $foreignCurrency
                ];
                $revRowId = $this->insertrecords($revenueAr, $chargeAr, [], $chargeOfCountry, $chargeArVas);
                if ($rateRecord > 0 && $revRowId > 0) {
                    $this->addfafcharges_toorder($arr['order_id'], $revRowId, $rateRecord, 'RP - Rate Preference');
                }
            }
        }
    }

    public function getchargesthrougvasbyrecord($rateOffering, $record, $orderId, $userId, $type)
    {
        $amount = $finalConversion = 0;
        $charges = [];
        if ($record && $record != "0") {
            $getVasIds = RaterecordServices::where(['raterecord_id' => $record, 'status' => '1'])
                ->select('raterecord_vas_id', 'raterecord_charge_id', 'charge_basis', 'min_amount', 'amount', 'currency')
                ->get();
            foreach ($getVasIds as $res) {
                $finalAmount = 0;
                $vasId = $res->raterecord_vas_id;
                $chargeBasis = strtoupper($res->charge_basis);
                $recAmount = $res->amount;
                $minAmount = $res->min_amount;
                $currency = $res->currency;
                $chargeId = $res->raterecord_charge_id;
                $vasQuantity = OrderVas::where(['order_id' => $orderId, 'vas_id' => $vasId, 'status' => '1'])->value('quantity') ?? 0;
                if ($vasQuantity != '0') {
                    if (in_array($chargeBasis, ['PER KG', 'KG', 'PER CBM', 'CBM', 'PER LDM', 'LDM', 'PIECE COUNT', 'PER PIECE', 'GOODS VALUE'])) {
                        $amountAr = $this->claculateperkgamount($rateOffering, $orderId, $userId, $type, $recAmount, $chargeBasis, 'vas');
                        $finalConversion = $amountAr['final_conversion'] ?? 0;
                        if ($finalConversion > 0) {
                            $amount = $finalConversion * $recAmount;
                        } else {
                            $getOrderDetails = $this->getallordercargodetails($orderId);
                            if ($getOrderDetails) {
                                $totalLdm = $getOrderDetails['total_ldm'] ?? 0;
                                $totalWeight = $getOrderDetails['total_weight'] ?? 0;
                                $totalVolume = $getOrderDetails['total_volume'] ?? 0;
                                $totalQuantity = $getOrderDetails['total_quantity'] ?? 0;
                                $orderGoodsValue = $getOrderDetails['goods_value'] ?? 0;
                                if ($chargeBasis == 'PER KG' || $chargeBasis == 'KG') {
                                    $amount = $totalWeight * $recAmount;
                                } else if ($chargeBasis == 'PER CBM' || $chargeBasis == 'CBM') {
                                    $amount = $totalVolume * $recAmount;
                                } else if ($chargeBasis == 'PER LDM' || $chargeBasis == 'LDM') {
                                    $amount = $totalLdm * $recAmount;
                                } else if ($chargeBasis == 'PER PIECE') {
                                    $amount = $totalQuantity * $recAmount;
                                } else if ($chargeBasis == 'GOODS VALUE') {
                                    $amount = $orderGoodsValue * $recAmount;
                                }
                            }
                        }
                        $amount = $amount > 0 ? $amount : ($vasQuantity * $recAmount);
                    } else if ($chargeBasis == 'PER TRIP' || $chargeBasis == 'TRIP') {
                        $amount = $vasQuantity * $recAmount;
                    } else {
                        $amount = $recAmount;
                    }
                    if ($minAmount > $amount) {
                        $amount = $minAmount;
                    }
                    $finalAmount += $amount;
                    $charges[] = ['amount' => $finalAmount, 'currency' => $currency, 'charge_id' => $chargeId];
                }
            }
        }
        return $charges;
    }

    public function claculateperkgamount($offering, $orderId, $userId, $type, $amount, $chargeBasis, $vasType)
    {
        $finalAmount = 0;
        $conversionId = 0;
        $finalConversion = $result = 0;

        if ($offering > 0) {
            $getConversionId = RateOffering::where('id', $offering)
                ->where('status', '1')
                ->select('uom_conversion_id')
                ->first();

            if ($getConversionId) {
                $conversionId = $getConversionId->uom_conversion_id;
            }
        }

        $chkType = $type == '1' ? 'BUY' : 'SELL';

        $countryCode = $companyCode = "";
        if ($conversionId != '0' && $conversionId != "") {
            $userData = DB::table('sx_users')
                ->where('id', $userId)
                ->select('country_code', 'company_code')
                ->first();

            if ($userData) {
                $countryCode = $userData->country_code;
                $companyCode = $userData->company_code;
            }

            $baseUom = $chargeBasis;
            if (in_array($chargeBasis, ['PER KG', 'KG'])) {
                $baseUom = 'KG';
            } elseif (in_array($chargeBasis, ['PER CBM', 'CBM'])) {
                $baseUom = 'CBM';
            } elseif (in_array($chargeBasis, ['PER LDM', 'LDM'])) {
                $baseUom = 'LDM';
            }

            $getChargeableWeight = SxuomConversion::where('id', $conversionId)
                ->where('country', $countryCode)
                ->where('type', $chkType)
                ->where('base_uom', $baseUom)
                ->where('status', '1')
                ->select(
                    'uom_type1',
                    'uom1',
                    'uom1_conversion',
                    'uom_type2',
                    'uom2',
                    'uom2_conversion',
                    'uom_type3',
                    'uom3',
                    'uom3_conversion',
                    'uom_type4',
                    'uom4',
                    'uom4_conversion',
                    'customer_id',
                    'carrier_id'
                )
                ->first();

            if ($getChargeableWeight) {
                $sts = 0;
                $customerId = $getChargeableWeight->customer_id;
                $carrierId = $getChargeableWeight->carrier_id;

                if ($type == '1') {
                    if ($carrierId != "0" && $carrierId != "") {
                        $chkOrderTrip = Order::where('id', $orderId)
                            ->where('shift_id', '>', '0')
                            ->select('vendor_id')
                            ->first();

                        if ($chkOrderTrip) {
                            $vendorId = $chkOrderTrip->vendor_id;
                            if ($vendorId > 0) {
                                $sts = $carrierId == $vendorId ? 1 : 0;
                            }
                        } else {
                            $sts = 1;
                        }
                    } else {
                        $sts = 1;
                    }
                } else {
                    if ($customerId != "0" && $customerId != "") {
                        $chkOrderCustomer = Order::where('id', $orderId)
                            ->select('customer_id')
                            ->first();

                        if ($chkOrderCustomer) {
                            $orderCustomerId = $chkOrderCustomer->customer_id;
                            $sts = $orderCustomerId == $customerId ? 1 : 0;
                        }
                    } else {
                        $sts = 1;
                    }
                }

                $totalConversion1 = $totalConversion2 = $totalConversion3 = $totalConversion4 = ['status' => '0', 'totalAmount' => '1'];

                if ($sts == '1') {
                    if ($getChargeableWeight->uom1 != "") {
                        $totalConversion1 = $this->getconversionableamount(
                            $getChargeableWeight->uom1,
                            $getChargeableWeight->uom_type1,
                            $getChargeableWeight->uom1_conversion,
                            $orderId
                        );
                    }
                    if ($getChargeableWeight->uom2 != "") {
                        $totalConversion2 = $this->getconversionableamount(
                            $getChargeableWeight->uom2,
                            $getChargeableWeight->uom_type2,
                            $getChargeableWeight->uom2_conversion,
                            $orderId
                        );
                    }
                    if ($getChargeableWeight->uom3 != "") {
                        $totalConversion3 = $this->getconversionableamount(
                            $getChargeableWeight->uom3,
                            $getChargeableWeight->uom_type3,
                            $getChargeableWeight->uom3_conversion,
                            $orderId
                        );
                    }
                    if ($getChargeableWeight->uom4 != "") {
                        $totalConversion4 = $this->getconversionableamount(
                            $getChargeableWeight->uom4,
                            $getChargeableWeight->uom_type4,
                            $getChargeableWeight->uom4_conversion,
                            $orderId
                        );
                    }

                    $finalConversion1 = $totalConversion1['status'] == '1' ? $totalConversion1['totalAmount'] : '0';
                    $finalConversion2 = $totalConversion2['status'] == '1' ? $totalConversion2['totalAmount'] : '0';
                    $finalConversion3 = $totalConversion3['status'] == '1' ? $totalConversion3['totalAmount'] : '0';
                    $finalConversion4 = $totalConversion4['status'] == '1' ? $totalConversion4['totalAmount'] : '0';

                    $result = 1;
                    $finalConversion = max($finalConversion1, $finalConversion2, $finalConversion3, $finalConversion4);
                } else {
                    $finalConversion = 0;
                    $result = 0;
                }
            } else {
                $finalConversion = 0;
                $result = 0;
            }
        } else {
            $finalConversion = 0;
            $result = 1;
        }

        return ['finalConversion' => $finalConversion, 'result' => $result];
    }

    public function getconversionableamount($uom1, $uomType1, $conversion1, $orderId)
    {
        $totalAmount = 0;
        $res = ['status' => '0', 'totalAmount' => $totalAmount];

        if (!empty($uom1)) {
            $uomType1 = strtoupper($uomType1);

            if ($uomType1 == 'ACTUAL WEIGHT') {
                $chkOrderWeight = DB::table('cargo_details as c')
                    ->join('order_cargodetails as o', 'c.id', '=', 'o.cargo_id')
                    ->where('o.order_id', $orderId)
                    ->where('o.status', '1')
                    ->where('c.weight_unit', '!=', $uom1)
                    ->select('c.id')
                    ->exists();

                if (!$chkOrderWeight) {
                    $totalWeight = OrderCargoDetail::where('order_id', $orderId)
                        ->where('status', '1')
                        ->sum('weight');

                    if ($totalWeight !== null) {
                        $totalAmount = $totalWeight * $conversion1;
                        $res = ['status' => '1', 'totalAmount' => $totalAmount];
                    }
                }
            }

            if ($uomType1 == 'WEIGHT') {
                $chkOrderWeight = DB::table('cargo_details as c')
                    ->join('order_cargodetails as o', 'c.id', '=', 'o.cargo_id')
                    ->where('o.order_id', $orderId)
                    ->where('o.status', '1')
                    ->where('c.secondweight_uom', '!=', $uom1)
                    ->select('c.id')
                    ->exists();

                if (!$chkOrderWeight) {
                    $totalWeight = OrderCargoDetail::where('order_id', $orderId)
                        ->where('status', '1')
                        ->sum('second_weight');

                    if ($totalWeight !== null) {
                        $totalAmount = $totalWeight * $conversion1;
                        $res = ['status' => '1', 'totalAmount' => $totalAmount];
                    }
                }
            }

            if ($uomType1 == 'ACTUAL VOLUME') {
                $chkOrderVolume = DB::table('cargo_details as c')
                    ->join('order_cargodetails as o', 'c.id', '=', 'o.cargo_id')
                    ->where('o.order_id', $orderId)
                    ->where('o.status', '1')
                    ->where('c.volume_unit', '!=', $uom1)
                    ->select('c.id')
                    ->exists();

                if (!$chkOrderVolume) {
                    $totalVolume = OrderCargoDetail::where('order_id', $orderId)
                        ->where('status', '1')
                        ->sum('volume');

                    if ($totalVolume !== null) {
                        $totalAmount = $totalVolume * $conversion1;
                        $res = ['status' => '1', 'totalAmount' => $totalAmount];
                    }
                }
            }

            if ($uomType1 == 'VOLUME') {
                $chkOrderVolume = DB::table('cargo_details as c')
                    ->join('order_cargodetails as o', 'c.id', '=', 'o.cargo_id')
                    ->where('o.order_id', $orderId)
                    ->where('o.status', '1')
                    ->where('c.secondvolume_uom', '!=', $uom1)
                    ->select('c.id')
                    ->exists();

                if (!$chkOrderVolume) {
                    $totalVolume = OrderCargoDetail::where('order_id', $orderId)
                        ->where('status', '1')
                        ->sum('second_volume');

                    if ($totalVolume !== null) {
                        $totalAmount = $totalVolume * $conversion1;
                        $res = ['status' => '1', 'totalAmount' => $totalAmount];
                    }
                }
            }

            if ($uomType1 == 'VOLUMETRIC WEIGHT') {
                $chkOrderVolume = DB::table('cargo_details as c')
                    ->join('order_cargodetails as o', 'c.id', '=', 'o.cargo_id')
                    ->where('o.order_id', $orderId)
                    ->where('o.status', '1')
                    ->where('c.volweight_uom', '!=', $uom1)
                    ->select('c.id')
                    ->exists();

                if (!$chkOrderVolume) {
                    $totalVVolume = OrderCargoDetail::where('order_id', $orderId)
                        ->where('status', '1')
                        ->sum('volumetric_weight');

                    if ($totalVVolume !== null) {
                        $totalAmount = $totalVVolume * $conversion1;
                        $res = ['status' => '1', 'totalAmount' => $totalAmount];
                    }
                }
            }

            if ($uomType1 == 'LDM') {
                $totalLdm = OrderCargoDetail::where('order_id', $orderId)
                    ->where('status', '1')
                    ->sum('ldm');

                if ($totalLdm !== null) {
                    $totalAmount = $totalLdm * $conversion1;
                    $res = ['status' => '1', 'totalAmount' => $totalAmount];
                }
            }

            if ($uomType1 == 'QUANTITY') {
                $totalQuantity = OrderCargoDetail::where('order_id', $orderId)
                    ->where('status', '1')
                    ->sum('quantity');

                if ($totalQuantity !== null) {
                    $totalAmount = $totalQuantity * $conversion1;
                    $res = ['status' => '1', 'totalAmount' => $totalAmount];
                }
            }
        }

        return $res;
    }

    public function getallordercargodetails($orderId)
    {
        $result = DB::table('cargo_details as c')
            ->join('order_cargodetails as o', 'c.id', '=', 'o.cargo_id')
            ->join('orders', 'o.order_id', '=', 'orders.id')
            ->where('o.order_id', $orderId)
            ->where('o.status', '1')
            ->selectRaw('sum(o.weight) as total_weight, sum(o.second_weight) as second_weight, sum(o.volume) as total_volume, sum(o.second_volume) as second_volume, sum(o.quantity) as total_quantity, sum(o.ldm) as total_ldm, c.weight_unit, c.secondweight_uom, goods_value')
            ->first();
        return (array) $result;
    }

    public function getchargesthrougvasbyoffering($offering, $type, $orderId, $userId)
    {
        $charges = [];
        $unit2 = 1;
        if ($offering && $offering != "0") {
            $getCharges = RateOfferingService::where(['rateoffering_id' => $offering, 'status' => '1'])
                ->select('ro_charge_id', 'charge_basis', 'min_amount', 'amount', 'currency')
                ->get();
            foreach ($getCharges as $res) {
                $finalAmount = $res->amount;
                $chargeId = $res->ro_charge_id;
                $chargeBasis = strtoupper($res->charge_basis);
                $minAmount = $res->min_amount;
                $currency = $res->currency;
                if ($orderId && $orderId != "0" && in_array($chargeBasis, ['PER KG', 'KG', 'PER CBM', 'CBM', 'PER LDM', 'LDM', 'PER PIECE', 'GOODS VALUE'])) {
                    $unitAr = $this->claculateperkgamount($offering, $orderId, $userId, $type, $res->amount, $chargeBasis, 'non-vas');
                    $finalConversion = $unitAr['final_conversion'] ?? 0;
                    $unit2 = $finalConversion > 0 ? $finalConversion : ($this->getallordercargodetails($orderId)[strtolower(str_replace('PER ', '', $chargeBasis))] ?? 1);
                }
                $finalAmount = $unit2 * $res->amount;
                if ($finalAmount < $minAmount) {
                    $finalAmount = $minAmount;
                }
                $charges[] = ['amount' => $finalAmount, 'currency' => $currency, 'charge_id' => $chargeId];
            }
        }
        return $charges;
    }

    public function getraterecordcharges($offering, $record, $type, $orderId, $recordId, $userId, $legsLocation = [])
    {
        $exchangeAmount = $totalAmount = $unit2 = 0;
        $chargeAr = [];
        $foreignCurrency = "";
        $cdate = now()->toDateTimeString();
        $orgId = Auth::user()->org_id ?? 0;
        $userCurrency = Auth::user()->usr_tzone['currency'] ?? 'USD';
        $getRecords = RaterecordCharges::where(['raterecord_id' => $record, 'status' => '1'])
            ->select('rr_charge_type', 'rr_tier_id', 'geo_tier_id', 'rr_charge_id', 'charge_basis', 'exchange_rate_id', 'min_amount', 'amount', 'currency')
            ->get();
        foreach ($getRecords as $res) {
            $unit2 = 1;
            $chargeId = $res->rr_charge_id;
            $rrChargeType = $res->rr_charge_type;
            $geoTierId = $res->geo_tier_id;
            $tierId = $res->rr_tier_id;
            $minAmount = $res->min_amount;
            $totalAmount = $res->amount;
            $currency = $res->currency;
            $exchangeRateId = $res->exchange_rate_id;
            $chargeBasis = strtoupper($res->charge_basis);
            $fafChargeCode = ChargeCode::where('id', $chargeId)->value('charge_code') ?? "";
            if (!(trim($rrChargeType) == "FIXED" && trim($fafChargeCode) == "FAF" && trim($chargeBasis) == "FRT")) {
                if ($exchangeRateId && $orgId != "PLKN" && $userCurrency != $currency) {
                    $info = ['exchange_rate_id' => $exchangeRateId, 'from_currency' => $currency, 'to_currency' => $userCurrency, 'user_id' => $userId, 'order_id' => $orderId];
                    $exchangeAmount = $this->getexchnageamountfromcurrency($info);
                    $foreignCurrency = $currency;
                } elseif ($orgId == "PLKN") {
                    $foreignCurrency = "EUR";
                    $info = ['exchange_rate_id' => $exchangeRateId, 'from_currency' => $currency, 'to_currency' => $userCurrency, 'user_id' => $userId, 'order_id' => $orderId];
                    $exchangeAmount = $this->getexchnageamountfromcurrency($info);
                }
                if ($exchangeAmount == 0 && $currency != $userCurrency) {
                    $getOrderDate = Order::where('id', $orderId)->value('createdon') ?? now()->toDateString();
                    $query = $this->getExchangeRateAmountFromUser(['userId' => $userId, 'currency' => $currency, 'userCurrency' => $userCurrency, 'currentDate' => $getOrderDate]);
                    $exchangeRateId = $query->id ?? 0;
                    $exchangeAmount = $query->exchange_rate ?? 0;
                }
                if (in_array($chargeBasis, ['PER KG', 'KG', 'PER CBM', 'CBM', 'LDM', 'PER LDM', 'PIECE COUNT', 'GOODS VALUE'])) {
                    if ($orderId && $orderId != "0") {
                        $unitAr = $this->claculateperkgamount($offering, $orderId, $userId, $type, $totalAmount, $chargeBasis, 'non-vas');
                        $unit2 = $unitAr['final_conversion'] ?? ($this->getallordercargodetails($orderId)[strtolower(str_replace('PER ', '', $chargeBasis))] ?? 1);
                    }
                }
                $totalAmount = $unit2 * $totalAmount;
                if ($totalAmount < $minAmount) {
                    $totalAmount = $minAmount;
                }
                $chargeName = ChargeCode::where('id', $chargeId)->value('name') ?? ($chargeId ? "Freight Charges" : "");
                $chargeId = $chargeId ?: "38";
                if ($tierId) {
                    $tierAr = $this->getratechargeamount_bytier($offering, $tierId, $orderId, $type, $userId);
                    if ($tierAr) {
                        $recordId = $tierAr['tier_id'];
                        $totalAmount = $tierAr['cost'];
                        $currency = $tierAr['currency'] ?: $userCurrency;
                        $foreignCurrency = $userCurrency != $currency ? $currency : "";
                    }
                }
                if ($geoTierId) {
                    $geoTierAr = $this->getratechargeamount_bygeotier($offering, $geoTierId, $orderId, $type, $legsLocation);
                    if ($geoTierAr) {
                        $recordId = $geoTierAr['geo_tier_id'];
                        $totalAmount = $geoTierAr['cost']['cost'] ?? "";
                        $minAmount = $geoTierAr['cost']['min_amount'] ?? "";
                        $currency = $geoTierAr['cost']['currency'] ?? $userCurrency;
                        if ($minAmount > $totalAmount) {
                            $totalAmount = $minAmount;
                        }
                        $foreignCurrency = $userCurrency != $currency ? $currency : "";
                    }
                }
                if ($rrChargeType == 'Fixed') {
                    $recordId = RateRecord::where(['id' => $record, 'status' => '1'])->value('rate_id') ?? 1;
                }
                if ($totalAmount != '0') {
                    $chargeAr[] = [
                        'charge_code' => $chargeId,
                        'description' => $chargeName,
                        'quantity_unit' => '1',
                        'value' => '1',
                        'rate_id' => $recordId,
                        'amount' => $totalAmount,
                        'currency' => $currency,
                        'foreign_currency' => $foreignCurrency,
                        'user_id' => $userId,
                        'createdon' => $cdate,
                        'exchange_amount' => $exchangeAmount
                    ];
                }
            }
        }
        return $chargeAr;
    }

    public function getexchnageamountfromcurrency($info)
    {
        $exchangeRateAmount = 0;
        $orderCreatedDate = now()->toDateString();
        $orderCustomer = "0";
        $orgId = Auth::user()->org_id ?? $info['company_code'] ?? 0;
        $beValue = Auth::user()->be_value ?? $info['branch_code'] ?? "";
        $curtz = Auth::user()->usr_tzone['timezone'] ?? DFLT_TZ;
        if ($orgId == "PLKN") {
            $info['to_currency'] = 'PLN';
        }
        if ($info['order_id']) {
            $getOrderDate = Order::where(['id' => $info['order_id'], 'user_id' => $info['user_id']])
                ->select('pickup_datetime', 'customer_id', 'org_id', 'be_value', 'createdon')
                ->first();
            if ($getOrderDate) {
                $orderCreatedDate = date('Y-m-d', strtotime($getOrderDate->createdon));
                if ($getOrderDate->pickup_datetime && $getOrderDate->pickup_datetime != "0000-00-00 00:00:00") {
                    $pdates = $this->orderProcessor->getDateTimeByTimezone('UTC', $getOrderDate->pickup_datetime, DFLT_TZ);
                    $orderCreatedDate = date('Y-m-d', strtotime($pdates['datetime']));
                }
                $orderCustomer = $getOrderDate->customer_id;
                $orgId = $getOrderDate->org_id;
                $beValue = $getOrderDate->be_value;
            }
            $info['customer_id'] = $orderCustomer;
            $info['org_id'] = $orgId;
            $info['be_value'] = $beValue;
            $info['order_createddate'] = $orderCreatedDate;
        }
        return $this->getexchangerateamount_fororder($info);
    }

    public function getexchangerateamount_fororder($info)
    {
        $exchangeRateAmount = 0;
        if ($info['exchange_rate_id']) {
            $getExchangeRate = RateExchangeSheet::where([
                'rate_exchange_id' => $info['exchange_rate_id'],
                'exchange_from' => $info['from_currency'],
                'exchange_to' => $info['to_currency'],
                'status' => '1'
            ])
                ->where('effective_date', '<=', $info['order_createddate'])
                ->where('expiry_date', '>=', $info['order_createddate'])
                ->value('exchange_rate');
            $exchangeRateAmount = $getExchangeRate ?? 0;
        } else if ($info['order_id'] > 0 && $info['customer_id'] > 0) {
            $getExchangeRate = RateExchangeSheet::join('rate_exchanges as r', 'r.id', '=', 'rate_exchange_sheets.rate_exchange_id')
                ->where('r.customer_id', $info['customer_id'])
                ->where('r.status', '1')
                ->where('r.org_id', $info['org_id'])
                ->where('r.be_value', $info['be_value'])
                ->where('rate_exchange_sheets.exchange_from', $info['from_currency'])
                ->where('rate_exchange_sheets.exchange_to', $info['to_currency'])
                ->where('rate_exchange_sheets.effective_date', '<=', $info['order_createddate'])
                ->where('rate_exchange_sheets.expiry_date', '>=', $info['order_createddate'])
                ->where('rate_exchange_sheets.status', '1')
                ->orderBy('r.id', 'DESC')
                ->value('rate_exchange_sheets.exchange_rate');
            $exchangeRateAmount = $getExchangeRate ?? $this->fallbackExchangeRate($info);
        }
        return $exchangeRateAmount;
    }

    private function fallbackExchangeRate($info)
    {
        $exchangeRateAmount = RateExchangeSheet::join('rate_exchanges as r', 'r.id', '=', 'rate_exchange_sheets.rate_exchange_id')
            ->where('r.org_id', $info['org_id'])
            ->where('r.be_value', $info['be_value'])
            ->where('rate_exchange_sheets.exchange_from', $info['from_currency'])
            ->where('rate_exchange_sheets.exchange_to', $info['to_currency'])
            ->where('rate_exchange_sheets.status', '1')
            ->orderBy('r.id', 'DESC')
            ->value('rate_exchange_sheets.exchange_rate');
        return $exchangeRateAmount ?? 0;
    }

    public function getExchangeRateAmountFromUser(array $data): object
    {
        return DB::selectOne("SELECT r.id, s.exchange_rate FROM rate_exchange_sheets s, rate_exchanges r WHERE r.user_id = ? AND r.id=s.rate_exchange_id AND s.exchange_from = ? AND s.exchange_to = ? AND s.effective_date <= ? AND s.expiry_date >= ? AND s.status = ? ORDER BY r.id DESC LIMIT 1", [
            $data['userId'],
            $data['currency'],
            $data['userCurrency'],
            $data['currentDate'],
            $data['currentDate'],
            1
        ]);
    }

    public function getratechargeamount_bytier($offering, $tierRowId, $orderId, $type, $userId)
    {
        $totalLdm = $secondVolume = $secondWeight = $totalWeight = $totalVolume = $cost = $tierId = $unit2 = 0;
        $secondweightUom = $weightUnit = $utype2 = $utype1 = $currency = "";
        $gettierDetails = TierMaster::where(['id' => $tierRowId, 'status' => '1'])
            ->select('tier_id', 'tier_name', 'uom1', 'unit_measure1', 'uom2', 'unit_measure2', 'currency', 'billing_uom')
            ->first();
        if ($gettierDetails) {
            $uom1 = $gettierDetails->uom1;
            $currency = $gettierDetails->currency;
            $tierId = $gettierDetails->tier_id;
            $billingUom = $gettierDetails->billing_uom;
            $unitMeasure1 = strtoupper($gettierDetails->unit_measure1);
            $uom2 = $gettierDetails->uom2;
            $unitMeasure2 = strtoupper($gettierDetails->unit_measure2);
            $getOrderDetails = $this->getallordercargodetails($orderId);
            if ($getOrderDetails) {
                $totalLdm = $getOrderDetails['total_ldm'] ?? 0;
                $secondVolume = $getOrderDetails['second_volume'] ?? 0;
                $secondWeight = $getOrderDetails['second_weight'] ?? 0;
                $totalWeight = $getOrderDetails['total_weight'] ?? 0;
                $totalVolume = $getOrderDetails['total_volume'] ?? 0;
                $secondweightUom = $getOrderDetails['secondweight_uom'] ?? "";
                $weightUnit = $getOrderDetails['weight_unit'] ?? "";
            }
            $convertedWeight = $this->weightunitsconversion($totalWeight, $uom1, $uom2, $unitMeasure1, $unitMeasure2, $weightUnit);
            $convertedSecondweight = $this->weightunitsconversion($secondWeight, $uom1, $uom2, $unitMeasure1, $unitMeasure2, $secondweightUom);
            $utype1 = $uom1 == "ACTUAL WEIGHT" ? $convertedWeight : ($uom1 == "WEIGHT" ? $convertedSecondweight : ($uom1 == "ACTUAL VOLUME" ? $totalVolume : ($uom1 == "VOLUME" ? $secondVolume : ($uom1 == "LDM" ? $totalLdm : 0))));
            $utype2 = $uom2 == "ACTUAL WEIGHT" ? $convertedWeight : ($uom2 == "WEIGHT" ? $convertedSecondweight : ($uom2 == "ACTUAL VOLUME" ? $totalVolume : ($uom2 == "VOLUME" ? $secondVolume : ($uom2 == "LDM" ? $totalLdm : 0))));
            if ($billingUom == 'WEIGHT') {
                $unit2 = $secondWeight;
                $unitAr = $this->claculateperkgamount($offering, $orderId, $userId, $type, $cost, $secondweightUom, 'non-vas');
            } else if ($billingUom == 'VOLUME') {
                $unit2 = $secondVolume;
                $unitAr = $this->claculateperkgamount($offering, $orderId, $userId, $type, $cost, 'CBM', 'non-vas');
            } else if ($billingUom == 'LDM') {
                $unit2 = $totalLdm;
                $unitAr = $this->claculateperkgamount($offering, $orderId, $userId, $type, $cost, 'LDM', 'non-vas');
            } else if ($billingUom == 'ACTUAL WEIGHT') {
                $unit2 = $totalWeight;
                $unitAr = $this->claculateperkgamount($offering, $orderId, $userId, $type, $cost, $weightUnit, 'non-vas');
            } else if ($billingUom == 'ACTUAL VOLUME') {
                $unit2 = $totalVolume;
                $unitAr = $this->claculateperkgamount($offering, $orderId, $userId, $type, $cost, 'LDM', 'non-vas');
            }
            if ($unitAr) {
                $finalConversion = $unitAr['final_conversion'] ?? 0;
                if ($finalConversion > 0) {
                    $unit2 = $finalConversion;
                }
            }
            if ($uom1 == $billingUom) {
                $utype1 = $unit2;
            }
            if ($uom2 == $billingUom) {
                $utype2 = $unit2;
            }
            if (in_array($unitMeasure1, ['KM', 'MI']) || in_array($unitMeasure2, ['KM', 'MI'])) {
                $getOrderLatlngs = Order::where('id', $orderId)->select('plat', 'plng', 'dlat', 'dlng')->first();
                $response = $this->distanceship($getOrderLatlngs->plat, $getOrderLatlngs->plng, $getOrderLatlngs->dlat, $getOrderLatlngs->dlng);
                if (empty($response['distance'])) {
                    $response = $this->distancemetrixship($getOrderLatlngs->plat, $getOrderLatlngs->plng, $getOrderLatlngs->dlat, $getOrderLatlngs->dlng);
                }
                if ($response['disttext']) {
                    $distanceArray = explode(" ", $response['disttext']);
                    $distance = $distanceArray[0] ?? 0;
                    $utype1 = $unitMeasure1 == "KM" ? $distance : ($unitMeasure1 == "MI" ? $distance * 0.621371 : $utype1);
                    $utype2 = $unitMeasure2 == "KM" ? $distance : ($unitMeasure2 == "MI" ? $distance * 0.621371 : $utype2);
                }
            }
            if ($utype1 > 0 && $utype2 > 0) {
                $cost = $this->getweightvolumecharges($tierRowId, $utype1, $utype2);
            }
            $cost = $unit2 * $cost;
        }
        return ['cost' => $cost, 'tier_id' => $tierId, 'currency' => $currency];
    }

    public function weightunitsconversion($totalWeight, $uom1, $uom2, $unitMeasure1, $unitMeasure2, $weightUnit)
    {
        $convertedWeight = 1;
        $weightUnit = strtoupper($weightUnit);
        if ($weightUnit == 'KG') {
            if (strtoupper($uom1) == 'WEIGHT') {
                $convertedWeight = $unitMeasure1 == 'KG' ? $totalWeight : ($unitMeasure1 == 'LB' ? $totalWeight * 2.2046 : ($unitMeasure1 == 'TON' || $unitMeasure1 == 'MTON' ? $totalWeight * 0.001 : $convertedWeight));
            } else if (strtoupper($uom2) == 'WEIGHT') {
                $convertedWeight = $unitMeasure2 == 'KG' ? $totalWeight : ($unitMeasure2 == 'LB' ? $totalWeight * 2.2046 : ($unitMeasure2 == 'TON' || $unitMeasure2 == 'MTON' ? $totalWeight * 0.001 : $convertedWeight));
            }
        } else if ($weightUnit == 'TON') {
            if (strtoupper($uom1) == 'WEIGHT') {
                $convertedWeight = $unitMeasure1 == 'KG' ? $totalWeight * 1000 : ($unitMeasure1 == 'LB' ? $totalWeight * 2204 : ($unitMeasure1 == 'TON' || $unitMeasure1 == 'MTON' ? $totalWeight : $convertedWeight));
            } else if (strtoupper($uom2) == 'WEIGHT') {
                $convertedWeight = $unitMeasure2 == 'KG' ? $totalWeight * 1000 : ($unitMeasure2 == 'LB' ? $totalWeight * 2204 : ($unitMeasure2 == 'TON' || $unitMeasure2 == 'MTON' ? $totalWeight : $convertedWeight));
            }
        }
        return $convertedWeight;
    }

    public function distanceship($lat1, $lng1, $lat2, $lng2): array
    {
        $lat1 = (float) $lat1;
        $lat2 = (float) $lat2;
        $lng1 = (float) $lng1;
        $lng2 = (float) $lng2;

        // Replace session with Laravel cache for distance caching
        $cacheChain = implode('-', [$lat1, $lng1, $lat2, $lng2]);
        $cachedResult = Cache::get('distances_' . $cacheChain);

        if ($cachedResult) {
            return $cachedResult;
        }

        $result = DistanceMaster::select('distance', 'disttext', 'duration', 'duratext')
            ->whereRaw("(((3959 * acos(cos(radians(?)) * cos(radians(slat)) * cos(radians(slng) - radians(?))) + sin(radians(?)) * sin(radians(slat))))) < 0.05", [$lat1, $lng1, $lat2, $lng2])
            ->whereRaw("(((3959 * acos(cos(radians(?)) * cos(radians(dlat)) * cos(radians(dlng) - radians(?))) + sin(radians(?)) * sin(radians(dlat))))) < 0.05", [$lat2, $lng2, $lat1, $lng1])
            ->orderBy('id')
            ->limit(1)
            ->first();

        $result = [
            'distance' => $result->distance ?? "",
            'disttext' => $result->disttext ?? "",
            'duration' => $result->duration ?? "",
            'duratext' => $result->duratext ?? ""
        ];

        Cache::put('distances_' . $cacheChain, $result, now()->addHours(1));

        return $result;
    }

    public function distancemetrixship($lat1, $lng1, $lat2, $lng2)
    {
        $res = ['distance' => '', 'disttext' => '', 'duration' => '', 'duratext' => ''];

        $getqry = DB::table('distance_master')
            ->where('slat', $lat1)
            ->where('slng', $lng1)
            ->where('dlat', $lat2)
            ->where('dlng', $lng2)
            ->limit(1)
            ->first();

        if (!$getqry) {
            $url = 'https://maps.googleapis.com/maps/api/distancematrix/json?origins=' . $lat1 . ',' . $lng1 . '&destinations=' . $lat2 . ',' . $lng2 . '&language=en&key=' . env('GOOGLE_BKND_API_KEY');

            $data = $this->processGoogleAPICurl($url);
            if (!empty($data) && $data['status'] == "OK") {
                $a = $data["rows"][0]["elements"];
                if ($a[0]["status"] == "NOT_FOUND" || $a[0]["status"] == "ZERO_RESULTS") {
                    $res['distance'] = $res['disttext'] = "Route Not Found!";
                } else {
                    $res['distance'] = isset($a[0]["distance"]["value"]) ? $a[0]["distance"]["value"] : '';
                    $res['disttext'] = isset($a[0]["distance"]["text"]) ? $a[0]["distance"]["text"] : '';
                    $res['duration'] = isset($a[0]["duration"]["value"]) ? $a[0]["duration"]["value"] : '';
                    $res['duratext'] = isset($a[0]["duration"]["text"]) ? $a[0]["duration"]["text"] : '';

                    if ($res['distance'] != "" && $res['duration'] != "") {
                        $orgin = isset($data["origin_addresses"][0]) ? $data["origin_addresses"][0] : "";
                        $orgin = DB::connection()->quote($orgin);
                        $dest = isset($data["destination_addresses"][0]) ? $data["destination_addresses"][0] : "";
                        $dest = DB::connection()->quote($dest);

                        $locdata = [
                            "slat" => $lat1,
                            "slng" => $lng1,
                            "dlat" => $lat2,
                            "dlng" => $lng2,
                            "origin_address" => $orgin,
                            "destination_address" => $dest,
                            "distance" => $res['distance'],
                            "disttext" => $res['disttext'],
                            "duration" => $res['duration'],
                            "duratext" => $res['duratext']
                        ];

                        DB::table('distance_master')->insert([
                            'slat' => $lat1,
                            'slng' => $lng1,
                            'dlat' => $lat2,
                            'dlng' => $lng2,
                            'origin_address' => $orgin,
                            'destination_address' => $dest,
                            'distance' => $res['distance'],
                            'disttext' => $res['disttext'],
                            'duration' => $res['duration'],
                            'duratext' => $res['duratext']
                        ]);
                    }
                }
            }

            return $res;
        }
    }

    protected function processGoogleAPICurl($url, $returnObject = false)
    {
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->get($url);

            if ($response->successful()) {
                return $returnObject ? $response->object() : $response->json();
            } else {
                Log::error('ProcessGoogleAPICurl HTTP status: ' . $response->status() . ' ' . $response->reason());
                return [];
            }
        } catch (\Exception $e) {
            Log::error('ProcessGoogleAPICurl Error: ' . $e->getMessage());
            return [];
        }
    }

    // Placeholder for methods not provided in the original code
    public function generateWhereConditionForGeo($source)
    {
        // Implement as per original logic
        return [];
    }

    public function insertrecords($revenueAr, $chargeAr, $empty1, $chargeOfCountry, $chargeArVas)
    {
        // Implement as per original logic
        return 1;
    }

    public function addfafcharges_toorder($orderId, $revRowId, $rateRecord, $type)
    {
        // Implement as per original logic
    }

    public function getweightvolumecharges($tierRowId, $utype1, $utype2)
    {
        // Implement as per original logic
        return 0;
    }

    public function getratechargeamount_bygeotier($offering, $geoTierId, $orderId, $type, $legsLocation)
    {
        // Implement as per original logic
        return [];
    }

    public function checkOrderRevenueCurrency(array $orderIds): void
    {
        $orgId = Auth::user()->org_id ?? 0;
        $userCurrency = Auth::user()->timezone['currency'] ?? config('app.currency', 'USD');

        foreach ($orderIds as $ids) {
            $orderId = $ids['ids'] ?? null;
            if (!$orderId) {
                continue;
            }

            $revenues = Revenue::where([
                'order_id' => $orderId,
                'invoice_status' => '0',
                'status' => '1'
            ])->select('id', 'exchange_rate', 'foreign_currency')->get();

            foreach ($revenues as $rev) {
                $revenueId = $rev->id;
                $foreignCurrency = $rev->foreign_currency;

                $charges = Charge::where([
                    'revenue_id' => $revenueId,
                    'status' => '1',
                    'currency' => $foreignCurrency
                ])->where('currency', '!=', $userCurrency)
                    ->select('id', 'amount', 'currency')
                    ->get();

                foreach ($charges as $charge) {
                    $chargeRowId = $charge->id;
                    $chargeAmount = $charge->amount;
                    $chargeCurrency = $charge->currency;

                    $exchangeAmount = $rev->exchange_rate;
                    if ($exchangeAmount && $exchangeAmount != 0) {
                        $userAmount = $chargeAmount * $exchangeAmount;
                        $chargeAmount = $orgId == 1 ? round($userAmount) : round($userAmount, 2);

                        Charge::where('id', $chargeRowId)->update([
                            'local_amount' => $chargeAmount,
                            'local_currency' => $orgId == 1 ? 'PLN' : $userCurrency,
                        ]);

                        $this->updateTotalAmountForRev($revenueId);
                    }
                }
            }
        }
    }

    public function updateTotalAmountForRev(int $revenueId): void
    {
        $orgId = Auth::user()->org_id ?? 0;
        $sessionCurrency = Auth::user()->timezone['currency'] ?? config('app.currency', 'USD');
        $currency = $orgId == 1 ? 'PLN' : $sessionCurrency;

        $totalAmount = 0;
        $vatAmount = 0;

        $charges = Charge::where(['revenue_id' => $revenueId, 'status' => '1'])
            ->select('amount', 'currency', 'local_amount', 'local_currency', 'vat_percentage', 'vat_amount', 'local_vat_amount')
            ->get();

        foreach ($charges as $charge) {
            $chargeCurrency = $charge->currency;
            $chargeAmount = $charge->amount;
            $vatPercentage = $charge->vat_percentage;
            $chargeVatAmount = $charge->vat_amount;

            if ($chargeCurrency != $sessionCurrency) {
                $localAmount = $charge->local_amount;
                if (is_numeric($localAmount) && $localAmount != 0) {
                    $chargeAmount = $localAmount;
                }

                $localVatAmount = $charge->local_vat_amount;
                if (is_numeric($localVatAmount) && $localVatAmount != 0) {
                    $vatAmount = $localVatAmount;
                }
            }

            if ($vatPercentage && $chargeVatAmount > 0) {
                $totalAmount += $vatAmount;
            }

            $totalAmount += $chargeAmount;
        }

        $chargeAmount = round($totalAmount, 4);
        $rev = Revenue::where('id', $revenueId)->select('amount', 'currency', 'exchange_rate', 'foreign_currency')->first();

        if ($rev) {
            $revForeignCurrency = $rev->foreign_currency;
            $exchangeRate = $rev->exchange_rate;

            if ($orgId == 1) {
                $foreignCurrency = 'EUR';
                $currency = 'PLN';
            } else {
                $foreignCurrency = $revForeignCurrency;
                if (!$revForeignCurrency) {
                    $chargeAmount = round($chargeAmount);
                } elseif ($exchangeRate > 0) {
                    $chargeAmount = round($chargeAmount);
                }
            }

            Revenue::where('id', $revenueId)->update([
                'amount' => $chargeAmount,
                'currency' => $currency,
                'foreign_currency' => $foreignCurrency ?: null,
            ]);
        }
    }

}
