<?php

namespace App\Services\OrderList;

use App\Models\StopStatus;
use App\Models\VatMaster;
use App\Models\Revenue;
use App\Models\OrderDetail;
use App\Models\Charge;
use App\Models\XborderNotification;
use App\Models\RouteTemplate;
use App\Models\CostCenter;
use App\Models\OrderType;
use App\Models\VatCategory;
use App\Models\OrderStatus;
use App\Models\TransportMode;
use App\Models\Regions;
use App\Models\ShipmentType;
use App\Models\ServiceMaster;
use App\Models\RateExchangeSheet;
use App\Models\VroOrder;
use App\Models\Order;
use App\Models\OrderCargodetail;
use App\Models\SxGeocode;
use App\Models\OrderFileLineIdentifier;
use App\Models\OrderReference;
use App\Models\SxPartyMembers;
use App\Models\CountryMaster;
use App\Models\LocationData;
use App\Models\UniqueOrderId;
use App\Models\SxPartyTypes;
use App\Models\OrderParty;
use App\Models\User;
use App\Models\RoutingAuto;
use App\Models\ShiftVehicle;
use App\Models\ShipmentVehicleStopsleg;
use App\Models\TrucksData;
use App\Models\VehiclesDriver;
use App\Models\AssignedDriver;
use App\Models\Driver;
use App\Models\Trip;
use App\Models\Shipment;
use App\Models\TruckType;
use App\Models\ShiporderStop;
use App\Models\ShiporderStopSequence;
use App\Models\CustomerProfile;
use App\Models\Product;
use App\Models\RatepreferProduct;
use App\Models\RatepreferService;
use App\Models\RatepreferOrdertype;
use App\Models\RateOffering;
use App\Models\RateRecordCondition;
use App\Models\LanesMaster;
use App\Models\RatePreference;
use App\Models\CustomerProfileList;
use App\Models\RateRecord;
use App\Models\RateService;
use App\Models\RateServiceLanes;
use App\Models\DeliveryTerm;
use App\Models\ChargeCode;
use App\Models\CargoDetail;
use App\Models\ReferenceMaster;
use App\Models\OrderpartyAddress;
use App\Models\Status;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

use App\Services\TripCreateFromOrders;
use App\Services\RateManagement;

use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Exception;

use Maatwebsite\Excel\Facades\Excel;

class OrderProcessor
{

    protected $tripCreateFromOrders;
    protected $rateManagement;

    public function __construct(TripCreateFromOrders $tripCreateFromOrders, RateManagement $rateManagement)
    {
        $this->tripCreateFromOrders = $tripCreateFromOrders;
        $this->rateManagement = $rateManagement;
    }

    public function processOrderItems(PaginationResponse $paginationResponse, Properties $properties, array $sessionData)
    {
        $shiftIds = array_column($paginationResponse->items(), 'shift_id');
        $tripAcceptancesByShiftIds = [];

        if (!empty($shiftIds)) {
            $stopStatusResults = StopStatus::selectRaw('COUNT(*) as cnt, shipment_id')
                ->whereIn('shipment_id', $shiftIds)
                ->where('status_code', '0212')
                ->where('status_id', 10)
                ->where('shipment_id', '>', 0)
                ->groupBy('shipment_id')
                ->get();

            foreach ($stopStatusResults as $stopStatus) {
                $tripAcceptancesByShiftIds[$stopStatus->shipment_id] = $stopStatus->cnt;
            }
        }

        $order = [];
        foreach ($paginationResponse->items() as $index => $res) {
            $orderData = $this->processSingleOrderItem($res, $properties, $sessionData, $tripAcceptancesByShiftIds);
            $order[] = $orderData;
        }

        $paginationResponse->updateItems($order);
    }

    protected function processSingleOrderItem($res, Properties $properties, array $sessionData, array $tripAcceptances)
    {
        // Process dates with timezone
        $dates = $this->processOrderDates($res, $sessionData['curtz']);

        // Get reference data
        $referenceData = $properties->getReferences($res);

        // Get status information
        $orderStatus = $properties->getStatusName($res);
        $otherStatus = $properties->getOtherStatus($res);

        // Get invoice information
        $invoiceArray = $properties->getInvoices($res);

        // Vendor/carrier name logic
        $vendorName = $res['customer_name'] ?? '';
        $carrierName = (isset($res['shift_id']) && $res['shift_id'] > 0) ? $res['vendor_name'] : '';

        // VRO orders check
        $vroId = 0;
        if (isset($res['id']) && $res['id'] > 0) {
            $vroOrder = VroOrder::select('id')
                ->where('status', 1)
                ->whereRaw('? = ANY(string_to_array(order_ids, \',\')::integer[])', [$res['id']])
                ->first();

            $vroId = $vroOrder ? $vroOrder->id : 0;
        }

        // Return formatted order data
        return [
            'can_cancel' => isset($res['shift_id']) ? (!($tripAcceptances[$res['shift_id']] ?? 0)) : 0,
            'order_row_id' => $res['id'] ?? 0,
            'order_id' => $res['order_id'] ?? 0,
            'trip_id' => $res['trip_id'] ?? 0,
            'shift_id' => $res['shift_id'] ?? 0,
            'username' => $res['username'] ?? '',
            'delivery_note' => $referenceData['delivery_note'] ?? '',
            'journey_id' => $referenceData['journey_id'] ?? '',
            'job_id' => $referenceData['job_id'] ?? '',
            'transport_direction' => $referenceData['transport_direction'] ?? '',
            'pickup' => $res['pickup'] ?? '',
            'delivery' => $res['delivery'] ?? '',
            'trip_no' => isset($res['shipmentid']) ? ($res['shipmentid'] == '0' ? '' : $res['shipmentid']) : '',
            'order_status' => $orderStatus ?? '',
            'transport_mode' => $res['transport_mode'] ?? '',
            'created_at' => $res['created_at'] ?? '',
            'total_packages' => isset($res['totqty']) ? round(floatval($res['totqty'])) : 0,
            'weight' => (isset($res['totwg']) && isset($res['weight_unit'])) ? $this->calculateWeight($res['totwg'], $this->getWeightHigherUnit($res['weight_unit'] ?? '')) : 0,
            'gross_weight' => $res['weight'] ?? 0,
            'gross_weight_uom' => $res['gross_weight_uom'] ?? 0,
            'chargeable_weight' => $res['chargeable_weight'] ?? 0,
            'chargeable_weight_uom' => $res['chargeable_weight_uom'] ?? 0,
            'volume' => $res['totvol'] ?? 0,
            'second_weight' => (isset($res['second_weight']) && isset($res['secondweight_uom'])) ? ($res['second_weight'] ?? 0) . ' ' . ($res['secondweight_uom'] ?? '') : 0,
            'second_volume' => (isset($res['second_volume']) && isset($res['secondvolume_uom'])) ? ($res['second_volume'] ?? 0) . ' ' . ($res['secondvolume_uom'] ?? '') : 0,
            'org_id' => $res['org_id'] ?? 0,
            'be_value' => $res['be_value'] ?? 0,
            'otherstatus' => $otherStatus ?? '',
            'Stoppage' => $res['Stoppage'] ?? '',
            'delivery_date' => $dates['early_delivery'] ?? '',
            'pickup_date' => $dates['early_pickup'] ?? '',
            'html' => $invoiceArray['html'] ?? '',
            'container_no' => $referenceData['container_no'] ?? '',
            'purchase_order' => $referenceData['purchase_order'] ?? '',
            'vendor_name' => $vendorName ?? '',
            'vendor_id' => $res['vendor_id'] ?? '',
            'manifestno' => $referenceData['manifestno'] ?? '',
            'num_of_pallets' => $res['num_of_pallets'] ?? '',
            'tot_scanned_qty' => $res['tot_scanned_qty'] ?? '',
            'deliverycity' => $res['delivery_city'] ?? '',
            'final_delivery' => $dates['final_delivery'] ?? '',
            'loadplanconnote' => $referenceData['loadplanconnote'] ?? '',
            'created_source' => $res['created_source'] ?? '',
            'salog_ref' => $referenceData['salog_ref'] ?? '',
            'pq_ref' => $referenceData['pq_ref'] ?? '',
            'pos_ref' => $referenceData['pos_ref'] ?? '',
            'weight_unit' => isset($res['weight_unit']) ? ($this->getWeightHigherUnit($res['weight_unit'] ?? '')) : 0,
            'order_type' => $res['order_type'] ?? '',
            'customer_name' => $res['customer_name'] ?? '',
            'carrier_name' => $carrierName ?? '',
            'POD' => $res['pod'] ?? '',
            'JFR' => $invoiceArray['finaljfr'] ?? '',
            'updatedDate' => $dates['updatedDate'] ?? '',
            'source_name' => $res['source_name'] ?? '',
            'category_id' => $res['category_id'] ?? '',
            'vro_id' => $vroId ?? 0
        ];
    }

    protected function processOrderDates($res, $timezone)
    {
        $chkDate = Carbon::createFromFormat('Y-m-d H:i:s', '2020-07-01 00:00:00');
        $created_at = Carbon::parse($res['created_at'] ?? '0000-00-00');

        $dates = [
            'early_pickup' => $res['pickup_datetime'] ?? '',
            'early_delivery' => $res['delivery_datetime'] ?? '',
            'final_delivery' => $res['delivered_time'] ?? '',
            'updatedDate' => $res['updated_at'] ?? '',
        ];

        if ($created_at->greaterThan($chkDate)) {
            foreach (['early_pickup', 'early_delivery'] as $key) {
                if (!empty($dates[$key]) && $dates[$key] != '0000-00-00 00:00:00') {
                    $dates[$key] = Carbon::parse($dates[$key], 'UTC')
                        ->setTimezone($timezone)
                        ->format('Y-m-d H:i:s');
                }
            }
        }

        // Process final delivery date
        if (!empty($dates['final_delivery']) && $dates['final_delivery'] != '0000-00-00 00:00:00' && $dates['final_delivery'] != '0') {
            $dates['final_delivery'] = Carbon::parse($dates['final_delivery'], 'UTC')
                ->setTimezone($timezone)
                ->format('Y-m-d h:i A');
        }

        // Process updated date
        if (isset($res['updated_at']) && !empty($res['updated_at'])) {
            $dates['updatedDate'] = Carbon::parse($res['updated_at'], 'UTC')
                ->setTimezone($timezone)
                ->format('Y-m-d H:i:s');
        } else {
            $dates['updatedDate'] = null;
        }

        return $dates;
    }

    protected function calculateWeight($weight, string $weightUnit)
    {
        if ($weightUnit === 'tons') {
            return $weight / 1000;
        } elseif ($weightUnit === 'g') {
            return $weight * 1000;
        }

        return $weight;
    }

    protected function getWeightHigherUnit($weightUnit): string
    {
        $weightUnitArray = explode(',', $weightUnit);

        if (array_intersect(['Tons', 'tons'], $weightUnitArray)) {
            return 'tons';
        } elseif (array_intersect(['Kg', 'kg'], $weightUnitArray)) {
            return 'kg';
        } elseif (array_intersect(['G', 'Gms', 'gms', 'grm'], $weightUnitArray)) {
            return 'g';
        }

        return 'kg';
    }

    protected function emptyCheckAsnOrder($id)
    {
        $where = ['status' => 1, 'id' => $id];
        $whr = ['status' => 1, 'order_id' => $id];
        $keys = "missing fields:\n";
        $checkVal = 0;

        // Get order data
        $orderArray = $this->getAuAsnOrderData($where);

        // Get cargo data
        $cargoArray = $this->getAsnCargoDetails($whr);

        if (!empty($orderArray)) {
            // Get order array empty values
            $getOrderEm = array_filter($orderArray, fn($val) => empty($val));
            if (!empty($getOrderEm)) {
                $getOrderEmKeys = array_keys($getOrderEm);
                $keys .= implode("\n", $getOrderEmKeys) . "\n";
                $checkVal = 1;
            }
        }

        // Cargo empty field keys
        $cargoKeys = $this->cargoFieldsCheck($cargoArray);
        if (!empty($cargoKeys)) {
            $keys .= $cargoKeys;
            $checkVal = 1;
        }

        return ['keys' => $keys, 'check_val' => $checkVal];
    }

    protected function getAuAsnOrderData($where)
    {
        $result = Order::select([
            'id',
            'order_id',
            'pickup_company as Shipper Name',
            'delivery_company as Consignee Name',
            'pickup_country as Shipper Country',
            'delivery_country as Consignee Country',
            'pickup_city as Shipper City',
            'delivery_city as Consignee City',
            'pickup_pincode as Shipper Zipcode',
            'delivery_pincode as Consignee Zipcode',
            'pickup_address1 as Shipper Address',
            'delivery_address1 as Consignee Address',
            'pickup_address2 as Shipper State',
            'delivery_address2 as Consignee State',
        ])
            ->where($where)
            ->first();

        return $result ? $result->toArray() : [];
    }

    protected function getAsnCargoDetails($whr)
    {
        $result = OrderCargodetail::select(['length', 'width', 'height', 'weight', 'volume', 'quantity'])
            ->where($whr)
            ->get();

        return $result->toArray();
    }

    protected function cargoFieldsCheck($cargoArray)
    {
        $keys = '';
        $cargoEmptyVal = [];

        if (!empty($cargoArray)) {
            foreach ($cargoArray as $cargo) {
                $emptyFields = array_filter($cargo, fn($val) => empty($val) || $val <= 0);
                if (!empty($emptyFields)) {
                    $cargoEmptyVal[] = array_keys($emptyFields);
                }
            }

            if (!empty($cargoEmptyVal)) {
                $singleArray = array_merge(...$cargoEmptyVal);
                $cargoResult = array_unique($singleArray);
                $keys .= implode("\n", $cargoResult);
            }
        }

        return $keys;
    }

    public function processOrderDatetimes(?string $early_time, ?string $late_time): array
    {
        $early_formatted = !empty($early_time) && $early_time !== '0000-00-00 00:00:00'
            ? date('Y-m-d H:i:s', strtotime($early_time))
            : now()->format('Y-m-d H:i:s');

        $late_formatted = !empty($late_time) && $late_time !== '0000-00-00 00:00:00'
            ? date('Y-m-d H:i:s', strtotime($late_time))
            : now()->addHour()->format('Y-m-d H:i:s');

        return [
            'early' => $early_formatted,
            'late' => $late_formatted
        ];
    }

    public function getDateTimeByTimezone(string $uzone, ?string $dt, string $dzone): array
    {
        if ($dt) {
            $res['date'] = date('Y-m-d', strtotime($dt));
            $res['time'] = date('H:i:s', strtotime($dt));
            $res['datetime'] = date('Y-m-d H:i:s', strtotime($dt));

            if ($uzone && $dzone && $uzone !== $dzone) {
                $userTimezone = new DateTimeZone($uzone);
                $dbTimezone = new DateTimeZone($dzone);
                $currDate = new DateTime($res['datetime'], $dbTimezone);
                $currDate->setTimezone($userTimezone);
                $res['datetime'] = $currDate->format('Y-m-d H:i:s');
                $res['date'] = $currDate->format('Y-m-d');
                $res['time'] = $currDate->format('H:i:s');
            }
        } else {
            $res['date'] = now()->format('Y-m-d');
            $res['time'] = now()->format('H:i:s');
            $res['datetime'] = now()->format('Y-m-d H:i:s');
        }

        return $res;
    }

    public function checkgeocode(array $location): array
    {
        // Normalize country codes
        if (strtolower($location['country']) === 'russia' || in_array($location['country'], ['РОССИЯ', 'Россия'])) {
            $location['country'] = 'RU';
        }
        if (strtolower($location['country']) === 'australia' || strtoupper($location['country']) === 'AU') {
            $location['country'] = 'AU';
        }

        $geocode = SxGeocode::where([
            'country' => $location['country'],
            'postal_code' => $location['zipcode'],
            'status' => 1,
        ])->select('postal_code', 'district', 'city', 'province', 'country', 'region')->first();

        if ($geocode) {
            return [
                'country' => $geocode->country,
                'state' => $geocode->province,
                'city' => $geocode->city,
                'region' => $geocode->region,
                'zipcode' => $geocode->postal_code,
            ];
        }

        $geocode = SxGeocode::where([
            'country' => $location['country'],
            'city' => $location['city'],
            'status' => 1,
        ])->select('postal_code', 'district', 'city', 'province', 'country', 'region')->first();

        return $geocode ? [
            'country' => $geocode->country,
            'state' => $geocode->province,
            'city' => $geocode->city,
            'region' => $geocode->region,
            'zipcode' => $geocode->postal_code,
        ] : [];
    }

    public function getPartyDetailsOptimized($party_id, array $fields = ['name', 'code', 'location_id as city', 'street', 'state', 'address', 'country', 'pincode', 'latitude', 'longitude', 'mobile']): ?object
    {
        if (empty($party_id) || $party_id == "0") {
            return null;
        }

        return SxPartyMembers::where('id', $party_id)
            ->where('status', 1)
            ->select($fields)
            ->first();
    }

    public function getLatLngsByPlace(string $address): array
    {
        $address = trim($address);
        $cacheKey = 'latlong:' . hash('sha256', $address);

        // Check cache
        $cachedResults = Cache::get($cacheKey);
        if ($cachedResults) {
            return $cachedResults;
        }

        // Process address
        $addressArray = explode(',', $address);
        $arrayCount = count($addressArray);
        $arrayCountry = trim($addressArray[$arrayCount - 2] ?? '');

        if (strlen($arrayCountry) < 4 && $arrayCountry !== '') {
            $country = CountryMaster::where('status', 1)
                ->where(function ($query) use ($arrayCountry) {
                    $query->where('country_name', $arrayCountry)
                        ->orWhere('country_code', $arrayCountry);
                })
                ->value('country_name');

            if ($country) {
                $addressArray[$arrayCount - 2] = $country;
                $address = implode(',', $addressArray);
            }
        }

        // Check database
        $databaseResults = LocationData::where('source', $address)
            ->select('name1', 'lat', 'lng', 'country')
            ->first();

        if ($databaseResults) {
            $results = [
                $databaseResults->lat,
                $databaseResults->lng,
                $databaseResults->name1,
                $databaseResults->country,
            ];
            Cache::put($cacheKey, $results, now()->addHour());
            return $results;
        }

        // Google Maps API
        $apiKey = config('services.google_maps.api_key');
        if (!$apiKey) {
            return ['', '', '', ''];
        }

        $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', [
            'address' => $address,
            'key' => $apiKey,
        ]);

        $apiResults = $response->json();
        if (empty($apiResults) || $apiResults['status'] !== 'OK') {
            return ['', '', '', ''];
        }

        $googleLat = $apiResults['results'][0]['geometry']['location']['lat'];
        $googleLng = $apiResults['results'][0]['geometry']['location']['lng'];
        $googleFormattedAddress = $apiResults['results'][0]['formatted_address'];
        $googleShortAddress = '';
        $country = '';

        foreach ($apiResults['results'][0]['address_components'] as $component) {
            if (in_array('route', $component['types'])) {
                $googleShortAddress = $component['long_name'];
            } elseif (in_array('country', $component['types'])) {
                $country = $component['short_name'];
            }
        }

        LocationData::create([
            'location_name' => '',
            'name1' => $googleFormattedAddress,
            'name2' => $googleShortAddress,
            'country' => $country,
            'lat' => (string)$googleLat,
            'lng' => (string)$googleLng,
            'source' => $address,
        ]);

        $results = [$googleLat, $googleLng, $googleFormattedAddress, $country];
        Cache::put($cacheKey, $results, now()->addHour());

        return $results;
    }

    public function generateBookingId(array $orderinfo): string
    {
        $country_code = $orderinfo['country_code'] ?? '';
        $org_id = $orderinfo['org_id'] ?? '';
        $order_id = $orderinfo['order_id'] ?? 0;
        $user_id = $orderinfo['user_id'] ?? 0;

        $tz = $this->getUserTimeZone($country_code);
        $phone_code = $tz['phone_code'] ?? '';
        $cdate = new DateTime('now', new DateTimeZone('UTC'));
        $year = $cdate->format('y');
        $week = $cdate->format('W');

        if ($org_id === 'KNAU') {
            $codeyear = $phone_code . $week;
        } else {
            $codeyear = $phone_code . $year;
        }

        $companyCondition = match ($org_id) {
            'NZKN', 'NZPG' => "(org_id LIKE '%NZPG%' OR org_id LIKE '%NZKN%')",
            'THKN', 'THCL' => "(org_id LIKE '%THCL%' OR org_id LIKE '%THKN%')",
            default => "org_id = '$org_id'",
        };

        $whr = "id < '$order_id' AND (order_id LIKE '$codeyear%' AND $companyCondition)";
        $previd = Order::select('order_id')->whereRaw($whr)->orderBy('id', 'DESC')->first();

        if ($previd) {
            $week_orderno = $previd->order_id;
            $cn_length = strlen($phone_code);
            $wcount = $org_id === 'KNAU' ? $cn_length + 2 : $cn_length + 2;
            $ycount = $wcount + 2;
            $prev_weekno = mb_substr($week_orderno, $cn_length, 2);
            $prev_ordno = mb_substr($week_orderno, $ycount, 5);
            $id = ltrim($prev_ordno, '0');

            if ($prev_weekno < $week) {
                $id = '0001';
            } else {
                $i_id = (int)$id + 1;
                $id = str_pad($i_id, 4, '0', STR_PAD_LEFT);
            }

            $booking_id = $org_id === 'KNAU'
                ? $phone_code . $week . $year . $id
                : $phone_code . $year . $week . $id;

            $chk = Order::where('order_id', $booking_id)->exists();
            if ($chk) {
                $iid = (int)$id + 1;
                $ii_d = str_pad($iid, 4, '0', STR_PAD_LEFT);
                $booking_id = $org_id === 'KNAU' || $org_id === 'UKKN-CG'
                    ? $phone_code . $week . $year . $ii_d
                    : $phone_code . $year . $week . $ii_d;
            }
        } else {
            $id = '0001';
            $booking_id = $org_id === 'KNAU'
                ? $phone_code . $week . $year . $id
                : $phone_code . $year . $week . $id;
        }

        // Save unique order ID
        try {
            UniqueOrderId::create(['order_id' => $booking_id]);
        } catch (Exception $e) {
            $booking_id = $this->getUniqueOrderId($booking_id);
        }

        return $booking_id;
    }

    public function getUserTimeZone(?string $cmpcode = null): array
    {
        $res = [
            'currency' => 'SGD',
            'timezone' => 'Asia/Singapore',
            'hrs' => '+08.00',
            'country' => 'SG',
            'phone_code' => '65'
        ];

        if (!empty($cmpcode)) {
            $country = CountryMaster::where('country_code', $cmpcode)
                ->where('status', 1)
                ->select('country_code', 'cntry_timezone', 'cntry_hrs', 'currency', 'phone_code')
                ->first();

            if ($country) {
                $res = [
                    'currency' => $country->currency,
                    'timezone' => $country->cntry_timezone,
                    'hrs' => $country->cntry_hrs,
                    'country' => $country->country_code,
                    'phone_code' => $country->phone_code
                ];
            }
        }

        return $res;
    }

    public function getUniqueOrderId(string $orderId, int $tries = 0): ?string
    {
        try {
            UniqueOrderId::create(['order_id' => $orderId]);
            return $orderId;
        } catch (Exception $e) {
            if ($tries >= 2) {
                throw new Exception('Maximum retries for generating a unique orderId reached');
            }
            $tries++;
            $newOrderId = $this->nextUniqueOrderId($orderId);
            return $this->getUniqueOrderId($newOrderId, $tries);
        }
    }

    private function nextUniqueOrderId(string $orderId): string
    {
        $numericPart = (int)substr($orderId, -4) + 1;
        $paddedNumeric = str_pad($numericPart, 4, '0', STR_PAD_LEFT);
        return substr($orderId, 0, -4) . $paddedNumeric;
    }

    public function gettblrowdata(array $where, array $select, string $table): array
    {
        $result = DB::table($table)
            ->select($select)
            ->where($where)
            ->limit(1)
            ->first();

        return $result ? (array)$result : [];
    }

    public function insertTableData(string $tablename, array $data): int
    {
        $id = DB::table($tablename)->insertGetId($data);
        return $id ?: 0;
    }

    public function updatetbledata(string $tablename, array $set, array $where): bool
    {
        DB::table($tablename)->where($where)->update($set);
        return true;
    }

    public function deleteForOrder(int $orderId, array $referenceIds = []): bool
    {
        $query = DB::table('order_references')
            ->where('order_id', $orderId)
            ->where('status', '1');

        if (!empty($referenceIds)) {
            $query->whereIn('reference_id', $referenceIds);
        }

        return $query->update([
            'status' => 0,
            'updatedon' => now()->format('Y-m-d H:i:s')
        ]) > 0;
    }

    public function deleteOrderById(int $id): bool
    {
        return DB::table('orders')
            ->where('id', $id)
            ->update(['status' => 0]) > 0;
    }

    public function insertOrdersRefFileLineIdentifier(array $info): void
    {
        $getRefData = OrderFileLineIdentifier::where([
            'source_city' => $info['pickupCity'],
            'source_suburb' => $info['pickupState'],
            'source_country' => $info['pickupCountry'],
            'destination_city' => $info['dropCity'],
            'destination_suburb' => $info['dropState'],
            'destination_country' => $info['dropCountry'],
            'org_id' => $info['org_id'],
            'status' => 1,
        ])->first(['ref_value']);

        if ($getRefData) {
            $checkReference = OrderReference::where([
                'order_id' => $info['orderRowId'],
                'reference_id' => 'FI',
            ])->first(['id']);

            if (!$checkReference) {
                OrderReference::create([
                    'order_id' => $info['orderRowId'],
                    'reference_id' => 'FI',
                    'ref_value' => $getRefData->ref_value,
                    'status' => 1,
                    'created_at' => $info['date'],
                ]);
            } else {
                $checkReference->update([
                    'order_id' => $info['orderRowId'],
                    'reference_id' => 'FI',
                    'ref_value' => $getRefData->ref_value,
                ]);
            }
        }
    }

    public function subcustpartiesinsert($orderId, $bookingId, $data, $orgId, $beValue, $userId, $cdate): void
    {
        $names = ['shipper', 'consignee', 'pickup', 'delivery'];

        foreach ($data as $i => $partyId) {
            if ($partyId && $i < 4) {
                $partyTypeData = SxPartyTypes::where([
                    'type_name' => $names[$i],
                    'status' => 1,
                    'org_id' => $orgId
                ])->first();

                $partyType = $partyTypeData ? $partyTypeData->id : SxPartyTypes::create([
                    'type_name' => $names[$i],
                    'description' => $names[$i],
                    'org_id' => $orgId,
                    'be_value' => $beValue,
                    'status' => 1,
                ])->id;

                $existingParties = OrderParty::where([
                    'order_id' => $orderId,
                    'status' => 1,
                ])->get();

                foreach ($existingParties as $existingParty) {
                    $existingPartyType = SxPartyTypes::where('id', $existingParty->party_type)->value('type_name');
                    if ($existingPartyType && strtoupper($existingPartyType) === strtoupper($names[$i])) {
                        if ($existingParty->party_id != $partyId) {
                            $existingParty->update(['status' => false]);
                        }
                    }
                }

                $existingParty = OrderParty::where([
                    'order_id' => $orderId,
                    'party_id' => $partyId,
                    'party_type' => $partyType,
                    'status' => 1,
                ])->first();

                if (!$existingParty) {
                    $checkParty = OrderParty::where([
                        'order_id' => $orderId,
                        'party_id' => $partyId,
                        'status' => 1,
                    ])->first();

                    if ($checkParty) {
                        $checkPartyTypeName = SxPartyTypes::where('id', $checkParty->party_type)->value('type_name');
                        if (!$checkPartyTypeName || strtoupper($checkPartyTypeName) !== strtoupper($names[$i])) {
                            OrderParty::create([
                                'order_id' => $orderId,
                                'be_value' => $beValue,
                                'user_id' => $userId,
                                'order_number' => $bookingId,
                                'party_id' => $partyId,
                                'party_type' => $partyType,
                                'status' => 1,
                                'created_at' => $cdate,
                            ]);
                        }
                    } else {
                        OrderParty::create([
                            'order_id' => $orderId,
                            'be_value' => $beValue,
                            'user_id' => $userId,
                            'order_number' => $bookingId,
                            'party_id' => $partyId,
                            'party_type' => $partyType,
                            'status' => 1,
                            'created_at' => $cdate,
                        ]);
                    }
                }
            }
        }
    }

    public function getcust_routeautomate($usr, $location)
    {
        $orderId = isset($location['cargo']['order_id']) ? $location['cargo']['order_id'] : 0;

        $cargoInfo = OrderCargodetail::where([
            'order_id' => $orderId,
            'status' => 1,
        ])->select([
            'id',
            'order_id',
            'cargo_id',
            'handling_unit',
            'length',
            'width',
            'height',
            'weight',
            'second_weight',
            'volumetric_weight',
            'volweight_uom',
            'ldm',
            'volume',
            'second_volume',
            'quantity',
            'scanned_quantity',
            'quantity_type',
            'cargo_content',
        ])->get();

        $weight = 0;
        $secondWeight = 0;
        $volume = 0;
        $secondVolume = 0;

        foreach ($cargoInfo as $row) {
            $weight += $row->weight;
            $secondWeight += $row->second_weight ?? 0;
            $volume += $row->volume;
            $secondVolume += $row->second_volume ?? 0;
        }

        $cargoDetails = [
            'weight' => $weight,
            'second_weight' => $secondWeight,
            'volume' => $volume,
            'second_volume' => $secondVolume,
        ];

        $res = [];
        $shipIdentifier = $consIdentifier = $notifyIdentifier = '';
        $stopType = $location['stoptype'];

        $routes = RoutingAuto::where([
            'cust_id' => $usr,
            'triggered_type' => '2',
            'status' => 1,
        ])->select([
            'pick_type',
            'pick_val',
            'deli_type',
            'deli_val',
            'ship_identifier',
            'cons_identifier',
            'notify_identifier',
            'carrier_id',
            'vehicle_type',
            'vehicle_id',
            'driver_id',
            'template_id',
            'min_weight',
            'max_weight',
            'min_weight_uom',
            'max_weight_uom',
            'min_volume',
            'max_volume',
            'min_volume_uom',
            'max_volume_uom',
            'min_weight_ac',
            'max_weight_ac',
            'min_weight_uom_ac',
            'max_weight_uom_ac',
            'min_volume_ac',
            'max_volume_ac',
            'min_volume_uom_ac',
            'max_volume_uom_ac',
        ])->get();

        foreach ($routes as $rw) {
            $shipIdentifier = $rw->ship_identifier;
            $consIdentifier = $rw->cons_identifier;
            $notifyIdentifier = $rw->notify_identifier;

            $constraints = ($rw->min_weight == 0.00 && $rw->max_weight == 0.00 &&
                $rw->min_weight_ac == 0.00 && $rw->max_weight_ac == 0.00 &&
                $rw->min_volume == 0.00 && $rw->max_volume == 0.00 &&
                $rw->min_volume_ac == 0.00 && $rw->max_volume_ac == 0.00) ? 'yes' : 'no';

            $locType = $stopType == 'P' ? 'pick_type' : 'deli_type';
            $locVal = $stopType == 'P' ? 'pick_val' : 'deli_val';

            if ($rw->$locType == 'Country') {
                if (!empty($location['country']) && strtoupper($location['country']) == strtoupper($rw->$locVal)) {
                    $checkAuto = $this->checkcargofir_autorout($cargoDetails, $rw);
                    if ($checkAuto == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    }
                }
                if (empty($res) && isset($location['order_country']) && !empty($location['order_country']) && strtoupper($location['order_country']) == strtoupper($rw->$locVal)) {
                    $checkAuto = $this->checkcargofir_autorout($cargoDetails, $rw);
                    if ($checkAuto == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    }
                }
            } elseif ($rw->$locType == 'State') {
                if (!empty($location['state']) && strtoupper($location['state']) == strtoupper($rw->$locVal)) {
                    $checkAuto = $this->checkcargofir_autorout($cargoDetails, $rw);
                    if ($checkAuto == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    }
                    break;
                }
            } elseif ($rw->$locType == 'City') {
                if (!empty($location['city']) && strtoupper($location['city']) == strtoupper($rw->$locVal)) {
                    $checkAuto = $this->checkcargofir_autorout($cargoDetails, $rw);
                    if ($checkAuto == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    }
                }
            } elseif ($rw->$locType == 'Zipcode') {
                if (!empty($location['zipcode']) && strtoupper($location['zipcode']) == strtoupper($rw->$locVal)) {
                    $checkAuto = $this->checkcargofir_autorout($cargoDetails, $rw);
                    if ($checkAuto == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                    }
                    break;
                }
            } elseif ($rw->$locType == 'Location ID' || $rw->$locType == 'Region') {
                $x = 0;
                if (!empty($location['region']) && strtoupper($location['region']) == strtoupper($rw->$locVal)) {
                    $checkAuto = $this->checkcargofir_autorout($cargoDetails, $rw);
                    if ($checkAuto == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                        $x = 1;
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrierId' => $rw->carrier_id,
                            'vehicleType' => $rw->vehicle_type,
                            'vehicleId' => $rw->vehicle_id,
                            'driverId' => $rw->driver_id,
                            'templateId' => $rw->template_id,
                        ];
                        $x = 1;
                    }
                }
                if ($x == 0 && isset($location['order_city'])) {
                    $getRegionCity = SxGeocode::where([
                        'city' => $location['order_city'],
                        'region' => strtoupper($rw->$locVal),
                        'status' => 1,
                    ])->select('id')->exists();
                    if ($getRegionCity) {
                        $checkAuto = $this->checkcargofir_autorout($cargoDetails, $rw);
                        if ($checkAuto == 'yes') {
                            $res = [
                                'carrierId' => $rw->carrier_id,
                                'vehicleType' => $rw->vehicle_type,
                                'vehicleId' => $rw->vehicle_id,
                                'driverId' => $rw->driver_id,
                                'templateId' => $rw->template_id,
                            ];
                            $x = 1;
                        } elseif ($constraints == 'yes') {
                            $res = [
                                'carrierId' => $rw->carrier_id,
                                'vehicleType' => $rw->vehicle_type,
                                'vehicleId' => $rw->vehicle_id,
                                'driverId' => $rw->driver_id,
                                'templateId' => $rw->template_id,
                            ];
                            $x = 1;
                        }
                    }
                }
                if ($x == 0 && isset($location['order_zipcode'])) {
                    $getRegionZipcode = SxGeocode::where([
                        'postal_code' => $location['order_zipcode'],
                        'region' => strtoupper($rw->$locVal),
                        'status' => 1,
                    ])->select('id')->exists();
                    if ($getRegionZipcode) {
                        $checkAuto = $this->checkcargofir_autorout($cargoDetails, $rw);
                        if ($checkAuto == 'yes') {
                            $res = [
                                'carrierId' => $rw->carrier_id,
                                'vehicleType' => $rw->vehicle_type,
                                'vehicleId' => $rw->vehicle_id,
                                'driverId' => $rw->driver_id,
                                'templateId' => $rw->template_id,
                            ];
                            $x = 1;
                        } elseif ($constraints == 'yes') {
                            $res = [
                                'carrierId' => $rw->carrier_id,
                                'vehicleType' => $rw->vehicle_type,
                                'vehicleId' => $rw->vehicle_id,
                                'driverId' => $rw->driver_id,
                                'templateId' => $rw->template_id,
                            ];
                            $x = 1;
                        }
                    }
                }
            }
            if (array_filter([$shipIdentifier, $consIdentifier, $notifyIdentifier])) {
                if (!$this->checkIdentifier($location, $shipIdentifier, $consIdentifier, $notifyIdentifier)) {
                    return [];
                }
            }
            if (!empty($res)) {
                break;
            }
        }

        return $res;
    }

    protected function checkcargofir_autorout($data, $rw)
    {
        $minWeight = $rw->min_weight;
        $maxWeight = $rw->max_weight;
        $minWeightAc = $rw->min_weight_ac;
        $maxWeightAc = $rw->max_weight_ac;
        $minVolume = $rw->min_volume;
        $maxVolume = $rw->max_volume;
        $minVolumeAc = $rw->min_volume_ac;
        $maxVolumeAc = $rw->max_volume_ac;

        $weightC = $this->checkwwhite($data, $rw);
        $weightCAc = $this->checkwwhite_ac($data, $rw);
        $volumeC = $this->checkvolume($data, $rw);
        $volumeCAc = $this->checkvolume_sc($data, $rw);

        if ($weightC == 'yes' && $weightCAc == 'yes' && $volumeC == 'yes' && $volumeCAc == 'yes') {
            return 'yes';
        }
        if ($volumeCAc == 'yes' && $volumeC == 'yes' && $weightCAc == 'yes' && $weightC != 'false') {
            return 'yes';
        }
        if ($volumeCAc == 'yes' && $volumeC == 'yes' && $weightC == 'yes' && $weightCAc != 'false') {
            return 'yes';
        }
        if ($volumeCAc == 'yes' && $volumeC == 'yes' && $weightC != 'false' && $weightCAc != 'false') {
            return 'yes';
        }
        if ($volumeCAc == 'yes' && $weightCAc == 'yes' && $weightC == 'yes' && $volumeC != 'false') {
            return 'yes';
        }
        if ($volumeCAc == 'yes' && $weightCAc == 'yes' && $volumeC != 'false' && $weightC != 'false') {
            return 'yes';
        }
        if ($volumeCAc == 'yes' && $weightC == 'yes' && $weightCAc != 'false' && $volumeC != 'false') {
            return 'yes';
        }
        if ($volumeCAc == 'yes' && $weightC != 'false' && $weightCAc != 'false' && $volumeC != 'false') {
            return 'yes';
        }
        if ($volumeC == 'yes' && $weightCAc == 'yes' && $weightC == 'yes' && $volumeCAc != 'false') {
            return 'yes';
        }
        if ($volumeC == 'yes' && $weightCAc == 'yes' && $volumeCAc != 'false' && $weightC != 'false') {
            return 'yes';
        }
        if ($volumeC == 'yes' && $weightC == 'yes' && $weightCAc != 'false' && $volumeCAc != 'false') {
            return 'yes';
        }
        if ($volumeC == 'yes' && $weightC != 'false' && $weightCAc != 'false' && $volumeCAc != 'false') {
            return 'yes';
        }
        if ($weightCAc == 'yes' && $weightC == 'yes' && $volumeC != 'false' && $volumeCAc != 'false') {
            return 'yes';
        }
        if ($weightCAc == 'yes' && $weightC != 'false' && $volumeC != 'false' && $volumeCAc != 'false') {
            return 'yes';
        }
        if ($weightC == 'yes' && $weightCAc != 'false' && $volumeC != 'false' && $volumeCAc != 'false') {
            return 'yes';
        }

        return 'no';
    }

    protected function checkwwhite($data, $rw)
    {
        if ($rw->min_weight >= 0 && $rw->max_weight >= 0) {
            if ($data['second_weight'] >= $rw->min_weight && $data['second_weight'] <= $rw->max_weight) {
                return 'yes';
            } else {
                return 'false';
            }
        }
        return 'no';
    }

    protected function checkwwhite_ac($data, $rw)
    {
        if ($rw->min_weight_ac >= 0 && $rw->max_weight_ac >= 0) {
            if ($data['weight'] >= $rw->min_weight_ac && $data['weight'] <= $rw->max_weight_ac) {
                return 'yes';
            } else {
                return 'false';
            }
        }
        return 'no';
    }

    protected function checkvolume($data, $rw)
    {
        if ($rw->min_volume >= 0 && $rw->max_volume >= 0) {
            if ($data['second_volume'] >= $rw->min_volume && $data['second_volume'] <= $rw->max_volume) {
                return 'yes';
            } else {
                return 'false';
            }
        }
        return 'no';
    }

    protected function checkvolume_sc($data, $rw)
    {
        if ($rw->min_volume_ac >= 0 && $rw->max_volume_ac >= 0) {
            if ($data['volume'] >= $rw->min_volume_ac && $data['volume'] <= $rw->max_volume_ac) {
                return 'yes';
            } else {
                return 'false';
            }
        }
        return 'no';
    }

    protected function checkIdentifire(array $orderDetails, string $shipperIdentifier, string $consigneeIdentifier, string $notifyIdentifier): bool
    {
        $parties = OrderParty::select([
            'order_parties.id',
            'order_parties.order_id',
            'order_parties.party_id',
            'order_parties.party_type',
            'sx_party_types.type_name as name',
            'sx_party_members.code',
        ])
            ->join('sx_party_types', 'order_parties.party_type', '=', 'sx_party_types.id')
            ->join('sx_party_members', 'order_parties.party_id', '=', 'sx_party_members.id')
            ->where([
                'order_parties.order_id' => $orderDetails['cargo']['order_id'],
                'order_parties.status' => true,
            ])
            ->get();

        if ($parties->isEmpty()) {
            return false;
        }

        $shipperCode = $consigneeCode = $notifyCode = '';
        foreach ($parties as $row) {
            switch (strtolower($row->name)) {
                case 'customer':
                    $customerCode = $row->code;
                    break;
                case 'shipper':
                    $shipperCode = $row->code;
                    break;
                case 'consignee':
                    $consigneeCode = $row->code;
                    break;
                case 'notify_party':
                    $notifyCode = $row->code;
                    break;
            }
        }

        if ($shipperIdentifier && $consigneeIdentifier && $notifyIdentifier) {
            return $shipperIdentifier === $shipperCode && $consigneeIdentifier === $consigneeCode && $notifyIdentifier === $notifyCode;
        }
        if ($shipperIdentifier && $consigneeIdentifier && empty($notifyIdentifier)) {
            return $shipperIdentifier === $shipperCode && $consigneeIdentifier === $consigneeCode;
        }
        if ($shipperIdentifier && empty($consigneeIdentifier) && empty($notifyIdentifier)) {
            return $shipperIdentifier === $shipperCode;
        }
        if (empty($shipperIdentifier) && $consigneeIdentifier && $notifyIdentifier) {
            return $consigneeIdentifier === $consigneeCode && $notifyIdentifier === $notifyCode;
        }
        if (empty($shipperIdentifier) && empty($consigneeIdentifier) && $notifyIdentifier) {
            return $notifyIdentifier === $notifyCode;
        }
        if (empty($shipperIdentifier) && $consigneeIdentifier && empty($notifyIdentifier)) {
            return $consigneeIdentifier === $consigneeCode;
        }
        if ($shipperIdentifier && empty($consigneeIdentifier) && $notifyIdentifier) {
            return $shipperIdentifier === $shipperCode && $notifyIdentifier === $notifyCode;
        }

        return false;
    }

    public function createShipmentByOrder($vendorInfo, $orderInfo)
    {
        $user = Auth::user();
        $response = 0;
        $userId = isset($orderInfo['user_id']) ? $orderInfo['user_id'] : $user->user_id;
        $orgId = isset($orderInfo['org_id']) ? $orderInfo['org_id'] : $user->org_id;
        $curtz = $user->usr_tzone['timezone'] ?? 'UTC';

        if (!$curtz) {
            $user = User::where('id', $userId)->select('country_code')->first();
            if ($user && $user->country_code) {
                $country = CountryMaster::where('country_code', $user->country_code)
                    ->orWhere('country_name', $user->country_code)
                    ->where('status', 1)
                    ->select('cntry_timezone')
                    ->first();
                $curtz = $country ? $country->cntry_timezone : 'UTC';
            }
        }

        $logDate = Carbon::now()->toDateTimeString();
        $currentDateTime = $this->getDateTimeByTimezone('UTC', $logDate, $curtz)['datetime'];
        $currentDateTime1 = Carbon::now()->toDateTimeString();

        $countCode = substr($orgId, 0, 2);
        $orderId = $orderInfo['id'];
        $orderNumber = $orderInfo['order_id'];
        $order = Order::where('id', $orderId)->first();
        $seqNum = substr($orderNumber, -6);
        $shipId = "T{$countCode}" . date('W') . date('y') . $seqNum;
        $vendorId = $vendorInfo['carrier_id'];
        $vehicleId = $vendorInfo['vehicle_id'] ?? 0;
        $driverId = $vendorInfo['driver_id'] ?? 0;
        $vehicleType = $vendorInfo['vehicle_type'] ?? 0;
        $templateId = $vendorInfo['template_id'] ?? 0;

        if ($templateId > 0) {
            // Placeholder for tripCreateFromOrders
            // $response = app('tripCreateFromOrders')->createTripByTripTemplate([
            //     'orderId' => $orderId,
            //     'templateId' => $templateId,
            //     'tripType' => 'Single'
            // ]);
            // $orderIds = $response['orderIds'];
            // $orderShiftIds = $response['orderShiftIds'];
        } else {
            if ($shipId && $vendorId > 0 && $orderId > 0 && $order) {
                $pickup = $order->pickup_address1 ?: $order->pickup_city;
                $delivery = $order->delivery_address1 ?: $order->delivery_city;

                $address = $dropAddr = '';
                foreach (['pickup_address1', 'pickup_company', 'pickup_country', 'pickup_city', 'pickup_pincode'] as $field) {
                    if ($order->$field) {
                        $address .= ($address ? ', ' : '') . $order->$field;
                    }
                }
                foreach (['delivery_address1', 'delivery_company', 'delivery_country', 'delivery_city', 'delivery_pincode'] as $field) {
                    if ($order->$field) {
                        $dropAddr .= ($dropAddr ? ', ' : '') . $order->$field;
                    }
                }

                $shipment = Shipment::where('shipmentid', $shipId)
                    ->select('id', 'splace', 'slat', 'slng', 'eplace', 'elat', 'elng', 'vendor_id', 'weight', 'volume', 'units', 'vehicle_type')
                    ->first();

                if (!$shipment) {
                    $vehicleTypeName = 'Truck';
                    if ($vehicleType) {
                        $truckType = TruckType::where('id', $vehicleType)->select('trucktype')->first();
                        if ($truckType) {
                            $vehicleTypeName = $truckType->trucktype;
                        } else {
                            $orgTruckType = TruckType::where('org_id', $order->org_id)->where('status', 'Active')->select('trucktype')->first();
                            $vehicleTypeName = $orgTruckType ? $orgTruckType->trucktype : 'Truck';
                        }
                    } else {
                        $orgTruckType = TruckType::where('org_id', $order->org_id)->where('status', 'Active')->select('trucktype')->first();
                        $vehicleTypeName = $orgTruckType ? $orgTruckType->trucktype : 'Truck';
                    }

                    $startDate = Carbon::parse($order->pickup_datetime);
                    $startTime = $startDate->format('H:i');
                    $endDate = Carbon::parse($order->delivery_datetime);
                    $endTime = $endDate->format('H:i');
                    $endDateTz = $this->getDateTimeByTimezone('UTC', $endDate, $curtz)['datetime'];

                    $shipmentData = [
                        'user_id' => $userId,
                        'stime' => $startTime,
                        'etime' => $endTime,
                        'splace' => $order->pickup_city,
                        'slat' => $order->plat,
                        'slng' => $order->plng,
                        'eplace' => $order->delivery_city,
                        'elat' => $order->dlat,
                        'elng' => $order->dlng,
                        'scity' => $order->pickup_city,
                        'dcity' => $order->delivery_city,
                        'zone_id' => 1,
                        'empshift_start' => $startTime,
                        'empshift_end' => $endTime,
                        'trip_type' => 0,
                        'startdate' => $startDate,
                        'enddate' => $endDateTz,
                        'shipment_name' => 'Boxes',
                        'shipment_id' => 0,
                        'transport_mode' => $order->transport_mode,
                        'customer_id' => $order->customer_id,
                        'vendor_id' => $vendorId,
                        'carrier_type' => 0,
                        'shipmentid' => $shipId,
                        'txnid' => $shipId,
                        'weight' => $order->weight,
                        'volume' => $order->volume,
                        'units' => $order->quantity,
                        'domainname' => 'INFD',
                        'vehicle_type' => $vehicleTypeName,
                        'org_id' => $order->org_id,
                        'be_value' => $order->be_value,
                        'status' => 1,
                        'created_at' => $currentDateTime,
                        'updated_at' => $currentDateTime,
                    ];

                    $shipIdDb = Shipment::create($shipmentData)->id;
                } else {
                    $shipIdDb = $shipment->id;
                }

                Order::where('id', $orderId)->update([
                    'vendor_id' => $vendorId,
                    'shift_id' => $shipIdDb,
                    'shipmentid' => $shipId,
                    'status' => 2,
                ]);

                $this->tripCreateFromOrders->makeOrderInvolvedPartiesType($vendorId, $orderId, 'CARRIER');

                $pickupStop = ShiporderStop::where('shipment_id', $shipIdDb)
                    ->where('stopcity', $order->pickup_city)
                    ->where('stoptype', 'P')
                    ->select('id')
                    ->first();

                if (!$pickupStop) {
                    $pickupStopData = [
                        'stopname' => $order->pickup_city,
                        'plat' => $order->plat,
                        'plng' => $order->plng,
                        'stopcity' => $order->pickup_city,
                        'address' => $address,
                        'stoptype' => 'P',
                        'stopstatus' => 0,
                        'shipment_id' => $shipIdDb,
                        'ordernumber' => 1,
                        'startdate' => $order->pickup_datetime,
                        'enddate' => $order->pickup_endtime,
                        'weight' => $order->weight,
                        'volume' => $order->volume,
                        'shipmentstopid' => 0,
                        'ship_units' => $order->quantity,
                        'txncode' => 'NP',
                        'status' => 1,
                        'created_at' => $currentDateTime,
                    ];
                    $pickupStopId = ShiporderStop::create($pickupStopData)->id;
                } else {
                    $pickupStopId = $pickupStop->id;
                }

                $dropStop = ShiporderStop::where('shipment_id', $shipIdDb)
                    ->where('stopcity', $order->delivery_city)
                    ->where('stoptype', 'D')
                    ->select('id')
                    ->first();

                if (!$dropStop) {
                    $dropStopData = [
                        'stopname' => $order->delivery_city,
                        'plat' => $order->dlat,
                        'plng' => $order->dlng,
                        'stopcity' => $order->delivery_city,
                        'address' => $dropAddr,
                        'stoptype' => 'D',
                        'stopstatus' => 0,
                        'shipment_id' => $shipIdDb,
                        'ordernumber' => 2,
                        'startdate' => $order->delivery_datetime,
                        'enddate' => $order->drop_endtime,
                        'weight' => $order->weight,
                        'volume' => $order->volume,
                        'shipmentstopid' => 0,
                        'ship_units' => $order->quantity,
                        'txncode' => 'NP',
                        'status' => 1,
                        'created_at' => $currentDateTime,
                    ];
                    $dropStopId = ShiporderStop::create($dropStopData)->id;
                } else {
                    $dropStopId = $dropStop->id;
                }

                $stopSequence = ShiporderStopSequence::where('stop_id', $pickupStopId)
                    ->where('drop_stopid', $dropStopId)
                    ->where('order_id', $order->order_id)
                    ->where('shift_id', $shipIdDb)
                    ->select('id')
                    ->first();

                if (!$stopSequence) {
                    $stopSequenceData = [
                        'assoc_id' => $shipId,
                        'pickup' => $pickup,
                        'plat' => $order->plat,
                        'plng' => $order->plng,
                        'drop' => $delivery,
                        'dlat' => $order->dlat,
                        'dlng' => $order->dlng,
                        'pickup_city' => $order->pickup_city,
                        'drop_city' => $order->delivery_city,
                        'pickup_datetime' => $order->pickup_datetime,
                        'drop_datetime' => $order->delivery_datetime,
                        'name' => 'Boxes',
                        'phone' => $order->customer_phone,
                        'address' => $address,
                        'emailid' => $order->customer_email,
                        'user_id' => $userId,
                        'status' => 1,
                        'created_at' => $currentDateTime,
                        'material_id' => 0,
                        'capacity' => $order->weight ?? '0',
                        'information' => $order->customer_comments ?? '',
                        'shipment_weight' => $order->weight,
                        'shipment_volume' => $order->volume,
                        'ship_type' => 'P',
                        'customer_id' => $order->customer_id,
                        'vendor_id' => $vendorId,
                        'shipment_id' => 0,
                        'startdate' => $order->pickup_datetime,
                        'enddate' => $order->delivery_datetime,
                        'shift_id' => $shipIdDb,
                        'stop_order' => 1,
                        'drop_order' => 0,
                        'basic_stop' => 0,
                        'stop_id' => $pickupStopId,
                        'drop_stopid' => $dropStopId,
                        'order_id' => $order->order_id,
                        'pkgitemid' => 'BOXES',
                        'no_of_pkgs' => $order->quantity,
                        'domainname' => 'INFD',
                    ];
                    $detailId = ShiporderStopSequence::create($stopSequenceData)->id;
                } else {
                    $detailId = $stopSequence->id;
                }

                $cargoDetails = DB::select("
                    SELECT oc.id, oc.quantity, oc.width, oc.height, oc.length, oc.weight, im.unit_name
                    FROM order_cargodetails oc
                    JOIN shipunit_types im ON im.id = oc.handling_unit
                    WHERE oc.order_id = ? AND oc.status = 1
                ", [$orderId]);

                if (count($cargoDetails) > 0) {
                    $cargo = $cargoDetails[0];
                    OrderCargodetail::where('id', $cargo->id)->update(['stop_detail_id' => $detailId]);
                }

                $shiftVehicle = ShiftVehicle::where('shft_id', $shipIdDb)->select('id')->first();
                if ($shiftVehicle) {
                    $shiftVehicleId = $shiftVehicle->id;
                    $stopLeg = ShipmentVehicleStopsleg::where('shft_veh_id', $shiftVehicleId)
                        ->where('status', 1)
                        ->select('id')
                        ->first();

                    if (!$stopLeg) {
                        $stopSequences = ShiporderStopSequence::where('shift_id', $shipIdDb)
                            ->select('id', 'pickup_datetime', 'drop_datetime')
                            ->get();

                        foreach ($stopSequences as $index => $seq) {
                            ShipmentVehicleStopsleg::create([
                                'user_id' => $userId,
                                'shft_veh_id' => $shiftVehicleId,
                                'emp_id' => $seq->id,
                                'priority' => $index + 1,
                                'pickup_time' => $seq->pickup_datetime,
                                'drop_time' => $seq->drop_datetime,
                                'created_at' => $currentDateTime,
                                'updated_at' => $currentDateTime,
                                'status' => 1,
                            ]);
                        }
                    } else {
                        $existingStopLeg = ShipmentVehicleStopsleg::where('shft_veh_id', $shiftVehicleId)
                            ->where('emp_id', $detailId)
                            ->where('status', 1)
                            ->select('id')
                            ->first();

                        if (!$existingStopLeg) {
                            ShipmentVehicleStopsleg::create([
                                'user_id' => $userId,
                                'shft_veh_id' => $shiftVehicleId,
                                'emp_id' => $detailId,
                                'priority' => 3,
                                'pickup_time' => $order->pickup_datetime,
                                'drop_time' => $order->delivery_datetime,
                                'created_at' => $currentDateTime,
                                'updated_at' => $currentDateTime,
                                'status' => 1,
                            ]);
                        }
                    }
                } else {
                    if ($vehicleId) {
                        $vehicle = TrucksData::where('id', $vehicleId)->select('register_number')->first();
                        $vehicleNumber = $vehicle ? $vehicle->register_number : '';

                        $shiftVehicleId = ShiftVehicle::create([
                            'user_id' => $userId,
                            'route_id' => 0,
                            'shft_id' => $shipIdDb,
                            'carrier_id' => $vendorId,
                            'vehicle_id' => $vehicleId,
                            'register_number' => $vehicleNumber,
                            'status' => 1,
                            'created_at' => $currentDateTime,
                            'updated_at' => $currentDateTime,
                        ])->id;

                        $stopSequences = ShiporderStopSequence::where('shift_id', $shipIdDb)
                            ->select('id', 'pickup_datetime', 'drop_datetime')
                            ->get();

                        foreach ($stopSequences as $index => $seq) {
                            ShipmentVehicleStopsleg::create([
                                'user_id' => $userId,
                                'shft_veh_id' => $shiftVehicleId,
                                'emp_id' => $seq->id,
                                'priority' => $index + 1,
                                'pickup_time' => $seq->pickup_datetime,
                                'drop_time' => $seq->drop_datetime,
                                'created_at' => $currentDateTime,
                                'updated_at' => $currentDateTime,
                                'status' => 1,
                            ]);
                        }
                    }
                }

                if ($vehicleId > 0 && $driverId > 0) {
                    $vehicleDriver = VehiclesDriver::where('vehicle_id', $vehicleId)
                        ->orderBy('id', 'desc')
                        ->select('id', 'driver_id', 'status')
                        ->first();

                    if ($vehicleDriver) {
                        if ($vehicleDriver->driver_id == $driverId) {
                            VehiclesDriver::where('id', $vehicleDriver->id)->update(['status' => 1]);
                        } else {
                            VehiclesDriver::create([
                                'vehicle_id' => $vehicleId,
                                'driver_id' => $driverId,
                                'imei' => '',
                                'status' => 1,
                                'created_at' => $currentDateTime,
                                'updated_at' => $currentDateTime,
                            ]);
                        }
                    } else {
                        VehiclesDriver::create([
                            'vehicle_id' => $vehicleId,
                            'driver_id' => $driverId,
                            'imei' => '',
                            'status' => 1,
                            'created_at' => $currentDateTime,
                            'updated_at' => $currentDateTime,
                        ]);
                    }

                    $assignedDriver = AssignedDriver::where('driver_id', $driverId)
                        ->where('vehicle_id', $vehicleId)
                        ->select('id')
                        ->first();

                    if ($assignedDriver) {
                        AssignedDriver::where('id', $assignedDriver->id)->update([
                            'status' => '1',
                            'updated_at' => $currentDateTime,
                        ]);
                    } else {
                        $otherDriver = AssignedDriver::where('driver_id', '!=', $driverId)
                            ->where('vehicle_id', $vehicleId)
                            ->where('status', 1)
                            ->select('id')
                            ->first();

                        if (!$otherDriver) {
                            AssignedDriver::create([
                                'vehicle_id' => $vehicleId,
                                'user_id' => $userId,
                                'driver_id' => $driverId,
                                'mobile_no' => $driverId,
                                'imei' => '',
                                'from_time' => $currentDateTime,
                                'to_time' => $currentDateTime,
                                'status' => '1',
                                'created_at' => $currentDateTime,
                            ]);
                        } else {
                            AssignedDriver::where('id', $otherDriver->id)->update([
                                'driver_id' => $driverId,
                                'updated_at' => $currentDateTime,
                            ]);
                        }
                    }
                }

                $response = 1;

                $stopStatus = StopStatus::where('shipment_id', $shipIdDb)
                    ->where('status_id', 9)
                    ->select('id')
                    ->first();

                if (!$stopStatus) {
                    StopStatus::create([
                        'shipment_id' => $shipIdDb,
                        'stop_id' => 0,
                        'stop_detail_id' => 0,
                        'stop_type' => '',
                        'trip_id' => 0,
                        'status_id' => 9,
                        'status' => 1,
                        'status_code' => '0100',
                        'reason' => 'Coming from E-Booking',
                        'created_at' => $currentDateTime1,
                    ]);
                }

                // Placeholder for notifytrigger and ratemanagement
                // app('notifytrigger')->handle();
                // app('ratemanagement')->handle();
                $this->rateManagement->addrecodfortripinsertion(['ordid' => $orderId, 'carrierid' => $vendorId]);

                // Placeholder for DelhiveryB2BEDIServices
                // if ($this->checkAccessConditions('CHECK_DELHIVERY_LIMITED_CARRIER', $vendorId)) {
                //     app('DelhiveryB2BEDIServices')->triggerDelhiveryAPI($shipIdDb);
                // }

                $vendorAutoAccept = SxPartyMembers::where('id', $vendorId)
                    ->where('tms_auto_accept', 1)
                    ->select('id')
                    ->first();

                if ($vendorAutoAccept) {
                    $driver = VehiclesDriver::where('vehicle_id', $vehicleId)
                        ->where('status', 1)
                        ->select('driver_id')
                        ->first();

                    if ($driver) {
                        $driverId = $driver->driver_id;
                        $contactNum = Driver::where('id', $driverId)
                            ->where('status', 1)
                            ->select('contact_num')
                            ->first()?->contact_num;

                        if ($vehicleId > 0 && $driverId > 0) {
                            $trip = Trip::where('shift_id', $shipIdDb)
                                ->where('vehicle_id', $vehicleId)
                                ->where('driver_id', $driverId)
                                ->select('id')
                                ->first();

                            if (!$trip) {
                                $tripId = Trip::create([
                                    'shift_id' => $shipIdDb,
                                    'vehicle_id' => $vehicleId,
                                    'driver_id' => $driverId,
                                    'stime' => $currentDateTime,
                                    'start_imei' => $contactNum ?? '',
                                    'splace' => '',
                                    'eplace' => '',
                                    'start_reading' => 0,
                                    'end_reading' => 0,
                                    'created_at' => $currentDateTime,
                                    'updated_at' => $currentDateTime,
                                    'status' => 1,
                                    'trip_type' => 0,
                                    'transit_status' => 0,
                                ])->id;

                                Order::where('shift_id', $shipIdDb)->update(['trip_id' => $tripId]);

                                StopStatus::create([
                                    'order_id' => $orderId,
                                    'shipment_id' => $shipIdDb,
                                    'stop_id' => 0,
                                    'stop_detail_id' => 0,
                                    'stop_type' => '',
                                    'trip_id' => $tripId,
                                    'status_id' => 10,
                                    'latitude' => '',
                                    'longitude' => '',
                                    'status' => 1,
                                    'reason' => 'From Admin',
                                    'vehicle_id' => $vehicleId,
                                    'driver_id' => $driverId,
                                    'status_code' => '0212',
                                    'created_at' => $currentDateTime,
                                ]);
                            }
                        }
                    }
                }

                $customer = SxPartyMembers::where('id', $orderInfo['customer_id'])->select('name')->first();
                $customerName = $customer ? $customer->name : '';

                $trackingRef = $order->created_source == 18 ? 'AWB' : 'DQ';
                $trackingNumber = OrderReference::where('order_id', $orderId)
                    ->where('reference_id', $trackingRef)
                    ->where('status', '1')
                    ->select('ref_value')
                    ->first()?->ref_value ?? '';

                // Placeholder for Firebase push notification
                // if ($shipId && $vendorInfo['driver_id'] > 0) {
                //     $message = "New trip is assigned and accepted Pickup Date: " . substr($order->pickup_datetime, 0, 10) . " Customer Name: $customerName Booking ID: $orderNumber KN tracking no: $trackingNumber Trip No: $shipId";
                //     app('sendfirebase')->sendSinglePush([
                //         'title' => 'ShipmentX',
                //         'message' => json_encode(['msg' => $message, 'info' => (object)[], 'type' => 1], JSON_UNESCAPED_UNICODE),
                //         'emp_id' => $vendorInfo['driver_id'],
                //         'type' => 'Driver'
                //     ]);
                // }
            }
        }

        return $response;
    }

    public function addRecordForOrderInsertion(array $arr)
    {
        $cdate = Carbon::now()->format('Y-m-d H:i:s');
        $cust_profile_id = $exchange_rate = $service_chk = $product_chk = $order_otype = $refcheck = $chk_rate_service = $rate_service_id = $rate_offering = $rate_record = $modechk = 0;
        $service_row_ids = $serviceses = [];
        $order_mod = $offering_type = '';
        $user = Auth::user();
        $org_id =  $user->org_id ?? 0;
        if (!empty($arr)) {
            $user_id = $arr['user_id'];
            $order_product = $arr['product'] ?? '';
            $order_service = $arr['service'] ?? '0';
            $order_otype = $arr['order_type'] ?? '0';
            $pickup_id = $arr['customer_row_id'] ?? '0';
            $pickup_country = $arr['pickup'] ?? '';
            $pickup_pincode = $arr['pickup_pincode'] ?? '';
            $pickup_city = $arr['pickup_city'] ?? '';
            $drop_country = $arr['drop'] ?? '';
            $drop_city = $arr['drop_city'] ?? '';
            $drop_pincode = $arr['drop_pincode'] ?? '';

            $pickup_location = [
                'country' => $pickup_country,
                'zipcode' => $pickup_pincode,
                'user_id' => $user_id,
                'city' => $pickup_city,
            ];
            $delivery_location = [
                'country' => $drop_country,
                'zipcode' => $drop_pincode,
                'user_id' => $user_id,
                'city' => $drop_city,
            ];

            if ($order_service == '') {
                $order_service = 0;
            }
            if ($order_otype == '') {
                $order_otype = 0;
            }

            // Fetch order transport mode
            $order = Order::where('id', $arr['order_id'])
                ->where('status', '!=', 0)
                ->select('transport_mode')
                ->first();

            if ($order) {
                $order_mod = $order->transport_mode ?? '';
                if (empty($order_mod) || is_null($order_mod)) {
                    $order_mod = '';
                    $modechk = 1;
                }
            }

            // Fetch customer profile ID
            $cust_profile = $this->getCustomerProfileForPreference($arr['customer_id'], $user_id);
            if ($cust_profile) {
                $cust_profile_id = $cust_profile->id;
            }

            $product_id = 0;
            $whr = "(org_id = '' OR org_id IS NULL)";
            if ($cust_profile_id != '0') {
                if ($order_product != '') {
                    // if (in_array($org_id, ['ZAKN', 'VNKN'], true)) {
                    //     $product = Product::where('name', $order_product)
                    //         ->where('status', 1)
                    //         ->where('org_id', $org_id)
                    //         ->select('id')
                    //         ->first();
                    //     if ($product) {
                    //         $product_id = $product->id;
                    //     }
                    // } else {
                    $product = Product::where('name', $order_product)
                        ->whereRaw($whr)
                        ->where('status', 1)
                        ->select('id')
                        ->first();
                    if ($product) {
                        $product_id = $product->id;
                    }
                    // } 
                }

                $charge_of_country = [];
                $rate_autobill = 0;
                $getpreferences = $this->getPreferenceByOrderDetails($cust_profile_id, $user_id, $arr);
                $info = ['order_id' => $arr['order_id'], 'product' => $order_product];
                $serviceses = $this->getCustomerProfileDetailsById($pickup_id, $order_service, $pickup_location, $delivery_location, $info);

                if (!empty($getpreferences)) {
                    foreach ($getpreferences as $preference) {
                        $preference_id = $preference['id'];
                        $rate_service_id = $preference['rate_service_id'];
                        $rate_offering = $preference['rate_offering_id'];
                        $rate_record = $preference['rate_record_id'];
                        $rate_autobill = $preference['auto_bill'];

                        if ($rate_service_id > 0) {
                            foreach ($serviceses as $serv) {
                                $service_row_ids[] = $serv['service_row_id'];
                            }
                            if (!empty($service_row_ids) && in_array($rate_service_id, $service_row_ids)) {
                                $chk_rate_service = 1;
                            }
                        }

                        $checkLanes = $this->getLaneIdsByService($rate_service_id, $arr['order_id'], [
                            'consignee_country' => $delivery_location['country'],
                            'shipper_country' => $pickup_location['country'],
                            'shipper_city' => $pickup_location['city'],
                            'consignee_city' => $delivery_location['city'],
                            'shipper_zipcode' => $pickup_location['zipcode'],
                            'consignee_zipcode' => $delivery_location['zipcode'],
                        ]);

                        if (empty($checkLanes)) {
                            $chk_rate_service = 0;
                        }

                        if ($rate_offering > 0) {
                            $offering = RateOffering::where('id', $rate_offering)
                                ->where('status', 1)
                                ->select('offering_type', 'cust_profile_id')
                                ->first();

                            if ($offering) {
                                $customerProfileId = $offering->cust_profile_id;
                                if ($customerProfileId > 0) {
                                    $checkProfile = CustomerProfileList::where('cp_id', $customerProfileId)
                                        ->where('profile_id', $arr['customer_id'])
                                        ->where('status', 1)
                                        ->first();
                                    if (!$checkProfile) {
                                        $chk_rate_service = 0;
                                    }
                                }
                                $modechk = 0;
                                $offering_type = $offering->offering_type ?? '';
                                if (empty($offering_type) || is_null($offering_type)) {
                                    $modechk = 1;
                                    $offering_type = '';
                                }
                                if ($modechk == 0 && $offering_type != '' && $order_mod != '') {
                                    if (strtoupper($offering_type) == strtoupper($order_mod)) {
                                        $modechk = 1;
                                    }
                                }
                            }
                        }

                        if ($chk_rate_service == 1) {
                            $refcheck = $this->checkRefAndOrCondition($arr['order_id'], $rate_record);
                            $chk_prefref = $this->preferenceCheckRefAndOrCondition($arr['order_id'], $preference_id);

                            if ($product_id > 0) {
                                $product_chk = 1;
                                $chk_preferproduct = RatepreferProduct::where('rate_prefer_id', $preference_id)
                                    ->where('user_id', $user_id)
                                    ->where('status', 1)
                                    ->pluck('product_id')
                                    ->toArray();
                                if (!empty($chk_preferproduct)) {
                                    $product_chk = in_array($product_id, $chk_preferproduct) ? 1 : 0;
                                }
                            } else {
                                $product_chk = 1;
                            }

                            if ($order_service > 0) {
                                $service_chk = 1;
                                $chk_preferservice = RatepreferService::where('rate_prefer_id', $preference_id)
                                    ->where('user_id', $user_id)
                                    ->where('status', 1)
                                    ->pluck('service_id')
                                    ->toArray();
                                if (!empty($chk_preferservice)) {
                                    $service_chk = in_array($order_service, $chk_preferservice) ? 1 : 0;
                                }
                            } else {
                                $service_chk = 1;
                            }

                            if ($order_otype > 0) {
                                $ordertype_chk = 1;
                                $chk_preferotype = RatepreferOrdertype::where('rate_prefer_id', $preference_id)
                                    ->where('user_id', $user_id)
                                    ->where('status', 1)
                                    ->pluck('order_type_id')
                                    ->toArray();
                                if (!empty($chk_preferotype)) {
                                    $ordertype_chk = in_array($order_otype, $chk_preferotype) ? 1 : 0;
                                }
                            } else {
                                $ordertype_chk = 1;
                            }

                            if ($refcheck == 1 && $ordertype_chk == 1 && $service_chk == 1 && $product_chk == 1 && $chk_prefref == 1 && $modechk == 1) {
                                $type = '0';
                                $vascharges = [];
                                if ($rate_offering != '' && $rate_offering != '0') {
                                    if ($arr['order_id'] != '' && $arr['order_id'] != '0') {
                                        $vascharges = $this->getChargesThroughVasByRecord($rate_offering, $rate_record, $arr['order_id'], $user_id, $type);
                                    }
                                    $offering_gid = 1;
                                    $getoffering_id = RateOffering::where('id', $rate_offering)
                                        ->where('status', 1)
                                        ->select('offering_id')
                                        ->first();
                                    if ($getoffering_id) {
                                        $offering_gid = $getoffering_id->offering_id;
                                    }
                                    $offeringvascharges = $this->getChargesThroughVasByOffering($rate_offering, $type, $arr['order_id'], $user_id);
                                    if (!empty($offeringvascharges)) {
                                        foreach ($offeringvascharges as $res) {
                                            $charge = ChargeCode::where('id', $res['charge_id'])
                                                ->where('status', 1)
                                                ->select('id', 'name')
                                                ->first();
                                            $lane_chargerow_id = $charge->id ?? 0;
                                            $lane_chargename = $charge->name ?? '';
                                            $charge_of_country[] = [
                                                'charge_code' => $res['charge_id'],
                                                'description' => $lane_chargename,
                                                'quantity_unit' => '1',
                                                'value' => '1',
                                                'rate_id' => $offering_gid,
                                                'amount' => $res['amount'],
                                                'currency' => $res['currency'],
                                                'rate_service_id' => $rate_service_id,
                                                'rate_offering_id' => $rate_offering,
                                                'rate_record_id' => $rate_record,
                                                'user_id' => $user_id,
                                                'created_at' => $cdate,
                                                'source_created' => 'RP - Rate Preference',
                                            ];
                                        }
                                    }
                                }

                                $record_id = 1;
                                $getraterecord_id = RateRecord::where('id', $rate_record)
                                    ->where('status', 1)
                                    ->select('rate_id')
                                    ->first();
                                if ($getraterecord_id) {
                                    $record_id = $getraterecord_id->rate_id;
                                }

                                $charge_ar = $this->getRateRecordCharges($rate_offering, $rate_record, $type, $arr['order_id'], $record_id, $user_id);

                                $invoice_status = 0;
                                $debtor_jfr = $invoice_number = $credit_note_number = '';
                                $invoice_date = '0000-00-00 00:00:00';
                                $inv_createddate = '0000-00-00 00:00:00';
                                $inv_receiveddate = '0000-00-00 00:00:00';
                                $customer_name = 'Customer';
                                $customerdetails = SxPartyMembers::where('code', $arr['customer_id'])
                                    ->where('status', 1)
                                    ->where('user_id', $user_id)
                                    ->select('name')
                                    ->first();
                                if ($customerdetails) {
                                    $customer_name = $customerdetails->name;
                                }

                                $currency = $foreign_currency = '';
                                if (!empty($charge_ar)) {
                                    $foreign_currency = $charge_ar[0]['foreign_currency'] ?? '';
                                    $currency = $charge_ar[0]['currency'] ?? '';
                                    $exchange_rate = $charge_ar[0]['exchange_amount'] ?? 0;
                                    foreach ($charge_ar as &$charge) {
                                        unset($charge['foreign_currency']);
                                        unset($charge['exchange_amount']);
                                        $charge['rate_service_id'] = $rate_service_id;
                                        $charge['rate_offering_id'] = $rate_offering;
                                        $charge['rate_record_id'] = $rate_record;
                                        $charge['source_created'] = 'RP - Rate Preference';
                                    }
                                }

                                $charge_ar_vas = [];
                                if (!empty($vascharges)) {
                                    foreach ($vascharges as $vas) {
                                        $vas_charge_id = $vas['charge_id'];
                                        if ($vas_charge_id != '0' && $vas_charge_id != '') {
                                            $charge = ChargeCode::where('id', $vas_charge_id)
                                                ->where('status', 1)
                                                ->select('id', 'name')
                                                ->first();
                                        } else {
                                            $charge = ChargeCode::where('charge_code', 'CSC')
                                                ->where('status', 1)
                                                ->select('id', 'name')
                                                ->first();
                                        }
                                        $lane_chargerow_id = $charge->id ?? 0;
                                        $lane_chargename = $charge->name ?? '';
                                        $charge_ar_vas[] = [
                                            'charge_code' => $lane_chargerow_id,
                                            'description' => $lane_chargename,
                                            'quantity_unit' => '1',
                                            'value' => '1',
                                            'rate_id' => $record_id,
                                            'amount' => $vas['amount'],
                                            'rate_service_id' => $rate_service_id,
                                            'rate_offering_id' => $rate_offering,
                                            'rate_record_id' => $rate_record,
                                            'currency' => $vas['currency'],
                                            'user_id' => $user_id,
                                            'created_at' => $cdate,
                                            'source_created' => 'RP - Rate Preference',
                                        ];
                                    }
                                }

                                $revenue_ar = [
                                    'order_id' => $arr['order_id'],
                                    'type' => '0',
                                    'recipient_role' => 'Customer',
                                    'recipient_code' => $arr['customer_id'],
                                    'recipient_name' => $customer_name,
                                    'debtor_jfr' => $debtor_jfr,
                                    'invoice_number' => $invoice_number,
                                    'credit_note_number' => $credit_note_number,
                                    'invoice_date' => $invoice_date,
                                    'invoice_creation_date' => $inv_createddate,
                                    'invoice_receivdon_date' => $inv_receiveddate,
                                    'amount' => '0',
                                    'currency' => $currency,
                                    'status' => 1,
                                    'user_id' => $user_id,
                                    'created_at' => $cdate,
                                    'exchange_rate' => $exchange_rate,
                                    'invoice_status' => $invoice_status,
                                    'foreign_currency' => $foreign_currency,
                                ];

                                $rev_row_id = $this->insertRecords($revenue_ar, $charge_ar, [], $charge_of_country, $charge_ar_vas);

                                if ($rate_record > 0 && $rev_row_id > 0) {
                                    $addfaf = $this->addFafChargesToOrder($arr['order_id'], $rev_row_id, $rate_record, 'RP - Rate Preference');
                                }

                                // Auto-bill logic (commented out in original)
                                /*
                                if ($rate_autobill == '1') {
                                    $user = DB::table('users')
                                        ->select('country_code', 'org_id')
                                        ->where('id', $user_id)
                                        ->first();
                                    $country_code = $user->country_code ?? '';
                                    $org_id = $user->org_id ?? '';
                                    $info = [
                                        'country_code' => $country_code,
                                        'user_id' => $user_id,
                                        'revnue_id' => $rev_row_id,
                                        'order_id' => $arr['order_id'],
                                    ];
                                    $debtor_jfr = $this->generateDebtorJfrCode($info);
                                    if ($debtor_jfr != '' && $debtor_jfr != '0') {
                                        $curtz = session('usr_tzone.timezone', 'UTC');
                                        $update = Carbon::now($curtz)->format('Y-m-d H:i:s');
                                        Revenue::where('id', $rev_row_id)
                                            ->update([
                                                'debtor_jfr' => $debtor_jfr,
                                                'invoice_status' => '1',
                                                'debitor_time' => $update,
                                            ]);
                                    }
                                }
                                */
                            }
                        }
                    }
                }
            }
        }
    }

    public function getCustomerProfileForPreference($cust_code, $user_id)
    {
        return CustomerProfile::select('customer_profile.id')
            ->from('customer_profile')
            ->leftJoin('customer_profile_list as cpl', 'cpl.cp_id', '=', 'customer_profile.id')
            ->where('cpl.profile_id', $cust_code)
            ->where('customer_profile.user_id', $user_id)
            ->where('customer_profile.status', 1)
            ->first();
    }

    public function getPreferenceByOrderDetails($cust_profile_id, $user_id, $arr)
    {
        $result = [];
        if ($cust_profile_id != '' && $cust_profile_id != '0') {
            $whr = " (rps.country LIKE '{$arr['pickup']}' OR rps.state LIKE '{$arr['pickup_state']}' OR rps.city LIKE '{$arr['pickup_city']}' OR rps.pincode LIKE '{$arr['pickup_pincode']}') AND rps.user_id='{$user_id}' AND rps.status='1' AND (rds.country LIKE '{$arr['drop']}' OR rds.state LIKE '{$arr['drop_state']}' OR rds.city LIKE '{$arr['drop_city']}' OR rds.pincode LIKE '{$arr['drop_pincode']}') AND rds.user_id ='{$user_id}' AND rds.status='1' AND rp.status='1'";

            $result = RatePreference::select('rp.id', 'rp.rate_service_id', 'rp.rate_offering_id', 'rp.rate_record_id', 'rp.auto_bill')
                ->from('rate_preferences as rp')
                ->leftJoin('rateprefer_source as rps', 'rps.rate_prefer_id', '=', 'rp.id')
                ->leftJoin('rateprefer_destination as rds', 'rds.rate_prefer_id', '=', 'rp.id')
                ->where('rp.cust_profile_id', $cust_profile_id)
                ->where('rp.tariff_type', '0')
                ->where('rp.user_id', $user_id)
                ->where('rp.status', 1)
                ->whereRaw($whr)
                ->groupBy('rp.id')
                ->get()
                ->toArray();
        }
        return $result;
    }

    public function getCustomerProfileDetailsById($cust_id, $service_id, $ploc, $dloc, $info)
    {
        $customer_code = 0;
        $orderproduct = $info['product'] ?? '';
        $order_id = $info['order_id'] ?? '0';
        $services = [];
        $serviceIds = [];
        $user = Auth::user();
        $org_id = $user->org_id ?? 0;
        $user_id = $user->user_id ?? 0;

        if ($cust_id != '' && $cust_id != '0') {
            $custwhr = ['id' => $cust_id];
            if ($org_id == 'RUKN') {
                $custwhr['org_id'] = $org_id;
            } else {
                $custwhr['user_id'] = $user_id;
            }

            $customer = SxPartyMembers::where($custwhr)->select('code')->first();
            $customer_code = $customer ? $customer->code : $cust_id;

            if (!empty($ploc) && strtoupper($ploc['country']) == 'AUSTRALIA') {
                $pickupgeocode = $this->checkgeocode($ploc);
                $dropgeocode = $this->checkgeocode($dloc);
                if (!empty($pickupgeocode) && !empty($dropgeocode)) {
                    $services = $this->getLanesAndServiceByGeocodes($pickupgeocode, $dropgeocode, $ploc['user_id'], $ploc['country']);
                }
            }

            if ($customer_code != '0') {
                $getservices = $this->getCustomerBasedServices($customer_code, $service_id);
                foreach ($getservices as $res) {
                    $service_name = $res->service_name;
                    $product = $res->product;
                    if ($orderproduct != '') {
                        if ($product != '' && strtoupper($orderproduct) == strtoupper($product)) {
                            $service = $service_name ? "{$res->service_id}-{$service_name}" : $res->service_id;
                            if (count($services) > 0) {
                                if (!in_array($res->id, $serviceIds)) {
                                    $serviceIds[] = $res->id;
                                    $services[] = ['service_row_id' => $res->id, 'service_id' => $service];
                                }
                            } else {
                                $serviceIds[] = $res->id;
                                $services[] = ['service_row_id' => $res->id, 'service_id' => $service];
                            }
                        }
                    } else {
                        $service = $service_name ? "{$res->service_id}-{$service_name}" : $res->service_id;
                        if (count($services) > 0) {
                            if (!in_array($res->id, $serviceIds)) {
                                $serviceIds[] = $res->id;
                                $services[] = ['service_row_id' => $res->id, 'service_id' => $service];
                            }
                        } else {
                            $serviceIds[] = $res->id;
                            $services[] = ['service_row_id' => $res->id, 'service_id' => $service];
                        }
                    }
                }
            }
        }

        $service_id = $this->getServicesByOrderLanes($order_id);
        $result = [];
        if (!empty($services)) {
            foreach ($services as $ser) {
                if ($ser['service_row_id'] > 0 && in_array($ser['service_row_id'], $service_id)) {
                    $result[] = ['service_row_id' => $ser['service_row_id'], 'service_id' => $ser['service_id']];
                }
            }
        }

        return $result;
    }

    public function getLanesAndServiceByGeocodes($pickup, $delivery, $user_id, $country)
    {
        $lane_id = 0;
        $services = [];
        $user = Auth::user();
        $org_id = $user->org_id ?? 0;

        if (!empty($pickup) && !empty($delivery)) {
            $pickup_region = $pickup['region'] ?? '';
            $delivery_region = $delivery['region'] ?? '';

            if ($pickup_region != '' && $delivery_region != '') {
                $query = LanesMaster::select('l.id')
                    ->from('lanes_master as l')
                    ->join('geo_master as g', function ($join) {
                        $join->on('g.id', '=', 'l.source_geo')
                            ->where('g.name', 'LIKE', 'Region');
                    })
                    ->where('l.source', $pickup_region)
                    ->where('l.source_country', $country)
                    ->where('l.destination_geo', DB::raw('g.id'))
                    ->where('l.destination', $delivery_region)
                    ->where('l.destination_country', $country);

                if ($org_id != 'RUKN') {
                    $query->where('l.user_id', $user_id);
                }

                $lane = $query->first();
                if ($lane) {
                    $lane_id = $lane->id;
                }
            }
        }

        if ($lane_id != '0') {
            $query = RateService::select('r.id', 'r.service_id', 'r.service_name')
                ->from('rate_services as r')
                ->join('rateservice_lanes as l', 'l.rate_id', '=', 'r.id')
                ->where('l.lane_id', $lane_id)
                ->where('l.status', 1)
                ->where('r.status', 1);

            $query->where('r.user_id', $user_id);

            $services_result = $query->get();
            foreach ($services_result as $res) {
                $service_name = $res->service_name;
                $service_id = $res->service_id;
                $service = $service_name ? "{$service_id}-{$service_name}" : $service_id;
                $services[] = ['service_row_id' => $res->id, 'service_id' => $service];
            }
        }

        return $services;
    }

    public function getCustomerBasedServices($cust_code, $service_id)
    {
        return RateService::select('rs.id', 'rs.service_id', 'rs.service_name', 'rs.product')
            ->from('rate_services as rs')
            ->leftJoin('rate_offerings as ro', 'rs.id', '=', 'ro.rate_service_id')
            ->leftJoin('customer_profile as cp', 'cp.id', '=', 'ro.cust_profile_id')
            ->leftJoin('customer_profile_list as cl', 'cp.id', '=', 'cl.cp_id')
            ->where('cl.profile_id', $cust_code)
            ->where('rs.service_type', $service_id)
            ->where('rs.status', 1)
            ->where('cp.status', 1)
            ->where('cl.status', 1)
            ->where('ro.status', 1)
            ->groupBy('rs.id')
            ->orderBy('rs.id', 'DESC')
            ->get();
    }

    public function getServicesByOrderLanes($order_id)
    {
        $user = Auth::user();
        $service_id = [];
        $user_id = $user->user_id ?? 0; //testing
        $pickup_country = $delivery_country = $pickup_city = $delivery_city = $pickup_zcode = $delivery_zcode = $pickupState = $deliveryState = $pickup_sub_district = $pickup_division_name = $delivery_sub_district = $delivery_division_name = '';

        if ($order_id != '' && $order_id > 0) {
            $details = Order::where('id', $order_id)
                ->select('pickup_country', 'delivery_country', 'pickup_city', 'delivery_city', 'pickup_pincode', 'delivery_pincode', 'pickup_address2', 'delivery_address2', 'pickup_custid', 'drop_custid')
                ->first();

            if ($details) {
                $pickup_country = $details->pickup_country;
                $delivery_country = $details->delivery_country;
                $pickup_city = $details->pickup_city;
                $delivery_city = $details->delivery_city;
                $pickup_zcode = $details->pickup_pincode;
                $delivery_zcode = $details->delivery_pincode;
                $pickupState = $details->pickup_address2;
                $deliveryState = $details->delivery_address2;

                $pickupsubdetails = SxPartyMembers::where(function ($query) use ($details) {
                    $query->where('id', $details->pickup_custid)
                        ->orWhere('code', $details->pickup_custid)
                        ->orWhere('customeridentifier', $details->pickup_custid);
                })
                    ->select('sub_district', 'division_name')
                    ->first();

                if ($pickupsubdetails) {
                    $pickup_sub_district = $pickupsubdetails->sub_district;
                    $pickup_division_name = $pickupsubdetails->division_name;
                }

                $dropsubdetails = SxPartyMembers::where(function ($query) use ($details) {
                    $query->where('id', $details->drop_custid)
                        ->orWhere('code', $details->drop_custid)
                        ->orWhere('customeridentifier', $details->drop_custid);
                })
                    ->select('sub_district', 'division_name')
                    ->first();

                if ($dropsubdetails) {
                    $delivery_sub_district = $dropsubdetails->sub_district;
                    $delivery_division_name = $dropsubdetails->division_name;
                }
            }
        }

        $source = [
            'pickup_country' => $pickup_country,
            'delivery_country' => $delivery_country,
            'pickup_city' => $pickup_city,
            'delivery_city' => $delivery_city,
            'pickup_zcode' => $pickup_zcode,
            'delivery_zcode' => $delivery_zcode,
            'user_id' => $user_id,
            'pickup_sub_district' => $pickup_sub_district,
            'pickup_division_name' => $pickup_division_name,
            'delivery_sub_district' => $delivery_sub_district,
            'delivery_division_name' => $delivery_division_name,
            'pickup_state' => $pickupState,
            'delivery_state' => $deliveryState,
        ];

        $getservice_id = $this->getServiceIdByLanes($source);
        foreach ($getservice_id as $row) {
            if ($row['rate_id'] > 0) {
                $service_id[] = $row['rate_id'];
            }
        }

        return $service_id;
    }

    public function getServiceIdByLanes($source)
    {
        $result = [];
        if (!empty($source)) {
            $whereCondition = $this->generateWhereConditionForGeo($source);
            $user = Auth::user();
            $org_id =  $user->org_id ?? 0;

            if (!empty($whereCondition)) {
                $whereString = '( ' . implode(' OR ', $whereCondition) . ' )';
                $query = RateServiceLanes::select('l.id', 'l.lane_id', 'l.lane_name', 'rl.rate_id')
                    ->from('rateservice_lanes as rl')
                    ->leftJoin('lanes_master as l', 'l.id', '=', 'rl.lane_id')
                    ->whereRaw($whereString);

                if ($org_id == 'RUKN') {
                    $query->where('l.org_id', $org_id);
                } else {
                    $query->where('rl.user_id', $source['user_id'])
                        ->where('l.user_id', $source['user_id']);
                }

                $query->where('rl.status', 1)
                    ->where('l.status', 1)
                    ->groupBy('rl.rate_id')
                    ->orderBy('l.id', 'DESC');

                $result = $query->get()->toArray();
            }
        }

        return $result;
    }

    public function generateWhereConditionForGeo(array $source): array
    {
        $whereCondition = [];
        $pickupCity = str_replace("'", "_", $source['pickup_city']);
        $deliveryCity = str_replace("'", "_", $source['delivery_city']);
        $pickupDetails = [
            $source['pickup_country'],
            $source['pickup_state'],
            $source['pickup_division_name'],
            $pickupCity,
            $source['pickup_zcode'],
            $source['pickup_sub_district'],
        ];
        $deliveryDetails = [
            $source['delivery_country'],
            $source['delivery_state'],
            $source['delivery_division_name'],
            $deliveryCity,
            $source['delivery_zcode'],
            $source['delivery_sub_district'],
        ];
        $geoHierarchy = [2, 3, 4, 5, 6, 7];

        foreach ($pickupDetails as $pickupKey => $pickupValue) {
            foreach ($deliveryDetails as $deliveryKey => $deliveryValue) {
                if ($pickupValue && $deliveryValue) {
                    $whereCondition[] = " ( lanes_master.source_geo = '{$geoHierarchy[$pickupKey]}' AND lanes_master.source = '{$pickupValue}' AND lanes_master.destination_geo = '{$geoHierarchy[$deliveryKey]}' AND lanes_master.destination = '{$deliveryValue}' ) ";
                }
            }
        }

        return $whereCondition;
    }

    public function getLaneIdsByService($serviceId, $orderId, $mainSource) // check 
    {
        $lanes = [];
        $pickupCountry = $deliveryCountry = $pickupCity = $deliveryCity = $pickupZcode = $deliveryZcode = '';
        $pickupState = $deliveryState = $pickupSubDistrict = $pickupDivisionName = $deliverySubDistrict = $deliveryDivisionName = '';

        if ($orderId) {
            $orderDetails = Order::where('id', $orderId)
                ->select('pickup_country', 'delivery_country', 'pickup_city', 'delivery_city', 'pickup_pincode', 'delivery_pincode', 'pickup_custid', 'pickup_partyid', 'drop_custid', 'drop_partyid', 'pickup_address2', 'delivery_address2')
                ->first();

            if ($orderDetails) {
                $pickupCountry = $orderDetails->pickup_country;
                $deliveryCountry = $orderDetails->delivery_country;
                $pickupCity = $orderDetails->pickup_city;
                $deliveryCity = $orderDetails->delivery_city;
                $pickupZcode = $orderDetails->pickup_pincode;
                $deliveryZcode = $orderDetails->delivery_pincode;
                $pickupState = $orderDetails->pickup_address2;
                $deliveryState = $orderDetails->delivery_address2;

                $pickupDetails = SxPartyMembers::where(function ($query) use ($orderDetails) {
                    $query->where('id', $orderDetails->pickup_custid)
                        ->orWhere('code', $orderDetails->pickup_custid)
                        ->orWhere('customeridentifier', $orderDetails->pickup_custid);
                })->select('sub_district', 'division_name')->first();

                if ($pickupDetails) {
                    $pickupSubDistrict = $pickupDetails->sub_district;
                    $pickupDivisionName = $pickupDetails->division_name;
                }

                $dropDetails = SxPartyMembers::where(function ($query) use ($orderDetails) {
                    $query->where('id', $orderDetails->drop_custid)
                        ->orWhere('code', $orderDetails->drop_custid)
                        ->orWhere('customeridentifier', $orderDetails->drop_custid);
                })->select('sub_district', 'division_name')->first();

                if ($dropDetails) {
                    $deliverySubDistrict = $dropDetails->sub_district;
                    $deliveryDivisionName = $dropDetails->division_name;
                }
            }
        } else {
            $pickupCountry = $mainSource['pickup_country'];
            $deliveryCountry = $mainSource['delivery_country'];
            $pickupCity = $mainSource['pickup_city'];
            $deliveryCity = $mainSource['delivery_city'];
            $pickupZcode = $mainSource['pickup_pincode'];
            $deliveryZcode = $mainSource['delivery_pincode'];
        }

        $source = [
            'pickup_country' => $pickupCountry,
            'delivery_country' => $deliveryCountry,
            'pickup_city' => $pickupCity,
            'delivery_city' => $deliveryCity,
            'pickup_zcode' => $pickupZcode,
            'delivery_zcode' => $deliveryZcode,
            'pickup_sub_district' => $pickupSubDistrict,
            'pickup_division_name' => $pickupDivisionName,
            'delivery_sub_district' => $deliverySubDistrict,
            'delivery_division_name' => $deliveryDivisionName,
            'pickup_state' => $pickupState,
            'delivery_state' => $deliveryState,
        ];

        $getLanes = $this->getLanesByService($source, $serviceId);

        foreach ($getLanes as $lane) {
            $laneId = $lane['lane_id'];
            $laneName = $lane['lane_name'];
            $laneIdFormatted = $laneName ? "$laneId-$laneName" : $laneId;
            $lanes[] = ['lane_row_id' => $lane['id'], 'lane_id' => $laneIdFormatted];
        }

        if ($pickupCountry == 'AUSTRALIA') {
            $lanesByRange = $this->getLanesByServiceRange($serviceId);
            foreach ($lanesByRange as $res) {
                $laneId = $res->lane_id;
                $laneName = $res->lane_name;
                $laneIdFormatted = $laneName ? "$laneId-$laneName" : $laneId;
                $lanes[] = ['lane_row_id' => $res->id, 'lane_id' => $laneIdFormatted];
            }
        }

        return $lanes;
    }

    public function getLanesByServiceRange($serviceId)
    {
        return LanesMaster::select('lanes_master.id', 'lanes_master.lane_id', 'lanes_master.lane_name')
            ->join('rateservice_lanes', 'lanes_master.id', '=', 'rateservice_lanes.lane_id', 'left')
            ->where('rateservice_lanes.rate_id', $serviceId)
            ->where('lanes_master.source_geo', 1)
            ->where('lanes_master.destination_geo', 1)
            ->where('rateservice_lanes.status', 1)
            ->where('lanes_master.status', 1)
            ->groupBy('lanes_master.id')
            ->orderBy('lanes_master.id', 'desc')
            ->get();
    }

    public function getLanesByService($source, $serviceId)
    {
        $whereCondition = $this->generateWhereConditionForGeo($source);
        if ($whereCondition) {
            $whereString = '(' . implode(' OR ', $whereCondition) . ')';
            return LanesMaster::select('lanes_master.id', 'lanes_master.lane_id', 'lanes_master.lane_name')
                ->join('rateservice_lanes', 'lanes_master.id', '=', 'rateservice_lanes.lane_id', 'left')
                ->whereRaw($whereString)
                ->where('rateservice_lanes.rate_id', $serviceId)
                ->where('rateservice_lanes.status', 1)
                ->where('lanes_master.status', 1)
                ->groupBy('lanes_master.id')
                ->orderBy('lanes_master.id', 'desc')
                ->get()
                ->toArray();
        }
        return [];
    }

    public function checkRefAndOrCondition($orderId, $record)
    {
        $refCheck = 1;
        if ($orderId && $record > 0) {
            $noAnd = $noOr = 1;
            $andWhr = $orWhr = '';
            $andAr = $orAr = [];
            $andCond = $orCond = $refCheckNumRows = $andRecordMatch = $orRecordMatch = 0;

            $refs = RateRecordCondition::where('rate_record_id', $record)
                ->where('status', 1)
                ->select('ref_id', 'ref_name', 'ref_value', 'condition_type')
                ->get();

            if ($refs->isNotEmpty()) {
                $refCheck = 0;
                $refCheckNumRows = $andCond = $orCond = 1;
                $a = 0;

                foreach ($refs as $ref) {
                    $conditionType = $ref->condition_type;
                    if ($refs->count() == 1) {
                        $conditionType = 'AND';
                    }
                    if ($conditionType == 'AND') {
                        $noAnd = 0;
                        $andAr[] = ['ref_id' => $ref->ref_id, 'ref_value' => $ref->ref_value];
                        $a++;
                    } elseif ($conditionType == 'OR') {
                        $noOr = 0;
                        $orAr[] = ['ref_id' => $ref->ref_id, 'ref_value' => $ref->ref_value];
                    }
                }

                if ($andAr) {
                    $andWhr .= ' AND ( ';
                    foreach ($andAr as $i => $ar) {
                        $andWhr .= $i == 0
                            ? "( r.id='{$ar['ref_id']}' AND o.reference_id = r.name AND o.ref_value='{$ar['ref_value']}' AND o.status ='1' )"
                            : " OR ( r.id='{$ar['ref_id']}' AND o.reference_id = r.name AND o.ref_value='{$ar['ref_value']}' AND o.status ='1' )";
                    }
                    $andWhr .= ' ) ';
                }

                if ($orAr && count($orAr) > 1) {
                    $orWhr .= ' AND ( ';
                    foreach ($orAr as $i => $ar) {
                        $orWhr .= $i == 0
                            ? "( r.id='{$ar['ref_id']}' AND o.reference_id = r.name AND o.ref_value='{$ar['ref_value']}' AND o.status ='1' )"
                            : " OR ( r.id='{$ar['ref_id']}' AND o.reference_id = r.name AND o.ref_value='{$ar['ref_value']}' AND o.status ='1' )";
                    }
                    $orWhr .= ' ) ';
                } else {
                    $noOr = 1;
                }

                if ($refCheckNumRows) {
                    $andRecordMatch = $andWhr ? $this->checkAndOrConditionForRef($orderId, $andWhr) : ($noAnd ? 1 : 0);
                    $andRecordMatch = $andRecordMatch == $a ? 1 : 0;

                    $orRecordMatch = $orWhr ? $this->checkAndOrConditionForRef($orderId, $orWhr) : ($noOr ? 1 : 0);
                }

                $refCheck = $orRecordMatch && $andRecordMatch ? 1 : 0;
            }
        }
        return $refCheck;
    }

    public function checkAndOrConditionForRef($orderId, $whr)
    {
        $query = DB::select("SELECT o.id FROM order_references o, reference_master r WHERE o.order_id = ? $whr", [$orderId]);
        return count($query);
    }

    public function preferenceCheckRefAndOrCondition($orderId, $id)
    {
        $refCheck = 1;
        if ($orderId && $id > 0) {
            $noAnd = $noOr = 1;
            $andWhr = $orWhr = '';
            $andAr = $orAr = [];
            $andCond = $orCond = $refCheckNumRows = $andRecordMatch = $orRecordMatch = 0;

            $refs = DB::table('rateprefer_ref_types')
                ->where('rate_prefer_id', $id)
                ->where('status', 1)
                ->select('ref_id', 'ref_value', 'ref_condition')
                ->get();

            if ($refs->isNotEmpty()) {
                $refCheck = 0;
                $refCheckNumRows = $andCond = $orCond = 1;
                $a = 0;
                $fileLineId = ReferenceMaster::where('name', 'FI')->select('id')->first()?->id;

                if ($refs->count() == 1) {
                    $ref = $refs->first();
                    if ($ref->ref_id == $fileLineId) {
                        $orderFileRef = OrderReference::where('order_id', $orderId)
                            ->where('reference_id', 'FI')
                            ->where('status', 1)
                            ->select('ref_value')
                            ->first();
                        return $orderFileRef && $orderFileRef->ref_value == $ref->ref_value ? 1 : 0;
                    }
                }

                foreach ($refs as $ref) {
                    $conditionType = $ref->ref_condition;
                    if ($refs->count() == 1) {
                        $conditionType = 'AND';
                    }
                    if ($conditionType == 'AND') {
                        $noAnd = 0;
                        $andAr[] = ['ref_id' => $ref->ref_id, 'ref_value' => $ref->ref_value];
                        $a++;
                    } elseif ($conditionType == 'OR') {
                        $noOr = 0;
                        $orAr[] = ['ref_id' => $ref->ref_id, 'ref_value' => $ref->ref_value];
                    }
                }

                if ($andAr) {
                    $andWhr .= ' AND ( ';
                    foreach ($andAr as $i => $ar) {
                        $andWhr .= $i == 0
                            ? "( r.id='{$ar['ref_id']}' AND o.reference_id = r.name AND o.ref_value='{$ar['ref_value']}' AND o.status ='1' )"
                            : " OR ( r.id='{$ar['ref_id']}' AND o.reference_id = r.name AND o.ref_value='{$ar['ref_value']}' AND o.status ='1' )";
                    }
                    $andWhr .= ' ) ';
                }

                if ($orAr && count($orAr) > 1) {
                    $orWhr .= ' AND ( ';
                    foreach ($orAr as $i => $ar) {
                        $orWhr .= $i == 0
                            ? "( r.id='{$ar['ref_id']}' AND o.reference_id = r.name AND o.ref_value='{$ar['ref_value']}' AND o.status ='1' )"
                            : " OR ( r.id='{$ar['ref_id']}' AND o.reference_id = r.name AND o.ref_value='{$ar['ref_value']}' AND o.status ='1' )";
                    }
                    $orWhr .= ' ) ';
                } else {
                    $noOr = 1;
                }

                if ($refCheckNumRows) {
                    $andRecordMatch = $andWhr ? $this->checkAndOrConditionForRef($orderId, $andWhr) : ($noAnd ? 1 : 0);
                    $andRecordMatch = $andRecordMatch == $a ? 1 : 0;

                    $orRecordMatch = $orWhr ? $this->checkAndOrConditionForRef($orderId, $orWhr) : ($noOr ? 1 : 0);
                }

                $refCheck = $orRecordMatch && $andRecordMatch ? 1 : 0;
            }
        }
        return $refCheck;
    }

    private function getChargesThroughVasByRecord($rate_offering, $rate_record, $order_id, $user_id, $type)
    {
        // Implement VAS charges retrieval
        return []; // Placeholder
    }

    private function getChargesThroughVasByOffering($rate_offering, $type, $order_id, $user_id)
    {
        // Implement offering-based VAS charges retrieval
        return []; // Placeholder
    }

    public function getRateRecordCharges($offering, $record, $type, $order_id, $record_id, $user_id, $legsLocation = [])
    {
        $exchange_amount = $total_amount = $unit2 = $final_conversion = 0;
        $charge_ar = [];
        $foreign_currency = '';
        $cdate = Carbon::now()->format('Y-m-d H:i:s');
        $user = Auth::user();
        $org_id =  $user->org_id ?? 0;
        $user_currency = session('usr_tzone.currency', 'USD');

        $records = DB::table('raterecord_charges')
            ->where('raterecord_id', $record)
            ->where('status', 1)
            ->select('rr_charge_type', 'rr_tier_id', 'geo_tier_id', 'rr_charge_id', 'charge_basis', 'exchange_rate_id', 'min_amount', 'amount', 'currency')
            ->get();

        foreach ($records as $res) {
            $unit2 = 1;
            $tier_ar = $geo_tier_ar = [];
            $charge_id = $res->rr_charge_id;
            $rr_charge_type = $res->rr_charge_type;
            $geo_tier_id = $res->geo_tier_id;
            $tier_id = $res->rr_tier_id;
            $min_amount = $res->min_amount;
            $amount = $res->amount;
            $total_amount = $amount;
            $currency = $res->currency;
            $exchange_rate_id = $res->exchange_rate_id;
            $charge_basis = strtoupper($res->charge_basis);

            $charge = ChargeCode::where('id', $charge_id)
                ->select('charge_code')
                ->first();
            $fafcharge_code = $charge ? $charge->charge_code : '';

            if (trim($rr_charge_type) == 'FIXED' && trim($fafcharge_code) == 'FAF' && trim($charge_basis) == 'FRT') {
                continue;
            }

            if ($exchange_rate_id != '0' && $exchange_rate_id != '') {
                if ($org_id != 'PLKN') {
                    if ($user_currency != $currency) {
                        $info = [
                            'exchange_rate_id' => $exchange_rate_id,
                            'from_currency' => $currency,
                            'to_currency' => $user_currency,
                            'user_id' => $user_id,
                            'order_id' => $order_id,
                        ];
                        $exchange_amount = $this->getExchangeAmountFromCurrency($info);
                        $foreign_currency = $currency;
                    }
                } else {
                    $foreign_currency = 'EUR';
                    $info = [
                        'exchange_rate_id' => $exchange_rate_id,
                        'from_currency' => $currency,
                        'to_currency' => $user_currency,
                        'user_id' => $user_id,
                        'order_id' => $order_id,
                    ];
                    $exchange_amount = $this->getExchangeAmountFromCurrency($info);
                }
            }

            if ($exchange_amount == 0 && $currency != $user_currency) {
                $orderDate = Order::where('id', $order_id)
                    ->select('created_at')
                    ->first();
                $query = DB::table('rate_exchange_sheet')
                    ->select('id', 'exchange_rate')
                    ->where('user_id', $user_id)
                    ->where('exchange_from', $currency)
                    ->where('exchange_to', $user_currency)
                    ->where('status', 1)
                    ->where('effective_date', '<=', $orderDate->created_at ?? Carbon::today()->format('Y-m-d'))
                    ->where('expiry_date', '>=', $orderDate->created_at ?? Carbon::today()->format('Y-m-d'))
                    ->first();
                $exchange_rate_id = $query ? $query->id : 0;
                $exchange_amount = $query ? $query->exchange_rate : 0;
            }

            if (in_array($charge_basis, ['PER KG', 'KG', 'PER CBM', 'CBM', 'LDM', 'PER LDM', 'PIECE COUNT', 'GOODS VALUE'])) {
                if ($order_id != '0' && $order_id != '') {
                    $unit_ar = $this->calculatePerKgAmount($offering, $order_id, $user_id, $type, $amount, $charge_basis, 'non-vas');
                    if (!empty($unit_ar)) {
                        $final_conversion = $unit_ar['final_conversion'] ?? 0;
                    }
                    if ($final_conversion > 0) {
                        $unit2 = $final_conversion;
                    } else {
                        $orderdetails = $this->getAllOrderCargoDetails($order_id);
                        if (!empty($orderdetails)) {
                            if (in_array($charge_basis, ['PER KG', 'KG'])) {
                                $unit2 = $orderdetails['total_weight'] ?? 0;
                            } elseif (in_array($charge_basis, ['PER LDM', 'LDM'])) {
                                $unit2 = $orderdetails['total_ldm'] ?? 0;
                            } elseif (in_array($charge_basis, ['PER CBM', 'CBM'])) {
                                $unit2 = $orderdetails['total_volume'] ?? 0;
                            } elseif ($charge_basis == 'GOODS VALUE') {
                                $unit2 = $orderdetails['goods_value'] ?? 0;
                            }
                        }
                    }
                }
            }

            $total_amount = $unit2 * $total_amount;
            if ($total_amount < $min_amount) {
                $total_amount = $min_amount;
            }

            $charge_name = ChargeCode::where('id', $charge_id)
                ->select('name')
                ->first()?->name ?? 'Freight Charges';
            $charge_id = $charge_id ?: '38';

            if ($tier_id != '' && $tier_id != '0') {
                $tier_ar = $this->getRateChargeAmountByTier($offering, $tier_id, $order_id, $type, $user_id);
                if (!empty($tier_ar)) {
                    $record_id = $tier_ar['tier_id'];
                    $total_amount = $tier_ar['cost'];
                    $currency = $tier_ar['currency'] ?? $user_currency;
                    if ($org_id == 'PLKN') {
                        $user_currency = 'PLN';
                    }
                    $foreign_currency = $user_currency != $currency ? $currency : '';
                }
            }

            $amount_ar = [];
            if ($geo_tier_id != '' && $geo_tier_id != '0') {
                $geo_tier_ar = $this->getRateChargeAmountByGeoTier($offering, $geo_tier_id, $order_id, $type, $legsLocation);
                if (!empty($geo_tier_ar)) {
                    $record_id = $geo_tier_ar['geo_tier_id'];
                    $amount_ar = $geo_tier_ar['cost'];
                    if (!empty($amount_ar)) {
                        $total_amount = $amount_ar['cost'] ?? 0;
                        $min_amount = $amount_ar['min_amount'] ?? 0;
                        $currency = $amount_ar['currency'] ?? $user_currency;
                        if ($min_amount > $total_amount) {
                            $total_amount = $min_amount;
                        }
                        if ($org_id == 'PLKN') {
                            $user_currency = 'PLN';
                        }
                        $foreign_currency = $user_currency != $currency ? $currency : '';
                    }
                }
            }

            if ($rr_charge_type == 'Fixed') {
                $getraterecord_id = RateRecord::where('id', $record)
                    ->where('status', 1)
                    ->select('rate_id')
                    ->first();
                if ($getraterecord_id) {
                    $record_id = $getraterecord_id->rate_id;
                }
            }

            if ($total_amount != '0') {
                $charge_ar[] = [
                    'charge_code' => $charge_id,
                    'description' => $charge_name,
                    'quantity_unit' => '1',
                    'value' => '1',
                    'rate_id' => $record_id,
                    'amount' => $total_amount,
                    'currency' => $currency,
                    'foreign_currency' => $foreign_currency,
                    'user_id' => $user_id,
                    'created_at' => $cdate,
                    'exchange_amount' => $exchange_amount,
                ];
            }
        }

        return $charge_ar;
    }

    public function getExchangeAmountFromCurrency($info)
    {
        $user = Auth::user();
        $exchange_rate_amount = 0;
        $order_createddate = Carbon::today()->format('Y-m-d');
        $order_customer = '0';
        $org_id =  $user->org_id ?? 0;
        $curtz = $user->usr_tzone['timezone'] ?? 'UTC';

        if (!empty($info)) {
            $order_id = $info['order_id'];
            if ($order_id != '' && $order_id != '0') {
                $order = Order::where('id', $order_id)
                    ->where('user_id', $info['user_id'])
                    ->select('pickup_datetime', 'customer_id', 'org_id', 'created_at')
                    ->first();

                if ($order) {
                    $order_createddate = Carbon::parse($order->created_at)->format('Y-m-d');
                    if ($order->pickup_datetime && $order->pickup_datetime != '0000-00-00 00:00:00') {
                        $pdates = $this->getDateTimeByTimezone($curtz, $order->pickup_datetime, config('app.default_timezone', 'UTC'));
                        $order_createddate = Carbon::parse($pdates['datetime'])->format('Y-m-d');
                    }
                    $order_customer = $order->customer_id;
                    $org_id = $order->org_id;
                    $be_value = $order->be_value;
                }

                if (!empty($info['to_currency']) && !empty($info['from_currency'])) {
                    $info['customer_id'] = $order_customer;
                    $info['org_id'] = $org_id;
                    $info['be_value'] = $be_value;
                    $info['order_createddate'] = $order_createddate;
                }
            }

            $exchange_rate_amount = $this->getExchangeRateAmountForOrder($info);
        }

        return $exchange_rate_amount;
    }

    public function getExchangeRateAmountForOrder($info)
    {
        $exchange_rate_amount = 0;

        if (!empty($info)) {
            if (!empty($info['exchange_rate_id'])) {
                $exchange = RateExchangeSheet::where('rate_exchange_id', $info['exchange_rate_id'])
                    ->where('exchange_from', 'LIKE', $info['from_currency'])
                    ->where('exchange_to', 'LIKE', $info['to_currency'])
                    ->where('effective_date', '<=', $info['order_createddate'])
                    ->where('expiry_date', '>=', $info['order_createddate'])
                    ->where('status', 1)
                    ->select('exchange_rate')
                    ->first();

                if ($exchange) {
                    $exchange_rate_amount = $exchange->exchange_rate;
                }
            } else {
                if ($info['order_id'] > 0 && $info['customer_id'] > 0) {
                    $exchange = RateExchangeSheet::select('s.exchange_rate')
                        ->from('rate_exchange_sheet as s')
                        ->join('rate_exchange as r', 'r.id', '=', 's.rate_exchange_id')
                        ->where('r.customer_id', $info['customer_id'])
                        ->where('r.status', 1)
                        ->where('r.org_id', 'LIKE', $info['org_id'])
                        ->where('s.exchange_from', 'LIKE', $info['from_currency'])
                        ->where('s.exchange_to', 'LIKE', $info['to_currency'])
                        ->where('s.effective_date', '<=', $info['order_createddate'])
                        ->where('s.expiry_date', '>=', $info['order_createddate'])
                        ->where('s.status', 1)
                        ->orderBy('r.id', 'DESC')
                        ->first();

                    if ($exchange) {
                        $exchange_rate_amount = $exchange->exchange_rate;
                    } else {
                        $exchange = RateExchangeSheet::select('s.exchange_rate')
                            ->from('rate_exchange_sheet as s')
                            ->join('rate_exchange as r', 'r.id', '=', 's.rate_exchange_id')
                            ->where('r.customer_id', '0')
                            ->where('r.status', 1)
                            ->where('r.org_id', 'LIKE', $info['org_id'])
                            ->where('s.exchange_from', 'LIKE', $info['from_currency'])
                            ->where('s.exchange_to', 'LIKE', $info['to_currency'])
                            ->where('s.effective_date', '<=', $info['order_createddate'])
                            ->where('s.expiry_date', '>=', $info['order_createddate'])
                            ->where('s.status', 1)
                            ->orderBy('r.id', 'DESC')
                            ->first();

                        if ($exchange) {
                            $exchange_rate_amount = $exchange->exchange_rate;
                        } else {
                            $exchange = RateExchangeSheet::select('s.exchange_rate')
                                ->from('rate_exchange_sheet as s')
                                ->join('rate_exchange as r', 'r.id', '=', 's.rate_exchange_id')
                                ->where('r.org_id', 'LIKE', $info['org_id'])
                                ->where('s.exchange_from', 'LIKE', $info['from_currency'])
                                ->where('s.exchange_to', 'LIKE', $info['to_currency'])
                                ->where('s.effective_date', '<=', $info['order_createddate'])
                                ->where('s.expiry_date', '>=', $info['order_createddate'])
                                ->where('s.status', 1)
                                ->orderBy('r.id', 'DESC')
                                ->first();

                            if ($exchange) {
                                $exchange_rate_amount = $exchange->exchange_rate;
                            }
                        }
                    }
                }
            }

            if ($exchange_rate_amount == 0) {
                $exchange = RateExchangeSheet::select('s.exchange_rate')
                    ->from('rate_exchange_sheet as s')
                    ->join('rate_exchange as r', 'r.id', '=', 's.rate_exchange_id')
                    ->where('r.org_id', 'LIKE', $info['org_id'])
                    ->where('s.exchange_from', 'LIKE', $info['from_currency'])
                    ->where('s.exchange_to', 'LIKE', $info['to_currency'])
                    ->where('s.status', 1)
                    ->where('s.effective_date', '<=', $info['order_createddate'])
                    ->where('s.expiry_date', '>=', $info['order_createddate'])
                    ->orderBy('r.id', 'DESC')
                    ->first();

                if ($exchange) {
                    $exchange_rate_amount = $exchange->exchange_rate;
                }
            }
        }

        return $exchange_rate_amount;
    }

    private function calculatePerKgAmount($offering, $order_id, $user_id, $type, $amount, $charge_basis, $context)
    {
        // Implement per-kg amount calculation
        return []; // Placeholder
    }


    private function getAllOrderCargoDetails($order_id)
    {
        // Implement cargo details retrieval
        return []; // Placeholder
    }

    // Placeholder methods (to be implemented based on your requirements)
    private function insertRecords($revenue_ar, $charge_ar, $empty_ar, $charge_of_country, $charge_ar_vas)
    {
        // Implement insertion logic for revenues and charges
        // Return the revenue row ID
        return 0; // Placeholder
    }

    private function addFafChargesToOrder($order_id, $rev_row_id, $rate_record, $source)
    {
        // Implement FAF charges logic
        return true; // Placeholder
    }

    private function getRateChargeAmountByTier($offering, $tier_id, $order_id, $type, $user_id)
    {
        // Implement tier-based charge retrieval
        return []; // Placeholder
    }

    private function getRateChargeAmountByGeoTier($offering, $geo_tier_id, $order_id, $type, $legsLocation)
    {
        // Implement geo-tier-based charge retrieval
        return []; // Placeholder
    }

    public function getOrderToEdit($id)
    {
        return Order::select([
            'o.id',
            'o.order_id',
            'o.pickup_datetime',
            'o.pickup_endtime',
            'o.plat',
            'o.plng',
            'o.dlat',
            'o.dlng',
            'o.shipment_id',
            'o.delivery_datetime',
            'o.drop_endtime',
            'o.pickup_company as pickup',
            'o.delivery_company as delivery',
            'o.pickup_address1',
            'o.delivery_address1',
            'o.pickup_address2',
            'o.delivery_address2',
            'o.pickup_city',
            'o.delivery_city',
            'o.pickup_country',
            'o.delivery_country',
            'o.pickup_pincode',
            'o.delivery_pincode',
            'o.org_id',
            'o.be_value',
            'o.product',
            'o.goods_value',
            'o.currency',
            'o.transport_mode',
            'o.vehicle_type',
            'o.quantity',
            'o.customer_id',
            'o.vendor_id',
            'o.pickup_custid',
            'o.pickup_partyid',
            'o.drop_custid',
            'o.drop_partyid',
            'o.trip_sts',
            'o.shift_id',
            'o.trip_id',
            'o.status',
            'o.order_status',
            'o.created_at',
            'o.updated_at',
            'o.shipmentid',
            'o.consignee_id',
            'o.shipper_id as o_shipper_id',
            'o.created_source',
            'o.shipment_type',
            'o.region',
            'd.order_type',
            'd.shipper_id',
            'd.service',
            'd.delivery_term',
            'd.incoterm',
            'd.delivery_note',
            'd.purchase_order',
            'd.notify_party',
            'd.lane_reference',
            'd.distance',
            'd.customs_required',
            'd.high_cargo_value',
            'd.valorance_insurance',
            'd.temperature_control',
            'd.docs_received_datetime',
            'd.docs_sent_datetime',
            'd.consignment_note',
            'd.order_remarks',
            'd.cost_center_id',
            'o.external_order_id',
            'o.logicalreceiver',
            'o.physicalreceiver',
            'o.physicalsender',
            'o.logicalsender',
            'o.volume',
            'o.weight',
            'o.user_id',
            'st.type_name as shipment_type_name',
            'r.region_name',
            'o.third_party_post'
        ])
            ->from('orders as o')
            ->leftJoin('order_details as d', 'o.id', '=', 'd.order_row_id')
            ->leftJoin('shipment_types as st', 'o.shipment_type', '=', 'st.id')
            ->leftJoin('regions as r', 'o.region', '=', 'r.id')
            ->where('o.id', $id)
            ->first();
    }

    public function getDeliveryTermsByIncoterm($incoterm)
    {
        if (!$incoterm) {
            return [];
        }

        return DeliveryTerm::where('incoterm', $incoterm)
            ->select('term_id as id', 'name', 'incoterm')
            ->orderByDesc('created_at')
            ->get()
            ->map(fn($res) => ['id' => $res->id, 'name' => $res->name, 'incoterm' => $res->incoterm])
            ->toArray();
    }

    public function getPickupDetails($id)
    {
        $pickup = SxPartyMembers::where(['status' => 1, 'id' => $id])
            ->select('id', 'name', 'address', 'pincode', 'code', 'country')
            ->first();

        return [
            'status' => 'success',
            'message' => 'Pickup details retrieved successfully',
            'data' => $pickup ? [
                'id' => $pickup->id,
                'name' => $pickup->name,
                'partyId' => $pickup->code,
                'address' => $pickup->address,
                'pincode' => $pickup->pincode,
                'country' => $pickup->country,
            ] : []
        ];
    }

    public function getShipperList($orgId)
    {
        $parties = [];

        $shippers = SxPartyMembers::where('status', 1)
            ->whereNotNull('org_id')
            ->where('org_id', $orgId)
            ->select('id', 'name', 'mobile', 'code', 'country', 'street', 'city', 'email', 'org_id', 'be_value')
            ->groupBy('id')
            ->orderByDesc('created_at')
            ->get();

        foreach ($shippers as $res) {
            $parties[] = [
                'check' => "<input class='shipperlist' type='radio' name='selectshipper' id='shipperlist_" . $res->id . "' value='" . $res->id . "' onchange=selectshipper(" . $res->id . ")>",
                'id' => $res->id,
                'partyId' => $res->code,
                'name' => $res->name,
                'phone' => $res->mobile,
                'email' => wordwrap($res->email, 25, "<br />\n"),
                'orgId' => $res->org_id,
                'beValue' => $res->be_value,
                'city' => $res->city,
                'country' => $res->country,
                'street' => $res->street
            ];
        }

        return $parties;
    }

    public function updateOrderReferencesBatch($orderId, $references)
    {
        if (empty($references)) {
            return;
        }

        DB::beginTransaction();
        try {
            foreach ($references as $referenceId => $value) {
                $this->updateOrderReference($orderId, $referenceId, $value);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Exception updating order references: " . $e->getMessage());
        }
    }

    private function updateOrderReference(int $orderId, string $referenceId, string $referenceValue): void
    {
        $existing = OrderReference::where([
            'order_id' => $orderId,
            'reference_id' => $referenceId,
            'ref_value' => $referenceValue,
            'status' => 1
        ])->first();

        if ($existing) {
            return;
        }

        $existingWithoutValue = OrderReference::where([
            'order_id' => $orderId,
            'reference_id' => $referenceId,
            'status' => 1
        ])->first();

        if ($existingWithoutValue) {
            $existingWithoutValue->update(['ref_value' => $referenceValue]);
        } else {
            OrderReference::create([
                'order_id' => $orderId,
                'reference_id' => $referenceId,
                'ref_value' => $referenceValue,
                'created_at' => Carbon::now()->format('Y-m-d H:i:s')
            ]);
        }
    }

    public function processShipperChange(
        $orderId,
        $newShipperId,
        $oldPickupId,
        $shipperPartyId,
        $userId,
        $cdate,
        &$pickupCustid,
        &$pickupName,
        &$pickupState,
        &$pickupAddress,
        &$pickupCountry,
        &$pickupStreet,
        &$pickupPincode,
        &$pickupCity
    ) {
        $shipperDetails = $this->getPartyDetailsOptimized($newShipperId);
        if (!$shipperDetails) {
            return;
        }

        $pickupCustid = $shipperDetails->code;
        $pickupName = $shipperDetails->name;
        $pickupState = $shipperDetails->state;
        $pickupAddress = $shipperDetails->address;
        $pickupCountry = $shipperDetails->country;
        $pickupStreet = $shipperDetails->street;
        $pickupPincode = $shipperDetails->pincode;
        $pickupCity = $shipperDetails->city;

        DB::beginTransaction();
        try {
            if ($oldPickupId != 0) {
                if ($shipperPartyId != 0) {
                    OrderParty::where('id', $shipperPartyId)->update(['status' => 0]);
                }
                OrderpartyAddress::where([
                    'order_id' => $orderId,
                    'party_master_id' => $oldPickupId,
                    'status' => 1
                ])->update(['status' => 0]);
            }

            $this->upsertOrderPartyAddress($orderId, $newShipperId, [
                'location_id' => $pickupCity,
                'street' => $pickupStreet,
                'state' => $pickupState,
                'address' => $pickupAddress,
                'pincode' => $pickupPincode,
                'country' => $pickupCountry,
                'user_id' => $userId,
                'status' => 1
            ], $cdate);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Exception in shipper change: " . $e->getMessage());
        }
    }

    public function upsertOrderPartyAddress($orderId, $partyMasterId, $addressData, $cdate)
    {
        $existingAddress = OrderpartyAddress::where([
            'order_id' => $orderId,
            'party_master_id' => $partyMasterId,
            'status' => 1
        ])->first();

        $addressData['order_id'] = $orderId;
        $addressData['party_master_id'] = $partyMasterId;

        if ($existingAddress) {
            $existingAddress->update($addressData);
        } else {
            $addressData['created_at'] = $cdate;
            OrderpartyAddress::create($addressData);
        }
    }

    public function loadExistingShipperAddress(
        $orderId,
        $shipperId,
        &$pickupCity,
        &$pickupCountry,
        &$pickupStreet,
        &$pickupPincode,
        &$pickupState,
        &$pickupAddress
    ) {
        $address = OrderpartyAddress::where([
            'order_id' => $orderId,
            'party_master_id' => $shipperId,
            'status' => 1
        ])->select('location_id', 'street', 'state', 'address', 'pincode', 'country')
            ->first();

        if ($address) {
            $pickupCity = $address->location_id;
            $pickupCountry = $address->country;
            $pickupStreet = $address->street;
            $pickupPincode = $address->pincode;
            $pickupState = $address->state;
            $pickupAddress = $address->address;
        }
    }

    public function processDeliveryChange(
        $orderId,
        $newDeliveryId,
        $oldDropId,
        $consigneePartyId,
        $userId,
        $cdate,
        &$dropId,
        &$dropCustid,
        &$dropName,
        &$dropState,
        &$dropAddress,
        &$dropCountry,
        &$dropStreet,
        &$dropPincode,
        &$dropCity
    ) {
        $deliveryDetails = $this->getPartyDetailsOptimized($newDeliveryId);
        if (!$deliveryDetails) {
            return;
        }

        $dropId = $newDeliveryId;
        $dropCustid = $deliveryDetails->code;
        $dropName = $deliveryDetails->name;
        $dropState = $deliveryDetails->state;
        $dropAddress = $deliveryDetails->address;
        $dropCountry = $deliveryDetails->country;
        $dropStreet = $deliveryDetails->street;
        $dropPincode = $deliveryDetails->pincode;
        $dropCity = $deliveryDetails->city;

        DB::beginTransaction();
        try {
            if ($oldDropId != 0) {
                OrderpartyAddress::where([
                    'order_id' => $orderId,
                    'party_master_id' => $oldDropId,
                    'status' => 1
                ])->update(['status' => 0]);
            }

            if ($consigneePartyId != '0' && $consigneePartyId != 0) {
                OrderParty::where('id', $consigneePartyId)->update(['status' => 0]);
            }

            $this->upsertOrderPartyAddress($orderId, $newDeliveryId, [
                'location_id' => $dropCity,
                'street' => $dropStreet,
                'state' => $dropState,
                'address' => $dropAddress,
                'pincode' => $dropPincode,
                'country' => $dropCountry,
                'user_id' => $userId,
                'status' => 1
            ], $cdate);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Exception in delivery change: " . $e->getMessage());
        }
    }

    public function loadExistingDeliveryAddress(
        $orderId,
        $deliveryId,
        &$dropCity,
        &$dropState,
        &$dropCountry,
        &$dropStreet,
        &$dropPincode,
        &$dropAddress
    ) {
        $address = OrderpartyAddress::where([
            'order_id' => $orderId,
            'party_master_id' => $deliveryId,
            'status' => 1
        ])->select('location_id', 'street', 'state', 'address', 'pincode', 'country')
            ->first();

        if ($address) {
            $dropCity = $address->location_id;
            $dropState = $address->state;
            $dropCountry = $address->country;
            $dropStreet = $address->street;
            $dropPincode = $address->pincode;
            $dropAddress = $address->address;
        }
    }

    public function getCoordinatesForAddress($addressParts)
    {
        // Filter out empty parts and create address string
        $addressParts = array_filter($addressParts, function ($part) {
            return !empty(trim($part));
        });

        if (empty($addressParts)) {
            return null;
        }

        $addressString = implode(", ", $addressParts);

        try {
            $coordinates = $this->getlatlngsbyplace($addressString);

            if (is_array($coordinates) && count($coordinates) >= 2) {
                return [
                    'lat' => $coordinates[0] ?? null,
                    'lng' => $coordinates[1] ?? null
                ];
            }
        } catch (\Exception $e) {
            Log::error("Geolocation API error for address '{$addressString}': " . $e->getMessage());
        }

        return null;
    }

    public function updateShipmentEmployeeData($orderId, $bookingId, $ePickup, $lPickup, $eDelivery, $lDelivery, $totalWeight)
    {
        $shipment = Order::select([
            'orders.shift_id',
            'shiporder_stop_sequence.id as emp_id',
            'shiporder_stop_sequence.stop_id',
            'shiporder_stop_sequence.drop_stopid'
        ])
            ->leftJoin('shiporder_stop_sequence', function ($join) use ($bookingId) {
                $join->on('orders.shift_id', '=', 'shiporder_stop_sequence.shift_id')
                    ->where('shiporder_stop_sequence.order_id', $bookingId);
            })
            ->where('orders.id', $orderId)
            ->first();

        if ($shipment && !empty($shipment->emp_id)) {
            DB::beginTransaction();
            try {
                $employeeUpdates = [
                    'pickup_datetime' => $ePickup,
                    'drop_datetime' => $lDelivery,
                    'startdate' => $ePickup,
                    'enddate' => $lDelivery,
                    'shipment_weight' => $totalWeight
                ];

                $pickupStopUpdates = [
                    'startdate' => $ePickup,
                    'enddate' => $lPickup,
                    'weight' => $totalWeight
                ];

                $deliveryStopUpdates = [
                    'startdate' => $eDelivery,
                    'enddate' => $lDelivery,
                    'weight' => $totalWeight
                ];

                ShiporderStopSequence::where('id', $shipment->emp_id)->update($employeeUpdates);

                if (!empty($shipment->stop_id)) {
                    ShiporderStop::where('id', $shipment->stop_id)->update($pickupStopUpdates);
                }

                if (!empty($shipment->drop_stopid)) {
                    ShiporderStop::where('id', $shipment->drop_stopid)->update($deliveryStopUpdates);
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error("Exception updating shipment employee data: " . $e->getMessage());
            }
        }
    }

    public function updateAdditionalStopDetails($orderId, $eDelivery, $lDelivery)
    {
        $stop = Order::select([
            'orders.order_id',
            'shiporder_stop_sequence.drop_stopid'
        ])
            ->join('shiporder_stop_sequence', 'orders.order_id', '=', 'shiporder_stop_sequence.order_id')
            ->where('orders.id', $orderId)
            ->whereNotNull('shiporder_stop_sequence.drop_stopid')
            ->first();

        if ($stop && !empty($stop->drop_stopid)) {
            ShiporderStop::where('id', $stop->drop_stopid)->update([
                'startdate' => $eDelivery,
                'enddate' => $lDelivery
            ]);
        }
    }

    public function datatablesQuery(array $selectColumns, array $dataTableSortOrdering, array $whereCondition, string $indexColumn, array $groupBy, Request $request)
    {
        $search = $request->input('search.value', '');
        $start = $request->input('start', 0);
        $length = $request->input('length', 10);
        $order = $request->input('order', []);

        $query = CargoDetail::select($selectColumns)
            ->join('order_cargodetails', 'order_cargodetails.cargo_id', '=', 'cargo_details.id')
            ->where($whereCondition);

        if ($search) {
            $query->where(function ($q) use ($selectColumns, $search) {
                foreach ($selectColumns as $column) {
                    $column = explode(' as ', $column)[0];
                    $q->orWhere($column, 'like', "%{$search}%");
                }
            });
        }

        if ($groupBy) {
            $query->groupBy($groupBy);
        }

        if (!empty($order)) {
            $columnIndex = $order[0]['column'];
            $orderByColumn = $dataTableSortOrdering[$columnIndex] ?? $indexColumn;
            $sortType = $order[0]['dir'];
            $query->orderBy($orderByColumn, $sortType);
        } else {
            $query->orderBy($indexColumn, 'desc');
        }

        $totalRecords = DB::table('cargo_details')
            ->join('order_cargodetails', 'order_cargodetails.cargo_id', '=', 'cargo_details.id')
            ->where($whereCondition)
            ->count($indexColumn ?: '*');

        if ($length > 0) {
            $query->skip($start)->take($length);
        }

        $records = $query->get();
        $recordsFiltered = $records->count();

        return [
            'data' => $records,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $recordsFiltered
        ];
    }

    public function getPackagesWhereCondition()
    {
        return [
            'BD',
            'BG',
            'BI',
            'BL',
            'BX',
            'CA',
            'CE',
            'CN',
            'CY',
            'DM',
            'HP',
            'PT',
            'CHEPB',
            'CHEPR',
            'CHEPT',
            'DUNN',
            'HIRED',
            'PALLD',
            'PALLO',
            'MXCON',
            'PLCON',
            'LOS',
            '20GP',
            '20HC',
            '20OT',
            '40GP',
            '40HC',
            '40OT'
        ];
    }

    public function getCustomerCodeByIds(array $ids)
    {
        return SxPartyMembers::whereIn('id', $ids)->pluck('code')->toArray();
    }

    public function getTableRowData(array $where, string $select, string $table): array
    {
        $query = app('App\Models\\' . ucfirst($table))
            ->selectRaw($select)
            ->where($where)
            ->first();

        return $query ? $query->toArray() : [];
    }

    public function newGetTableRowData(array $where, array $select, string $model): array
    {
        $query = $model::select($select)
            ->where($where)
            ->first();

        return $query ? $query->toArray() : [];
    }


    public function updateTableData(string $modelClass, array $data, array $where)
    {
        return $modelClass::where($where)->update($data);
    }

    public function getTableData(array $where, array $select, string $modelClass)
    {
        $query = $modelClass::select($select)->where($where);
        return $query->get()->toArray();
    }

    // Placeholder for getTrafficCodeDetails (requires implementation based on sgmyjfrmodel)
    private function getTrafficCodeDetails(array $data)
    {
        // Implement logic to fetch traffic_code, spill_one, spill_two based on service, org_id, be_value
        // This is a placeholder as the original sgmyjfrmodel is not provided
        return [
            'traffic_code' => '',
            'spill_one' => '',
            'spill_two' => ''
        ];
    }

    // Placeholder for checkHistoryJfrExistingTripOrders
    private function checkHistoryJfrExistingTripOrders($debtor_jfr)
    {
        // Implement logic to check historical JFR for trip orders
        return $debtor_jfr;
    }

    // Placeholder for generateNewJfrForFirstOrderForTrip
    private function generateNewJfrForFirstOrderForTrip(array $data_info, $flag, $prefix)
    {
        // Implement logic to generate new JFR for first order in trip
        return now()->format('YmdHis');
    }

    // Placeholder for checkJfrExistsInRevenue
    private function checkJfrExistsInRevenue(array $data_info, $debtor_jfr)
    {
        // Implement logic to check if JFR exists in revenues
        return $debtor_jfr;
    }

    // Helper to increment JFR code
    private function incrementJfr($jfr)
    {
        if (strlen($jfr) === 14 && is_numeric(substr($jfr, -6))) {
            $numeric_part = (int)substr($jfr, -6);
            $numeric_part++;
            return substr($jfr, 0, 8) . sprintf('%06d', $numeric_part);
        }
        return '';
    }

    public function getProductMasterData($orderDate = '', $lessDate = '', $orgId, $beValue)
    {
        $products = [];

        $query = Product::where('status', 1)->select('id', 'name');

        if ($orderDate !== '' && $lessDate !== '' && $orderDate < $lessDate) {
            $query->where(function ($q) use ($orgId) {
                $q->where('org_id', $orgId);
            });
        } else {
            $branchProductsExist = Product::where('status', 1)
                ->where('be_value', $beValue)
                ->where('org_id', $orgId)
                ->exists();

            if ($branchProductsExist) {
                $query->where(function ($q) use ($orgId, $beValue) {
                    $q->where(function ($q1) use ($orgId, $beValue) {
                        $q1->where('org_id', $orgId)
                            ->where(function ($q2) use ($beValue) {
                                $q2->where('be_value', $beValue)
                                    ->orWhereNull('be_value');
                            });
                    })->orWhere(function ($q1) {
                        $q1->whereNull('org_id')
                            ->whereNull('be_value');
                    });
                });
            } else {
                $query->where(function ($q) use ($orgId) {
                    $q->where('org_id', $orgId)
                        ->orWhereNull('org_id');
                })->whereNull('be_value');
            }
        }

        $results = $query->get();

        foreach ($results as $row) {
            $products[] = [
                'name' => $row->name,
                'id' => $row->id
            ];
        }

        return $products;
    }

    public function getOrderStatus()
    {
        $orderStatuses = [];

        $results = Status::where('status_type', '0')
            ->where('status', 1)
            ->select('name')
            ->orderBy('id', 'ASC')
            ->get();

        foreach ($results as $res) {
            $orderStatuses[] = $res->name;
        }

        return $orderStatuses;
    }

    public function getServiceMasterData($orderDate = '', $lessDate = '', $orgId, $beValue)
    {
        $services = [];

        $query = ServiceMaster::where(function ($q) use ($orgId, $beValue) {
            $q->where('org_id', $orgId)
                ->where(function ($q2) use ($beValue) {
                    $q2->where('be_value', $beValue);
                    // ->orWhereNull('be_value');
                })
                ->orWhere(function ($q2) {
                    $q2->whereNull('org_id');
                    // ->whereNull('be_value');
                });
        })->select('id', 'service_id', 'name');

        $results = $query->get();

        foreach ($results as $res) {
            $services[] = [
                'rowId' => $res->id,
                'id' => $res->service_id,
                'name' => $res->name
            ];
        }

        return $services;
    }

    public function getShipmentTypes($orgId, $beValue)
    {
        return ShipmentType::where('org_id', $orgId)
            // ->where('be_value', $beValue)
            ->select('id', 'type_name as name')
            ->get()
            ->map(fn($res) => ['id' => $res->id, 'name' => $res->name])
            ->toArray();
    }

    public function getRegions($orgId, $beValue)
    {
        return Regions::where('org_id', $orgId)
            // ->where('be_value', $beValue)
            ->select('id', 'region_name as name')
            ->get()
            ->map(fn($res) => ['id' => $res->id, 'name' => $res->name])
            ->toArray();
    }

    public function viewOrder($id, $urlString, $orgId, $userId, $currency)
    {
        $data = [
            'currency' => $currency,
            'orderDetails' => [],
            'pickupDetails' => [],
            'dropDetails' => [],
            'shipperDetails' => [],
            'vatcategory' => [],
            'spotondata' => [],
            'spotonLrExist' => 0,
            'businessType' => '',
            'amazonEdi' => '',
            'arrowTracking' => [],
            'arrowXLData' => [],
        ];

        $order = $this->getOrderToEdit($id);
        if (!$order) {
            return $data;
        }

        $data['orderDetails'] = $this->getOrderDetails($order, $orgId);
        $data['pickupDetails'] = $this->getViewPickupDetails($order);
        $data['dropDetails'] = $this->getDropDetails($order);
        $data['shipperDetails'] = $this->getShipperDetails($order);
        $data['vatcategory'] = $this->getVatCategory($orgId);
        $data['spotondata'] = $this->getSpotonData($id);
        $data['spotonLrExist'] = !empty($data['spotondata']) ? 1 : 0;
        $data['businessType'] = ''; // Assuming business_type is not available in Auth::user()
        $data['amazonEdi'] = User::where('id', $userId)->where('status', 'Active')->value('amazon_edi') ?? '';
        $data['arrowTracking'] = $this->getArrowTracking($id);
        $data['arrowXLData'] = $orgId === 'ARROWXL' && $urlString ? $this->getArrowXLData($id, urldecode($urlString)) : [];

        return $data;
    }

    protected function getOrderDetails($order, $orgId)
    {
        $timezone = config('app.timezone', 'UTC');
        $orderDetails = [
            'id' => $order->id,
            'orderId' => $order->order_id,
            'shipmentId' => '',
            'orderStatus' => $this->getOrderViewStatus($order->trip_id, $order->trip_sts),
            'earlyPickup' => $this->formatDate($order->pickup_datetime, $timezone),
            'earlyDelivery' => $this->formatDate($order->delivery_datetime, $timezone),
            'latePickup' => $this->formatDate($order->pickup_endtime, $timezone),
            'lateDelivery' => $this->formatDate($order->drop_endtime, $timezone),
            'product' => $order->product,
            'incoterm' => $order->incoterm,
            'deliveryNote' => $order->delivery_note,
            'shipmentType' => $order->shipment_type_name,
            'region' => $order->region_name,
            'purchaseOrder' => '',
            'notifyParty' => $order->notify_party,
            'goodsValue' => $order->goods_value,
            'currency' => $order->currency,
            'laneReference' => $order->lane_reference,
            'distance' => $order->distance,
            'customsRequired' => $order->customs_required,
            'highCargoValue' => $order->high_cargo_value,
            'valoranceInsurance' => $order->valorance_insurance,
            'temperatureControl' => $order->temperature_control,
            'orgId' => $order->org_id,
            'beValue' => $order->be_value,
            'createdon' => $order->createdon,
            'transportMode' => '',
            'pickupInst' => '',
            'deliveryInst' => '',
            'containerNo' => '',
            'docsReceivedDatetime' => $this->formatDate($order->docs_received_datetime, $timezone),
            'docsSentDatetime' => $this->formatDate($order->docs_sent_datetime, $timezone),
            'externalOrderId' => $order->external_order_id ?? '',
            'crReferance' => '',
            'deliveryDatetime' => $order->delivery_datetime ?? '',
            'pickupDatetime' => $order->pickup_datetime ?? '',
            'deliveryPincode' => $order->delivery_pincode ?? '',
            'orderType' => '',
            'costCenter' => '',
            'service' => '',
            'deliveryTerm' => '',
        ];

        if ($order->transport_mode) {
            $orderDetails['transportMode'] = TransportMode::where('code', $order->transport_mode)->value('name') ?? '';
        }

        $references = OrderReference::where('order_id', $order->id)
            ->whereIn('reference_id', ['DQ', 'PO', 'ORD_DLVINST', 'ORD_PIKINST', 'CTR', 'CR'])
            ->pluck('ref_value', 'reference_id')
            ->toArray();

        $orderDetails['shipmentId'] = $references['DQ'] ?? '';
        $orderDetails['deliveryInst'] = $references['ORD_DLVINST'] ?? '';
        $orderDetails['pickupInst'] = $references['ORD_PIKINST'] ?? '';
        $orderDetails['containerNo'] = $references['CTR'] ?? '';
        $orderDetails['purchaseOrder'] = $references['PO'] ?? '';
        $orderDetails['crReferance'] = $references['CR'] ?? '';

        if ($order->order_type) {
            $orderDetails['orderType'] = OrderType::where('id', $order->order_type)
                ->where('status', '1')
                ->where('org_id', $order->org_id)
                ->value('type_name') ?? '';
        }

        if ($order->cost_center_id) {
            $orderDetails['costCenter'] = OrderType::where('id', $order->cost_center_id)
                ->where('status', '1')
                ->where('org_id', $order->org_id)
                ->value('type_name') ?? '';
        }

        if ($order->delivery_term) {
            $deliveryTerm = DeliveryTerm::where('term_id', $order->delivery_term)->first();
            $orderDetails['deliveryTerm'] = $deliveryTerm ? "{$deliveryTerm->term_id}-{$deliveryTerm->name}" : '';
        }

        if ($order->service) {
            $service = ServiceMaster::where('id', $order->service)->first();
            $orderDetails['service'] = $service ? "{$service->service_id}-{$service->name}" : '';
        }

        return $orderDetails;
    }

    protected function getViewPickupDetails($order)
    {
        $pickup = SxPartyMembers::where('status', 1)
            ->where('id', $order->customer_id)
            ->select('id', 'name', 'address', 'pincode', 'code', 'country')
            ->first();

        return $pickup ? [
            'id' => $pickup->id,
            'name' => $pickup->name,
            'partyId' => $pickup->code,
            'address' => $pickup->address,
            'pincode' => $pickup->pincode,
            'country' => $pickup->country,
        ] : [];
    }

    protected function getShipperDetails($order)
    {
        return [
            'name' => $order->pickup,
            'street' => $order->pickup_address1,
            'state' => $order->pickup_address2,
            'city' => $order->pickup_city,
            'country' => $order->pickup_country,
            'pincode' => $order->pickup_pincode,
        ];
    }

    protected function getDropDetails($order)
    {
        $dropDetails = [
            'name' => $order->delivery,
            'street' => $order->delivery_address1,
            'state' => $order->delivery_address2,
            'city' => $order->delivery_city,
            'country' => $order->delivery_country,
            'pincode' => $order->delivery_pincode,
        ];

        $partyDetails = $this->getPartyDetails($order->id);
        $dropDetails = array_merge($dropDetails, $partyDetails['consignee'] ?? []);

        return $dropDetails;
    }

    protected function getPartyDetails($orderId)
    {
        $parties = SxPartyMembers::select(
            'sx_party_members.id',
            'sx_party_members.party_type_id',
            'sx_party_members.name',
            'sx_party_members.mobile',
            'sx_party_members.email',
            'sx_party_members.code',
            'sx_party_members.fax',
            'order_parties.party_type',
            'au_carrier_postalcodes.id as aucp_id',
            'au_carrier_postalcodes.email as aucp_email',
            'au_label_consignee_mobile_num.id as acm_id',
            'au_label_consignee_mobile_num.phoneNum as acm_phone'
        )
            ->join('order_parties', fn($join) => $join->on('sx_party_members.id', '=', 'order_parties.party_id')->where('order_parties.status', 1))
            ->join('order_references as orf', fn($join) => $join->on('order_parties.order_id', '=', 'orf.order_id')->where('order_parties.order_id', $orderId))
            ->leftJoin('au_carrier_postalcodes', 'au_carrier_postalcodes.site', '=', 'orf.ref_value')
            ->leftJoin('au_label_consignee_mobile_num', 'au_label_consignee_mobile_num.storeCode', '=', 'orf.ref_value')
            ->where('sx_party_members.status', 1)
            ->where('order_parties.order_id', $orderId)
            ->groupBy('order_parties.party_type')
            ->get();

        $result = ['shipper' => [], 'consignee' => []];
        foreach ($parties as $party) {
            $partyType = SxPartyTypes::where('id', $party->party_type_id)->value('type_name');
            if ($partyType === 'Consignee') {
                $result['consignee'] = [
                    'name' => $party->name,
                    'phone' => $party->mobile,
                    'email' => $party->email,
                    'fax' => $party->fax,
                    'partyId' => $party->code,
                    'aucpEmail' => $party->aucp_email,
                    'acmPhone' => $party->acm_phone,
                ];
            } elseif ($partyType === 'Shipper') {
                $result['shipper'] = [
                    'name' => $party->name,
                    'phone' => $party->mobile,
                    'email' => $party->email,
                    'fax' => $party->fax,
                    'partyId' => $party->code,
                    'aucpEmail' => $party->aucp_email,
                    'acmPhone' => $party->acm_phone,
                ];
            }
        }

        return $result;
    }

    protected function getVatCategory($orgId)
    {
        return VatCategory::where('org_id', $orgId)
            ->where('status', '1')
            ->select('id', 'description', 'vat_category', 'vat_percentage')
            ->get()
            ->map(fn($res) => [
                'id' => $res->id,
                'val' => "{$res->id}_{$res->vat_category}",
                'desc' => "{$res->description} ({$res->vat_category}-{$res->vat_percentage})",
            ])
            ->toArray();
    }

    protected function getSpotonData($orderId)
    {
        return OrderReference::where('order_id', $orderId)
            ->where('reference_id', 'BN')
            ->select('ref_value', 'updatedon')
            ->get()
            ->toArray();
    }

    protected function getArrowTracking($orderId)
    {
        return OrderStatus::select(
            'order_status.id',
            'order_status.status_id',
            'order_status.status_date',
            'order_status.status_code',
            'status_master.status_name',
            'status_master.arrowxl_defination',
            'status_master.description'
        )
            ->join('status_master', fn($join) => $join->on('order_status.status_id', '=', 'status_master.status_code'))
            ->where('order_status.order_id', $orderId)
            ->where('order_status.status', 1)
            ->whereRaw('order_status.status_date = (
                SELECT MAX(o2.status_date)
                FROM order_status o2
                WHERE o2.order_id = order_status.order_id
                AND o2.status_id = order_status.status_id
                AND o2.status = 1
            )')
            ->groupBy('order_status.status_id')
            ->orderBy('order_status.status_date')
            ->get()
            ->toArray();
    }

    protected function getArrowXLData($orderId, $urlString)
    {
        $orderData = $this->getArrowXLOrderData($orderId);
        if (!$orderData) {
            return [];
        }

        $titles = [
            '1.Restricted Post Code- 24 HR Fail - Transit Time' => 'Restricted Post Code - 24 HR Fail - Transit Time - Stock Received into Network day(+1) is not included in the 24HR service',
            '2.Restricted Post Code- 48 HR Fail - Transit Time' => 'Restricted Post Code - 48 HR Fail - Transit Time - Stock Received into Network day(+2) is not included in the 48HR service',
            '3.Restricted Post Code- 24 HR delivery' => 'Restricted Post Code - 24 HR delivery - Hours difference between delivered date and Stock Received into Network date > 24Hrs',
            '4.Restricted Post Code- 48 HR delivery' => 'Restricted Post Code - 48 HR delivery - Hours difference between delivered date and Stock Received into Network > 48Hrs',
            '5.Restricted Post Code-  24 HR PDD - Days Of Coverage' => 'Restricted Post Code - Days Of Coverage - 24 HR PDD - PDD day is not included in the 24HR service',
            '6.Restricted Post Code-  48 HR PDD - Days Of Coverage' => 'Restricted Post Code - Days Of Coverage - 48 HR PDD - PDD day is not included in the 48HR service',
            '7.Restricted Post Code-  7 Day PDD  - Days Of Coverage' => 'Restricted Post Code - Days Of Coverage - 7 Day PDD - No 24 or 48HR service and day difference between Stock Received into Network day and pdd is < 7 days',
            '8.Restricted Post Code- 24 HR Miscalculation' => 'Restricted Post Code - 24 HR Miscalculation - Stock Received into Network day(+1) is not included in the 24HR service and Stock Received into Network day(+1 day) > PDD',
            '9.Restricted Post Code- 48 HR Miscalculation' => 'Restricted Post Code - 48 HR Miscalculation - Stock Received into Network day(+2) is included in the 48HR service and Stock Received into Network(+2 day) > PDD',
            '10.Restricted Post Code- 24 HR Late Receipt' => 'Restricted Post Code - 24 HR Late Receipt - Stock Received into Network day(+1) is included in the 24HR service and manifest date(+1 day) > PDD',
            '11.Restricted Post Code- 48 HR Late Receipt' => 'Restricted Post Code - 48 HR Late Receipt - Stock Received into Network day(+2) is included in the 48HR service and manifest date(+2 day) > PDD',
            '1.Late Receipt of Stock' => 'Late Receipt of Stock - (Order data created date != manifest date)',
            '2.Late Receipt of Stock- 201 not equals createdon' => 'Late Receipt of Stock - (Stock Received into Network date != manifest date)',
            '3.Late Receipt of Stock- 201 not equals 150' => 'Late Receipt of Stock - (Stock Received into Network date != Order data created date)',
            '4.Late Receipt of Stock- 24 HR Late' => 'Late Receipt of Stock - 24 HR Late (Stock Received into Network date +1 > PDD date)',
            '5.Late Receipt of Stock- 48 HR Late' => 'Late Receipt of Stock - 48 HR Late (Stock Received into Network date +2 > PDD date)',
        ];

        $is48HoursService = $orderData['is_48hourse_service'] == 1 ? 'YES' : 'NO';
        $is24HoursService = $orderData['is_24hourse_service'] == 1 ? 'YES' : 'NO';
        $statusDate150 = Carbon::parse($orderData['status_date_150'])->format('Y-m-d');
        $statusDate201 = Carbon::parse($orderData['status_date_201'])->format('Y-m-d');
        $manifestDate = Carbon::parse($orderData['manifest_date'])->format('Y-m-d');
        $pdd = Carbon::parse($orderData['pdd'])->format('Y-m-d');
        $statusDate201Plus1 = Carbon::parse($orderData['status_date_201'])->addDay()->format('Y-m-d');
        $statusDate201Plus2 = Carbon::parse($orderData['status_date_201'])->addDays(2)->format('Y-m-d');
        $manifestDatePlus1 = Carbon::parse($orderData['manifest_date'])->addDay()->format('Y-m-d');
        $manifestDatePlus2 = Carbon::parse($orderData['manifest_date'])->addDays(2)->format('Y-m-d');

        $description = match ($titles[$urlString] ?? '') {
            'Restricted Post Code - 24 HR Fail - Transit Time - Stock Received into Network day(+1) is not included in the 24HR service' =>
            "{$statusDate201Plus1} ({$orderData['status_day_201_plus_one']}) is not included in the 24HR service ({$orderData['24hourse_service']})",
            'Restricted Post Code - 48 HR Fail - Transit Time - Stock Received into Network day(+2) is not included in the 48HR service' =>
            "{$statusDate201Plus2} ({$orderData['status_day_201_plus_two']}) is not included in the 48HR service ({$orderData['48hourse_service']})",
            'Restricted Post Code - 24 HR delivery - Hours difference between delivered date and Stock Received into Network date > 24Hrs' =>
            "{$statusDate201Plus2} ({$orderData['status_day_201_plus_two']}) is not included in the 48HR service ({$orderData['48hourse_service']})",
            'Restricted Post Code - 48 HR delivery - Hours difference between delivered date and Stock Received into Network > 48Hrs' =>
            "{$statusDate201Plus2} ({$orderData['status_day_201_plus_two']}) is not included in the 48HR service ({$orderData['48hourse_service']})",
            'Restricted Post Code - Days Of Coverage - 24 HR PDD - PDD day is not included in the 24HR service' =>
            "{$pdd} ({$orderData['pdd_weekday']}) is not included in the 24HR service ({$orderData['24hourse_service']})",
            'Restricted Post Code - Days Of Coverage - 48 HR PDD - PDD day is not included in the 48HR service' =>
            "{$pdd} ({$orderData['pdd_weekday']}) is not included in the 48HR service ({$orderData['48hourse_service']})",
            'Restricted Post Code - Days Of Coverage - 7 Day PDD - No 24 or 48HR service and day difference between Stock Received into Network day and pdd is < 7 days' =>
            "{$statusDate201} - {$pdd} < 7 Days",
            'Restricted Post Code - 24 HR Miscalculation - Stock Received into Network day(+1) is not included in the 24HR service and Stock Received into Network day(+1 day) > PDD' =>
            "{$statusDate201Plus1} ({$orderData['status_day_201_plus_one']}) is not included in the 24HR service ({$orderData['24hourse_service']}) AND {$statusDate201Plus1} > {$pdd}",
            'Restricted Post Code - 48 HR Miscalculation - Stock Received into Network day(+2) is included in the 48HR service and Stock Received into Network(+2 day) > PDD' =>
            "{$statusDate201Plus2} ({$orderData['status_day_201_plus_two']}) is not included in the 48HR service ({$orderData['48hourse_service']}) AND {$statusDate201Plus2} > {$pdd}",
            'Restricted Post Code - 24 HR Late Receipt - Stock Received into Network day(+1) is included in the 24HR service and manifest date(+1 day) > PDD' =>
            "{$statusDate201Plus1} ({$orderData['status_day_201_plus_one']}) is not included in the 24HR service ({$orderData['24hourse_service']}) AND {$manifestDatePlus1} > {$pdd}",
            'Restricted Post Code - 48 HR Late Receipt - Stock Received into Network day(+2) is included in the 48HR service and manifest date(+2 day) > PDD' =>
            "{$statusDate201Plus2} ({$orderData['status_day_201_plus_two']}) is not included in the 48HR service ({$orderData['48hourse_service']}) AND {$manifestDatePlus2} > {$pdd}",
            'Late Receipt of Stock - (Order data created date != manifest date)' =>
            "{$statusDate150} != {$manifestDate}",
            'Late Receipt of Stock - (Stock Received into Network date != manifest date)' =>
            "{$statusDate201} != {$manifestDate}",
            'Late Receipt of Stock - (Stock Received into Network date != Order data created date)' =>
            "{$statusDate201} != {$statusDate150}",
            'Late Receipt of Stock - 24 HR Late (Stock Received into Network date +1 > PDD date)' =>
            "{$statusDate201Plus1} > {$pdd}",
            'Late Receipt of Stock - 48 HR Late (Stock Received into Network date +2 > PDD date)' =>
            "{$statusDate201Plus2} > {$pdd}",
            default => '',
        };

        return [
            '48hoursService' => "{$orderData['48hourse_service']} ({$is48HoursService})",
            '24hoursService' => "{$orderData['24hourse_service']} ({$is24HoursService})",
            'manifestDate' => $orderData['manifest_date'],
            'deliveryPincode' => $orderData['delivery_pincode'],
            'statusDate150' => $statusDate150,
            'statusDate201' => $statusDate201,
            'manifestDate' => $manifestDate,
            'pdd' => $pdd,
            'statusDate201Plus1' => $statusDate201Plus1,
            'statusDate201Plus2' => $statusDate201Plus2,
            'title' => $titles[$urlString] ?? '',
            'description' => $description,
        ];
    }

    protected function getOrderViewStatus($tripId, $tripSts)
    {
        if ($tripId != 0 && $tripSts == 0) {
            return 'ACTIVE';
        }
        if ($tripId != 0 && $tripSts == 1) {
            return 'CLOSED';
        }
        return 'PENDING';
    }

    protected function formatDate($date, $timezone)
    {
        if (!$date || $date === '0000-00-00 00:00:00') {
            return '';
        }
        $created = Carbon::createFromTimestamp(strtotime($date), 'UTC');
        $check = Carbon::createFromTimestamp(strtotime('2020-07-01 00:00:00'), 'UTC');
        if ($created->gt($check)) {
            return Carbon::parse($date)->setTimezone($timezone)->format('Y-m-d H:i:s');
        }
        return $date;
    }

    public function getArrowXLOrderData($orderId)
    {
        $order = Order::where('id', $orderId)->first();
        if (!$order) {
            return [];
        }

        $status150 = OrderStatus::where('order_id', $orderId)
            ->where('status_code', '150')
            ->where('status', 1)
            ->select('status_date')
            ->first();

        $status201 = OrderStatus::where('order_id', $orderId)
            ->where('status_code', '201')
            ->where('status', 1)
            ->select('status_date')
            ->first();

        $service = $order->service ? ServiceMaster::where('id', $order->service)->first() : null;

        $statusDate201 = $status201 ? Carbon::parse($status201->status_date) : null;
        $manifestDate = $order->manifest_date ?? $order->created_at; // Fallback to createdon if manifest_date is unavailable
        $pdd = $order->pdd ?? $order->delivery_datetime; // Fallback to delivery_datetime if pdd is unavailable

        return [
            'is_48hourse_service' => $service && strpos($service->name, '48HR') !== false ? 1 : 0,
            'is_24hourse_service' => $service && strpos($service->name, '24HR') !== false ? 1 : 0,
            'status_date_150' => $status150 ? $status150->status_date : null,
            'status_date_201' => $status201 ? $status201->status_date : null,
            'manifest_date' => $manifestDate,
            'pdd' => $pdd,
            'status_day_201_plus_one' => $statusDate201 ? $statusDate201->addDay()->format('l') : '',
            'status_day_201_plus_two' => $statusDate201 ? $statusDate201->addDays(2)->format('l') : '',
            '24hourse_service' => $service && strpos($service->name, '24HR') !== false ? '24HR' : '',
            '48hourse_service' => $service && strpos($service->name, '48HR') !== false ? '48HR' : '',
            'pdd_weekday' => $pdd ? Carbon::parse($pdd)->format('l') : '',
            'delivery_pincode' => $order->delivery_pincode ?? '',
        ];
    }

    public function copyOrder($id, $orgId, $userId, $currency)
    {
        $data = [
            'currencies' => [$currency],
            'transport' => [],
            'orderDetails' => ['typeName' => '', 'ordtypeCode' => '', 'cargoId' => '', 'partyId' => ''],
            'pickupDetails' => [],
            'dropDetails' => [],
            'shipperDetails' => [],
            'orderTypes' => [],
            'costCenter' => [],
            'deliveryArray' => [],
            'chargecodes' => [],
            'roles' => [],
        ];

        $currencies = CountryMaster::where('status', '1')->pluck('currency')->toArray();
        if (!empty($currencies)) {
            $data['currencies'] = array_merge($data['currencies'], $currencies);
        }

        $order = $this->getOrderToEdit($id);
        if (!$order) {
            return $data;
        }

        $data['orderDetails'] = $this->getOrderDetails($order, $orgId);
        $data['pickupDetails'] = $this->getPickupDetails($order->customer_id);
        $data['dropDetails'] = $this->getDropDetails($order);
        $data['shipperDetails'] = $this->getShipperDetails($order);
        $data['orderTypes'] = $this->getOrderTypes($order->customer_id, $orgId, $order->be_value, $order->createdon);
        $data['costCenter'] = $orgId === 'NZPG' ? $this->getCostCenter($orgId) : [];
        $data['deliveryArray'] = $order->incoterm ? $this->getDeliveryTermsByIncoterm($order->incoterm) : [];
        $data['chargecodes'] = $this->getChargeCodes();
        $data['roles'] = $this->getRoles($userId);
        $transportMode = new TransportMode();
        $data['transport'] = $transportMode->getTransportMode($order->createdon ?? '', '', $orgId, $order->be_value);
        $data['orderDetails']['cargoId'] = $this->getCargoDetails($id, $userId);

        return $data;
    }

    protected function getOrderTypes($customerId, $orgId, $beValue, $createdon)
    {
        $orderTypes = OrderType::where('customer_id', $customerId)
            ->where('org_id', $orgId)
            ->where('status', '1')
            ->select('id', 'type_name')
            ->groupBy('type_name')
            ->get()
            ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
            ->toArray();

        if (!empty($orderTypes)) {
            return $orderTypes;
        }

        $orderDate = Carbon::parse($createdon);
        $checkDate = Carbon::parse('2021-03-19 00:00:00');

        if ($orderDate->lt($checkDate)) {
            $orderTypes = OrderType::where('org_id', $orgId)
                ->where('status', '1')
                ->select('id', 'type_name')
                ->groupBy('type_name')
                ->get()
                ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                ->toArray();

            if (!empty($orderTypes)) {
                return $orderTypes;
            }

            return OrderType::where('org_id', 'SGKN')
                ->where('status', '1')
                ->select('id', 'type_name')
                ->groupBy('type_name')
                ->get()
                ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                ->toArray();
        }

        $hasBranch = OrderType::where('status', '1')
            ->where('org_id', $orgId)
            ->where('be_value', $beValue)
            ->exists();

        if ($hasBranch) {
            return OrderType::where('status', '1')
                ->where(function ($query) use ($orgId, $beValue) {
                    $query->where('org_id', $orgId)
                        ->where(function ($q) use ($beValue) {
                            $q->where('be_value', $beValue)
                                ->orWhereNull('be_value')
                                ->orWhere('be_value', '');
                        })
                        ->orWhereNull('org_id')
                        ->orWhere('org_id', '')
                        ->where(function ($q) {
                            $q->whereNull('be_value')
                                ->orWhere('be_value', '');
                        });
                })
                ->select('id', 'type_name')
                ->groupBy('type_name')
                ->get()
                ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                ->toArray();
        }

        return OrderType::where('status', '1')
            ->where(function ($query) use ($orgId) {
                $query->where('org_id', $orgId)
                    ->orWhereNull('org_id')
                    ->orWhere('org_id', '')
                    ->where(function ($q) {
                        $q->whereNull('be_value')
                            ->orWhere('be_value', '');
                    });
            })
            ->select('id', 'type_name')
            ->groupBy('type_name')
            ->get()
            ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
            ->toArray();
    }

    protected function getCostCenter($orgId)
    {
        return CostCenter::where('org_id', $orgId)
            ->where('status', '1')
            ->select('id', 'type_name')
            ->groupBy('type_name')
            ->get()
            ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
            ->toArray();
    }

    protected function getChargeCodes()
    {
        return ChargeCode::where('status', '1')
            ->select('id', 'charge_code')
            ->get()
            ->map(fn($res) => ['chargeId' => $res->id, 'chargeCode' => $res->charge_code])
            ->toArray();
    }

    protected function getRoles($userId)
    {
        return SxPartyTypes::where('user_id', $userId)
            ->where('status', 1)
            ->select('id', 'type_name as name')
            ->groupBy('type_name')
            ->get()
            ->map(fn($res) => ['id' => $res->id, 'name' => $res->name])
            ->toArray();
    }

    protected function getCargoDetails($orderId, $userId)
    {
        $cargoDetails = CargoDetail::select('cargo_details.*')
            ->join('order_cargodetails', fn($join) => $join->on('cargo_details.id', '=', 'order_cargodetails.cargo_id')->where('order_cargodetails.status', 1))
            ->where('order_cargodetails.order_id', $orderId)
            ->groupBy('cargo_details.id')
            ->orderByDesc('cargo_details.id')
            ->get();

        $cargoRowIds = [];
        foreach ($cargoDetails as $cargo) {
            $newCargo = CargoDetail::create([
                'order_id' => $orderId,
                'be_value' => Auth::user()->be_value ?? 0,
                'user_id' => $userId,
                'cargo_type' => $cargo->cargo_type,
                'goods_description' => $cargo->goods_description,
                'quantity' => $cargo->quantity,
                'length' => $cargo->length,
                'length_unit' => $cargo->length_unit,
                'width' => $cargo->width,
                'width_unit' => $cargo->width_unit,
                'height' => $cargo->height,
                'height_unit' => $cargo->height_unit,
                'weight' => $cargo->weight,
                'weight_unit' => $cargo->weight_unit,
                'volume' => $cargo->volume,
                'volume_unit' => $cargo->volume_unit,
                'stackable' => $cargo->stackable,
                'grounded' => $cargo->grounded,
                'splittable' => $cargo->splittable,
                'createdby' => $userId,
                'volumetric_weight' => $cargo->volumetric_weight,
                'volweight_uom' => $cargo->volweight_uom,
                'createdon' => now(),
                'ldm' => $cargo->ldm,
                'second_weight' => $cargo->second_weight,
                'secondweight_uom' => $cargo->secondweight_uom,
                'second_volume' => $cargo->second_volume,
                'secondvolume_uom' => $cargo->secondvolume_uom,
                'marks_numbers' => $cargo->marks_numbers,
            ]);
            $cargoRowIds[] = $newCargo->id;
        }

        return implode(',', $cargoRowIds);
    }

    public function cancelOrder(int $id, int $orgId, int $userId, string $timezone, string $countryHours): void
    {
        if ($id <= 0) {
            throw new \Exception('Invalid order ID');
        }

        $updated = Order::where('id', $id)->update(['status' => 3]);

        if ($updated) {
            $orderDetails = $this->getCustomerDetailsWithOrder([$id]);
            $singleOrderDetail = $orderDetails[0] ?? [];

            if (($singleOrderDetail['sendtoKnlogin'] ?? 0) > 0 && ($singleOrderDetail['isCreated'] ?? 0) > 1) {
                $this->sendStatusToKNLoginWhileDeletingOrder($singleOrderDetail, [
                    'timeZone' => $timezone,
                    'currentDate' => now()->format('Y-m-d H:i:s'),
                    'sessionOrgId' => $orgId,
                    'sessionBeValue' => Auth::user()->be_value ?? 0,
                    'countryHours' => $countryHours,
                ]);
            }
        } else {
            throw new \Exception('Failed to cancel order');
        }
    }

    public function getCustomerDetailsWithOrder(array $orderIds): array
    {
        return Order::select([
            'orders.id',
            'orders.order_id',
            'orders.pickup_city',
            'orders.modeoftransport',
            'orders.is_created',
            'orders.shift_id',
            'orders.trip_id',
            'orders.shipmentid',
            'customers.sendto_knlogin',
        ])
            ->join('customers', 'customers.id', '=', 'orders.customer_id')
            ->whereIn('orders.id', $orderIds)
            ->groupBy('orders.id')
            ->get()
            ->map(fn($row) => [
                'id' => $row->id,
                'orderId' => $row->order_id,
                'pickupCity' => $row->pickup_city,
                'modeoftransport' => $row->modeoftransport,
                'isCreated' => $row->is_created,
                'shiftId' => $row->shift_id,
                'tripId' => $row->trip_id,
                'shipmentid' => $row->shipmentid,
                'sendtoKnlogin' => $row->sendto_knlogin,
            ])
            ->toArray();
    }

    public function sendStatusToKNLoginWhileDeletingOrder(array $ordersInfo, array $additionalInfo): void
    {
        $timeZone = $additionalInfo['timeZone'];
        $countryHours = str_replace('.', ':', $additionalInfo['countryHours']);
        $currentDateTime = $additionalInfo['currentDate'];
        $currentDate = Carbon::parse($currentDateTime)->format('Y-m-d');
        $currentTime = Carbon::parse($currentDateTime)->format('H:i:s') . $countryHours;
        $userDateTime = Carbon::parse($currentDateTime)->format('Y-m-d\TH:i:s');
        $userDateWithHours = $userDateTime . $countryHours;

        $logicalReceiver = 'WWWWW99';
        $physicalReceiver = 'WWDPT01';
        $physicalSender = 'KNRLG09';
        // if (config('app.sys_type') === 'TMS') {
        //     $physicalReceiver = 'WWDPP01';
        //     $physicalSender = 'KNRLG01';
        // }
        $logicalSender = 'THPNA74';

        $postData = [
            'shipment_id' => $ordersInfo['shipmentid'] ?? '',
            'status_code' => '9800',
            'org_id' => $additionalInfo['sessionOrgId'],
            've_value' => $additionalInfo['sessionBeValue'],
            'pickup_city' => $ordersInfo['pickupCity'] ?? '',
            'logicalreceiver' => $logicalReceiver,
            'physicalreceiver' => $physicalReceiver,
            'physicalsender' => $physicalSender,
            'logicalsender' => $logicalSender,
            'modeoftransport' => $ordersInfo['modeoftransport'] ?? '',
            'trackingnumber' => $ordersInfo['orderId'] ?? '',
            'ref_id' => 'INN',
            'ref_num' => 'RL' . ($ordersInfo['orderId'] ?? ''),
            'createdon' => $currentDateTime,
            'date' => $currentDate,
            'time' => $currentTime,
            'curdt' => $userDateWithHours,
            'curdt1' => $userDateTime
        ];

        $this->knLoginSendOrderStatus($postData);
    }

    public function knLoginSendOrderStatus(array $post): bool
    {
        $deliveryStatus = ['0192', '2300', '3000'];
        $otherStatus = ['1500', '1600'];

        $request = '<?xml version="1.0" encoding="UTF-8"?>';
        $request .= '<ns9:RoadStatus xmlns:ns6="http://knie4.int.kn/com/lref/0100" xmlns:ns5="http://knie4.int.kn/com/lsts/0200" xmlns:ns8="http://knie4.int.kn/com/lpaci/0200" xmlns:ns7="http://knie4.int.kn/com/lrtv/0100" xmlns:ns9="http://knie4.int.kn/msg/roadstatus/0100" xmlns:ns2="http://knie4.int.kn/com/ltypes/0200" xmlns:ns4="http://knie4.int.kn/com/lmlv/0200" xmlns:ns3="http://knie4.int.kn/com/lhdr/0200">';
        $request .= '<ns3:MessageHeader>';
        $request .= '<ReceiverDetails ns2:AddressType="FW">';
        $request .= '<PhysicalReceiver>' . htmlspecialchars($post['physicalreceiver']) . '</PhysicalReceiver>';
        $request .= '<LogicalReceiver>' . htmlspecialchars($post['logicalreceiver']) . '</LogicalReceiver>';
        $request .= '</ReceiverDetails>';
        $request .= '<PhysicalSender>' . htmlspecialchars($post['physicalsender']) . '</PhysicalSender>';
        $request .= '<LogicalSender>' . htmlspecialchars($post['logicalsender']) . '</LogicalSender>';
        $request .= '<SendingApplication>ROADLOG</SendingApplication>';
        $request .= '<MessageType>RoadStatus</MessageType>';
        $request .= '<VersionNumber>0100</VersionNumber>';
        $request .= '<CreationDateTime>' . htmlspecialchars($post['curdt1']) . 'Z</CreationDateTime>';
        $request .= '<FunctionCode>CR</FunctionCode>';
        $request .= '</ns3:MessageHeader>';
        $request .= '<Message>';
        $request .= '<ns4:MessageLevel>';
        $request .= '<TrackingNumber>' . htmlspecialchars($post['trackingnumber']) . '</TrackingNumber>';
        $request .= '<SenderInformation>';
        $request .= '<CompanyCode>' . htmlspecialchars($post['org_id']) . '</CompanyCode>';
        $request .= '<BranchCode>' . htmlspecialchars($post['be_value']) . '</BranchCode>';
        $request .= '<DepartmentCode>' . htmlspecialchars($post['be_value']) . '</DepartmentCode>';
        $request .= '<ModeOfTransport>1</ModeOfTransport>';
        $request .= '</SenderInformation>';
        $request .= '<MessageReferences ns2:AddressType="FF">';
        $request .= '<Code>' . htmlspecialchars($post['ref_id']) . '</Code>';
        $request .= '<Value>' . htmlspecialchars($post['ref_num']) . '</Value>';
        $request .= '</MessageReferences>';
        $request .= '</ns4:MessageLevel>';
        $request .= '<ns5:StatusInformation ns2:StatusCode="' . htmlspecialchars($post['status_code']) . '">';
        $request .= '<StatusDate>' . htmlspecialchars($post['date']) . '</StatusDate>';
        $request .= '<StatusTime>' . htmlspecialchars($post['time']) . '</StatusTime>';
        $request .= '<StatusLocation ns2:LocationIssuer="ZZZ">';
        $request .= in_array($post['status_code'], $otherStatus, true) ? htmlspecialchars($post['pickup_city']) : (in_array($post['status_code'], $deliveryStatus, true) ? htmlspecialchars($post['pickup_city']) : htmlspecialchars($post['pickup_city']));
        $request .= '</StatusLocation>';
        $request .= '<StatusLocation ns2:LocationIssuer="6">' . htmlspecialchars($post['pickup_city']) . '</StatusLocation>';
        $request .= '<AdditionalStatusInformation>';
        $request .= '<CreationIdentifier>R</CreationIdentifier>';
        $request .= '<StatusEntryDateTime>' . htmlspecialchars($post['curdt']) . '</StatusEntryDateTime>';
        $request .= '<UserIdentifier>root</UserIdentifier>';
        $request .= '</AdditionalStatusInformation>';
        $request .= '<AdditionalRoadStatusInformation/>';
        $request .= '</ns5:StatusInformation>';
        $request .= '<Shipment/>';
        $request .= '</Message>';
        $request .= '</ns9:RoadStatus>';

        $response = Http::withHeaders([
            'Content-Type' => 'application/xml',
            'Authorization' => 'Basic ' . base64_encode(env('KN_LOGIN_API_USERNAME') . ':' . env('KN_LOGIN_API_PASSWORD')),
        ])->post(env('KN_LOGIN_API_URL'), $request);

        if ($response->status() !== 200) {
            Log::error('KNLogin API Error: Status ' . $response->status() . ' - ' . $response->body());
            throw new \Exception('Failed to send status to KNLogin');
        }

        return true;
    }

    public function checkTrip($orderId, int $userId, int $orgId, int $beValue): array
    {
        $templates = $this->getTripTemplatesByUser($orgId);
        $allocationRules = $this->getAllocationRules($orgId, $beValue);
        $result = [
            'result' => 0,
            'carrierId' => 0,
            'code' => '',
            'templates' => $templates,
            'allocationRules' => $allocationRules,
            'ordersVolume' => 0,
            'numberOfVehicles' => 0,
            'status' => 0,
        ];

        if (!is_array($orderId)) {
            $order = $this->getTableRowData(['id' => $orderId], 'vendor_id,shift_id,status', 'Order');
            if (!empty($order)) {
                $result['result'] = $order['shift_id'] > 0 ? 1 : 0;
                $result['carrierId'] = $order['vendor_id'];
                $result['status'] = $order['status'];

                if ($order['vendor_id'] > 0) {
                    $vendor = $this->getTableRowData(['id' => $order['vendor_id']], 'code', 'SxPartyMembers');
                    $result['code'] = $vendor['code'] ?? '';
                }
            }
        } else {
            $orders = Order::select(['id', 'vendor_id', 'status'])
                ->whereIn('id', $orderId)
                ->where('shift_id', '!=', 0)
                ->first();

            if ($orders) {
                $result['result'] = 1;
                $result['carrierId'] = $orders->vendor_id;
                $result['status'] = $orders->status;

                if ($orders->vendor_id > 0) {
                    $vendor = $this->getTableRowData(['id' => $orders->vendor_id], 'code', 'SxPartyMembers');
                    $result['code'] = $vendor['code'] ?? '';
                }
            } else {
                $vroId = VroOrder::where('status', 1)
                    ->whereIn('order_ids', array_map(fn($id) => "%$id%", $orderId))
                    ->value('id');

                if ($vroId) {
                    $result['result'] = 4;
                }
            }

            $ordersVolume = Order::whereIn('id', $orderId)->sum('volume');
            $vehiclesVolumes = TrucksData::where('user_id', $userId)
                ->where('status', 1)
                ->orderByDesc('truck_volume')
                ->pluck('truck_volume')
                ->toArray();

            $selectedVehicles = [];
            $currentVolume = 0;
            foreach ($vehiclesVolumes as $volume) {
                $selectedVehicles[] = $volume;
                $currentVolume += $volume;
                if ($currentVolume >= $ordersVolume) {
                    break;
                }
            }

            $result['ordersVolume'] = $ordersVolume;
            $result['numberOfVehicles'] = count($selectedVehicles);
        }

        return $result;
    }

    public function getTripTemplatesByUser(int $orgId): array
    {
        return RouteTemplate::where('org_id', $orgId)
            ->where('active', 1)
            ->where('status', 1)
            ->select(['id', 'template_id as templateId', 'template_name as templateName'])
            ->orderByDesc('id')
            ->get()
            ->map(fn($row) => [
                'id' => $row->id,
                'templateId' => $row->templateId,
                'templateName' => $row->templateName,
            ])
            ->toArray();
    }

    public function getAllocationRules(int $orgId, int $beValue): array
    {
        return RouteTemplate::where('org_id', $orgId)
            ->where('be_value', $beValue)
            ->select(['id', 'template_name as ruleName'])
            ->get()
            ->map(fn($row) => [
                'id' => $row->id,
                'ruleName' => $row->ruleName,
            ])
            ->toArray();
    }

    public function getVehicleDetailsByCarrierInfo(int $carrierId, int $vehicleType): array
    {
        $data = [
            'vehicles' => [],
            'carrierName' => '',
            'carrierCode' => '',
            'vehicleTypeName' => '',
        ];

        if ($vehicleType > 0) {
            $truckType = $this->getTableRowData(['id' => $vehicleType], 'trucktype', 'TruckType');
            $data['vehicleTypeName'] = $truckType['trucktype'] ?? '';
        }

        if ($carrierId > 0) {
            $carrier = $this->getTableRowData(['id' => $carrierId, 'status' => '1'], 'name,code', 'sxPartyMembers');
            $data['carrierName'] = $carrier['name'] ?? '';
            $data['carrierCode'] = $carrier['code'] ?? '';
            $data['vehicles'] = $this->getVehicleByCarrierInfo($carrierId, $vehicleType);
        }

        return $data;
    }

    public function getVehicleByCarrierInfo(int $carrierId, int $vehicleType): array
    {
        $query = TrucksData::where('status', '1')
            ->where('vendor_id', $carrierId)
            ->select(['id', 'register_number as registerNumber'])
            ->orderBy('register_number', 'ASC');

        if ($vehicleType > 0) {
            $query->where('truck_type', $vehicleType);
        }

        return $query->get()
            ->map(fn($row) => [
                'id' => $row->id,
                'registerNumber' => $row->registerNumber,
            ])
            ->toArray();
    }

    public function getDriverId(int $id): string
    {
        if ($id <= 0) {
            return '';
        }

        $driver = $this->getTableRowData(['id' => $id], 'contact_num', 'TruckDriver');
        return $driver['contact_num'] ?? '';
    }

    public function addReferenceDetails(
        string $referenceId,
        string $referenceName,
        string $referenceValue,
        string $refRowId,
        string $orderId,
        int $orgId,
        int $beValue,
        string $timezone
    ): array {
        $createdOn = now()->format('Y-m-d H:i:s');
        $insId = 0;
        $orderInsId = 0;

        // Handle ETA timezone conversion
        $value = $referenceValue;
        if ($referenceId === 'ETA') {
            try {
                $value = Carbon::parse($referenceValue, $timezone)->format('Y-m-d H:i:s');
            } catch (\Exception $e) {
                Log::error('ETA Timezone Conversion Error: ' . $e->getMessage());
                $value = $referenceValue; // Fallback to original value
            }
        }

        if (empty($refRowId)) {
            // Insert into reference_master if not exists
            $chkAr = $this->getTableRowData(['name' => $referenceId, 'status' => '1'], 'id', 'referenceMaster');
            if (empty($chkAr)) {
                $insArr = [
                    'name' => $referenceId,
                    'description' => $referenceName,
                    'createdon' => $createdOn,
                    'user_id' => Auth::user()->id ?? 0,
                    'org_id' => $orgId,
                    'be_value' => $beValue,
                ];
                $reference = ReferenceMaster::create($insArr);
                $insId = $reference->id;
            } else {
                $insId = $chkAr['id'];
            }

            if ($insId) {
                // Insert into order_references
                $insOrder = [
                    'reference_id' => $referenceId,
                    'ref_value' => $value,
                    'createdon' => $createdOn,
                    'order_id' => $orderId,
                    'user_id' => Auth::user()->id ?? 0,
                    'org_id' => $orgId,
                    'be_value' => $beValue,
                ];
                $orderReference = OrderReference::create($insOrder);
                $orderInsId = $orderReference->id;

                // Insert into xborder_notifications for XBRDR
                $this->xbdrNotificationInsert($referenceId, $orgId, $value, $orderId, $beValue);
            }
        } else {
            // Update reference_master
            $updAr = [
                'name' => $referenceId,
                'description' => $referenceName,
                'user_id' => Auth::user()->id ?? 0,
                'org_id' => $orgId,
                'be_value' => $beValue,
            ];
            ReferenceMaster::where('id', $refRowId)->update($updAr);

            // Update purchase_order in order_details for PO
            if ($referenceId === 'PO') {
                OrderDetail::where('order_row_id', $orderId)->update(['purchase_order' => $value]);
            }

            // Handle order_references
            if ($orderId !== '0') {
                $insAr = [
                    'order_id' => $orderId,
                    'reference_id' => $referenceId,
                    'ref_value' => $value,
                    'createdon' => $createdOn,
                    'user_id' => Auth::user()->id ?? 0,
                    'org_id' => $orgId,
                    'be_value' => $beValue,
                ];
                $chk = $this->getTableRowData(['order_id' => $orderId, 'reference_id' => $referenceId, 'status' => '1'], 'id', 'orderReference');
                if (empty($chk)) {
                    $orderReference = OrderReference::create($insAr);
                    $orderInsId = $orderReference->id;
                } else {
                    OrderReference::where('id', $chk['id'])->update($insAr);
                    $orderInsId = $chk['id'];
                }
            } else {
                $insAr = [
                    'order_id' => '0',
                    'reference_id' => $referenceId,
                    'ref_value' => $value,
                    'createdon' => $createdOn,
                    'user_id' => Auth::user()->id ?? 0,
                    'org_id' => $orgId,
                    'be_value' => $beValue,
                ];
                $chk = $this->getTableRowData(['reference_id' => $referenceId, 'status' => '1'], 'id', 'orderReference');
                if (empty($chk)) {
                    $orderReference = OrderReference::create($insAr);
                    $orderInsId = $orderReference->id;
                } else {
                    $orderInsId = $chk['id'];
                    OrderReference::where('id', $orderInsId)->update($insAr);
                }
            }

            // Insert into xborder_notifications for XBRDR
            $this->xbdrNotificationInsert($referenceId, $orgId, $value, $orderId, $beValue);

            $insId = $refRowId;
        }

        return [
            'insId' => $insId,
            'orderInsId' => $orderInsId,
        ];
    }

    public function xbdrNotificationInsert(string $referenceId, int $orgId, string $value, string $orderId, int $beValue): bool
    {
        if ($referenceId !== 'XBRDR' || $orgId == $value) {
            return false;
        }

        $data = [
            'order_id' => $orderId,
            'company_code' => $orgId,
            'branch_code' => $beValue,
            'xborder_reference' => $value,
            'status' => '1',
            'user_id' => Auth::user()->id ?? 0,
            'org_id' => $orgId,
            'be_value' => $beValue,
        ];

        $check = $this->getTableRowData(
            ['order_id' => $orderId, 'xborder_reference' => $value, 'status' => '1'],
            'id',
            'xborderNotification'
        );

        if (empty($check)) {
            XborderNotification::create($data);
            return true;
        }

        return false;
    }

    public function deleteOrderReferenceDetails(string $orderId, string $refId): bool
    {
        if ($orderId === '0' || $refId === '0') {
            return false;
        }

        // Check if order reference exists
        $chkDetails = $this->getTableRowData(
            ['order_id' => $orderId, 'reference_id' => $refId, 'status' => '1'],
            'id',
            'orderReference'
        );

        if (!empty($chkDetails)) {
            $updated = OrderReference::where('id', $chkDetails['id'])->update(['status' => '0']);
            return $updated > 0;
        }

        return false;
    }

    public function processOrderCostExcel(UploadedFile $file, int $userId, int $orgId, int $beValue, string $currency): array
    {
        ini_set('max_execution_time', 300);
        $createdOn = now()->format('Y-m-d H:i:s');
        $allOrders = [];
        $failureArr = [];
        $revIds = [];

        $data = Excel::toArray([], $file)[0]; // Get first sheet
        $mainHeader = $data[1] ?? []; // Row 2 (header)
        $arrData = array_slice($data, 2); // Data rows (3+)

        $arr = [];
        $sno = '';
        $i = 0;

        foreach ($arrData as $rowIndex => $row) {
            $shipmentId = $row[0] ?? ''; // Column A
            $shipmentId = $shipmentId . $i;
            $row['row'] = $rowIndex + 3; // Adjust for 1-based row index
            if ($sno !== $shipmentId && $shipmentId !== '') {
                $sno = $shipmentId;
                $arr[$sno][] = $row;
            } else {
                $arr[$sno][] = $row;
            }
            $i++;
        }

        $arrData = array_values($arr);

        foreach ($arrData as $group) {
            $revCustIds = $venCustIds = $revInternalBus = $costInternalBus = [];
            $rowNo = $group[0]['row'] ?? '';
            $orderId = $group[0][0] ?? ''; // Column A

            if ($orderId !== '') {
                $orderRowId = Order::where('order_id', $orderId)->value('id') ?? 0;
                $allOrders[] = ['id' => $orderRowId, 'booking_id' => $orderId];

                $customer = $group[0][1] ?? ''; // Column B
                $vendor = $group[0][2] ?? ''; // Column C
                $revenueIbu = $group[0][3] ?? ''; // Column D
                $revBuJfr = $group[0][4] ?? ''; // Column E
                $costIbu = $group[0][5] ?? ''; // Column F
                $costBuJfr = $group[0][6] ?? ''; // Column G

                $x = 7; // Column H (index-based)
                for ($j = 0; $j <= 15; $j++) {
                    $charge = $mainHeader[$x] ?? '';
                    $x += 4; // Skip to next charge code
                    if ($charge !== '') {
                        $y = $x - 4; // Start of charge data
                        $sell = $group[0][$y] ?? '';
                        $sellCurrency = $group[0][$y + 1] ?? '';
                        $buy = $group[0][$y + 2] ?? '';
                        $buyCurrency = $group[0][$y + 3] ?? '';

                        if ($customer !== '') {
                            // Revenue (Customer)
                            if ($sell !== '') {
                                $sellAmount = (float) ltrim($sell, ' $ ');
                                $sellChk = $this->checkDecimalAmountForVn($sellAmount, $orgId, $currency, $sellCurrency);
                                if ($sellChk === 0) {
                                    $failureArr[] = [
                                        'decimal' => '1',
                                        'type' => '0',
                                        'order_id' => $orderRowId,
                                        'status' => "Revenue Failed for this order because of decimal amount -'$sellAmount $sellCurrency'",
                                        'amount' => $sellAmount,
                                        'bu_jfr' => '',
                                    ];
                                } else {
                                    $chargeData = $this->getTableRowData(['charge_code' => $charge, 'status' => '1'], 'id,description', 'chargeCode');
                                    if (!empty($chargeData)) {
                                        $chargeId = $chargeData['id'];
                                        $chargeDesc = $chargeData['description'];
                                        $localAmount = $currency === $sellCurrency ? $sellAmount : 0;
                                        $localCurrency = $currency === $sellCurrency ? $sellCurrency : $currency;

                                        $chargeAr = [
                                            'revenue_id' => 0,
                                            'charge_code' => $chargeId,
                                            'description' => $chargeDesc,
                                            'quantity_unit' => '1',
                                            'value' => '1',
                                            'rate_id' => '1',
                                            'amount' => $sellAmount,
                                            'currency' => $sellCurrency,
                                            'createdon' => $createdOn,
                                            'local_amount' => $localAmount,
                                            'local_currency' => $localCurrency,
                                            'source_created' => 'CU - Excel Upload',
                                            'user_id' => $userId,
                                            'org_id' => $orgId,
                                            'be_value' => $beValue,
                                        ];
                                        $charge = Charge::create($chargeAr);
                                        $revCustIds[] = $charge->id;
                                    }
                                }
                            }
                        } elseif ($revenueIbu !== '') {
                            // Revenue (Internal BU)
                            if ($sell !== '') {
                                $sellAmount = (float) ltrim($sell, ' $ ');
                                $sellIbuChk = $this->checkDecimalAmountForVn($sellAmount, $orgId, $currency, $sellCurrency);
                                if ($sellIbuChk === 0) {
                                    $failureArr[] = [
                                        'decimal' => '1',
                                        'type' => '0',
                                        'order_id' => $orderRowId,
                                        'status' => "Internal BU(REV) Failed for this order because of decimal amount -'$sellAmount $sellCurrency'",
                                        'amount' => $sellAmount,
                                        'bu_jfr' => $revBuJfr,
                                    ];
                                } else {
                                    $chargeData = $this->getTableRowData(['charge_code' => $charge, 'status' => '1'], 'id,description', 'chargeCode');
                                    if (!empty($chargeData)) {
                                        $chargeId = $chargeData['id'];
                                        $chargeDesc = $chargeData['description'];
                                        $localAmount = $currency === $sellCurrency ? $sellAmount : 0;
                                        $localCurrency = $currency === $sellCurrency ? $sellCurrency : $currency;

                                        $chargeAr = [
                                            'revenue_id' => 0,
                                            'charge_code' => $chargeId,
                                            'description' => $chargeDesc,
                                            'quantity_unit' => '1',
                                            'value' => '1',
                                            'rate_id' => '1',
                                            'amount' => $sellAmount,
                                            'currency' => $sellCurrency,
                                            'createdon' => $createdOn,
                                            'local_amount' => $localAmount,
                                            'local_currency' => $localCurrency,
                                            'source_created' => 'CU - Excel Upload',
                                            'user_id' => $userId,
                                            'org_id' => $orgId,
                                            'be_value' => $beValue,
                                        ];
                                        $charge = Charge::create($chargeAr);
                                        $revInternalBus[] = $charge->id;
                                    }
                                }
                            }
                        }

                        if ($vendor !== '') {
                            // Cost (Vendor)
                            if ($buy !== '') {
                                $buyAmount = (float) ltrim($buy, ' $ ');
                                $buyChk = $this->checkDecimalAmountForVn($buyAmount, $orgId, $currency, $buyCurrency);
                                if ($buyChk === 0) {
                                    $failureArr[] = [
                                        'decimal' => '1',
                                        'type' => '1',
                                        'order_id' => $orderRowId,
                                        'status' => "Cost Failed for this order because of decimal amount -'$buyAmount $buyCurrency'",
                                        'amount' => $buyAmount,
                                        'bu_jfr' => '',
                                    ];
                                } else {
                                    $chargeData = $this->getTableRowData(['charge_code' => $charge, 'status' => '1'], 'id,description', 'chargeCode');
                                    if (!empty($chargeData)) {
                                        $chargeId = $chargeData['id'];
                                        $chargeDesc = $chargeData['description'];
                                        $localAmount = $currency === $buyCurrency ? $buyAmount : 0;
                                        $localCurrency = $currency === $buyCurrency ? $buyCurrency : $currency;

                                        $chargeAr = [
                                            'revenue_id' => 0,
                                            'charge_code' => $chargeId,
                                            'description' => $chargeDesc,
                                            'quantity_unit' => '1',
                                            'value' => '1',
                                            'rate_id' => '1',
                                            'amount' => $buyAmount,
                                            'currency' => $buyCurrency,
                                            'createdon' => $createdOn,
                                            'local_amount' => $localAmount,
                                            'local_currency' => $localCurrency,
                                            'source_created' => 'CU - Excel Upload',
                                            'user_id' => $userId,
                                            'org_id' => $orgId,
                                            'be_value' => $beValue,
                                        ];
                                        $charge = Charge::create($chargeAr);
                                        $venCustIds[] = $charge->id;
                                    }
                                }
                            }
                        } elseif ($costIbu !== '') {
                            // Cost (Internal BU)
                            if ($buy !== '') {
                                $buyAmount = (float) ltrim($buy, ' $ ');
                                $buyIbuChk = $this->checkDecimalAmountForVn($buyAmount, $orgId, $currency, $buyCurrency);
                                if ($buyIbuChk === 0) {
                                    $failureArr[] = [
                                        'decimal' => '1',
                                        'type' => '1',
                                        'order_id' => $orderRowId,
                                        'status' => "Internal BU(Cost) Failed for this order because of decimal amount -'$buyAmount $buyCurrency'",
                                        'amount' => $buyAmount,
                                        'bu_jfr' => $costBuJfr,
                                    ];
                                } else {
                                    $chargeData = $this->getTableRowData(['charge_code' => $charge, 'status' => '1'], 'id,description', 'chargeCode');
                                    if (!empty($chargeData)) {
                                        $chargeId = $chargeData['id'];
                                        $chargeDesc = $chargeData['description'];
                                        $localAmount = $currency === $buyCurrency ? $buyAmount : 0;
                                        $localCurrency = $currency === $buyCurrency ? $buyCurrency : $currency;

                                        $chargeAr = [
                                            'revenue_id' => 0,
                                            'charge_code' => $chargeId,
                                            'description' => $chargeDesc,
                                            'quantity_unit' => '1',
                                            'value' => '1',
                                            'rate_id' => '1',
                                            'amount' => $buyAmount,
                                            'currency' => $buyCurrency,
                                            'createdon' => $createdOn,
                                            'local_amount' => $localAmount,
                                            'local_currency' => $localCurrency,
                                            'source_created' => 'CU - Excel Upload',
                                            'user_id' => $userId,
                                            'org_id' => $orgId,
                                            'be_value' => $beValue,
                                        ];
                                        $charge = Charge::create($chargeAr);
                                        $costInternalBus[] = $charge->id;
                                    }
                                }
                            }
                        }
                    }
                }

                // Process Revenues
                if (!empty($revCustIds)) {
                    $customerData = $this->getPartyNameAndCode($customer, $orgId, $userId, 'Customer');
                    if (!empty($customerData)) {
                        $total = Charge::whereIn('id', $revCustIds)->selectRaw('SUM(amount) as amount, currency')->first();
                        $totalAmount = $total->amount ?? 0;
                        $chargeCurrency = $total->currency ?? '';
                        $foreignCurrency = $currency === $chargeCurrency ? '' : $chargeCurrency;

                        $revAr = [
                            'type' => '0',
                            'order_id' => $orderRowId,
                            'recipient_role' => 'Customer',
                            'recipient_code' => $customerData['party_code'],
                            'recipient_name' => $customerData['name'],
                            'debtor_jfr' => '',
                            'amount' => $totalAmount,
                            'currency' => $currency,
                            'foreign_currency' => $foreignCurrency,
                            'createdon' => $createdOn,
                            'user_id' => $userId,
                            'source_created' => 'CU - Excel Upload',
                            'org_id' => $orgId,
                            'be_value' => $beValue,
                        ];
                        $revenue = Revenue::create($revAr);
                        $revIds[] = $revenue->id;
                        Charge::whereIn('id', $revCustIds)->update(['revenue_id' => $revenue->id]);

                        if ($foreignCurrency !== '') {
                            $exchangeRateAmount = $this->getExchangeAmountFromCurrency([
                                'exchange_rate_id' => '0',
                                'from_currency' => $foreignCurrency,
                                'to_currency' => $currency,
                                'user_id' => $userId,
                                'order_id' => $orderId,
                                'revenue_id' => $revenue->id,
                            ]);
                            if ($exchangeRateAmount > 0) {
                                $this->updateLocalAmountByExchangeRate($revenue->id, $exchangeRateAmount, $foreignCurrency, $orgId, $currency);
                            }
                        }
                    }
                }

                if (!empty($revInternalBus)) {
                    $customerData = $this->getPartyNameAndCode($revenueIbu, $orgId, $userId, 'Customer');
                    if (!empty($customerData)) {
                        $total = Charge::whereIn('id', $revInternalBus)->selectRaw('SUM(amount) as amount, currency')->first();
                        $totalAmount = $total->amount ?? 0;
                        $chargeCurrency = $total->currency ?? '';
                        $foreignCurrency = $currency === $chargeCurrency ? '' : $chargeCurrency;
                        $revenueBuJfr = '';

                        if ($revBuJfr !== '') {
                            if (strpos($revBuJfr, '-') !== false && strlen($revBuJfr) === 15) {
                                $revenueBuJfr = $revBuJfr;
                            } elseif (strlen($revBuJfr) === 16) {
                                $revenueBuJfr = $revBuJfr;
                            } else {
                                $failureArr[] = [
                                    'decimal' => '0',
                                    'bu_jfr' => $revBuJfr,
                                    'type' => '0',
                                    'order_id' => $orderRowId,
                                ];
                            }
                        }

                        $revAr = [
                            'type' => '0',
                            'order_id' => $orderRowId,
                            'recipient_role' => 'Internal BU',
                            'recipient_code' => $customerData['party_code'],
                            'recipient_name' => $customerData['name'],
                            'debtor_jfr' => '',
                            'bu_jfr' => $revenueBuJfr,
                            'amount' => $totalAmount,
                            'currency' => $currency,
                            'foreign_currency' => $foreignCurrency,
                            'createdon' => $createdOn,
                            'user_id' => $userId,
                            'org_id' => $orgId,
                            'be_value' => $beValue,
                        ];
                        $revenue = Revenue::create($revAr);
                        $revIds[] = $revenue->id;
                        Charge::whereIn('id', $revInternalBus)->update(['revenue_id' => $revenue->id]);

                        if ($foreignCurrency !== '') {
                            $exchangeRateAmount = $this->getExchangeAmountFromCurrency([
                                'exchange_rate_id' => '0',
                                'from_currency' => $foreignCurrency,
                                'to_currency' => $currency,
                                'user_id' => $userId,
                                'order_id' => $orderId,
                                'revenue_id' => $revenue->id,
                            ]);
                            if ($exchangeRateAmount > 0) {
                                $this->updateLocalAmountByExchangeRate($revenue->id, $exchangeRateAmount, $foreignCurrency, $orgId, $currency);
                            }
                        }
                    }
                }

                if (!empty($venCustIds)) {
                    $vendorData = $this->getPartyNameAndCode($vendor, $orgId, $userId, 'Vendor');
                    if (!empty($vendorData)) {
                        $total = Charge::whereIn('id', $venCustIds)->selectRaw('SUM(amount) as amount, currency')->first();
                        $totalAmount = $total->amount ?? 0;
                        $chargeCurrency = $total->currency ?? '';
                        $foreignCurrency = $currency === $chargeCurrency ? '' : $chargeCurrency;

                        $costAr = [
                            'type' => '1',
                            'order_id' => $orderRowId,
                            'recipient_role' => 'Carrier',
                            'recipient_code' => $vendorData['party_code'],
                            'recipient_name' => $vendorData['name'],
                            'debtor_jfr' => '',
                            'amount' => $totalAmount,
                            'currency' => $currency,
                            'foreign_currency' => $foreignCurrency,
                            'createdon' => $createdOn,
                            'user_id' => $userId,
                            'source_created' => 'CU - Excel Upload',
                            'org_id' => $orgId,
                            'be_value' => $beValue,
                        ];
                        $cost = Revenue::create($costAr);
                        $revIds[] = $cost->id;
                        Charge::whereIn('id', $venCustIds)->update(['revenue_id' => $cost->id]);

                        if ($foreignCurrency !== '') {
                            $exchangeRateAmount = $this->getExchangeAmountFromCurrency([
                                'exchange_rate_id' => '0',
                                'from_currency' => $foreignCurrency,
                                'to_currency' => $currency,
                                'user_id' => $userId,
                                'order_id' => $orderId,
                                'revenue_id' => $cost->id,
                            ]);
                            if ($exchangeRateAmount > 0) {
                                $this->updateLocalAmountByExchangeRate($cost->id, $exchangeRateAmount, $foreignCurrency, $orgId, $currency);
                            }
                        }
                    }
                }

                if (!empty($costInternalBus)) {
                    $vendorData = $this->getPartyNameAndCode($costIbu, $orgId, $userId, 'Vendor');
                    if (!empty($vendorData)) {
                        $total = Charge::whereIn('id', $costInternalBus)->selectRaw('SUM(amount) as amount, currency')->first();
                        $totalAmount = $total->amount ?? 0;
                        $chargeCurrency = $total->currency ?? '';
                        $foreignCurrency = $currency === $chargeCurrency ? '' : $chargeCurrency;
                        $costBuJfrVal = '';

                        if ($costBuJfr !== '') {
                            if (strpos($costBuJfr, '-') !== false && strlen($costBuJfr) === 15) {
                                $costBuJfrVal = $costBuJfr;
                            } elseif (strlen($costBuJfr) === 16) {
                                $costBuJfrVal = $costBuJfr;
                            } else {
                                $failureArr[] = [
                                    'decimal' => '0',
                                    'bu_jfr' => $costBuJfr,
                                    'type' => '1',
                                    'order_id' => $orderRowId,
                                ];
                            }
                        }

                        $costAr = [
                            'type' => '1',
                            'order_id' => $orderRowId,
                            'recipient_role' => 'Internal BU',
                            'recipient_code' => $vendorData['party_code'],
                            'recipient_name' => $vendorData['name'],
                            'debtor_jfr' => '',
                            'bu_jfr' => $costBuJfrVal,
                            'amount' => $totalAmount,
                            'currency' => $currency,
                            'foreign_currency' => $foreignCurrency,
                            'createdon' => $createdOn,
                            'user_id' => $userId,
                            'org_id' => $orgId,
                            'be_value' => $beValue,
                        ];
                        $cost = Revenue::create($costAr);
                        $revIds[] = $cost->id;
                        Charge::whereIn('id', $costInternalBus)->update(['revenue_id' => $cost->id]);

                        if ($foreignCurrency !== '') {
                            $exchangeRateAmount = $this->getExchangeAmountFromCurrency([
                                'exchange_rate_id' => '0',
                                'from_currency' => $foreignCurrency,
                                'to_currency' => $currency,
                                'user_id' => $userId,
                                'order_id' => $orderId,
                                'revenue_id' => $cost->id,
                            ]);
                            if ($exchangeRateAmount > 0) {
                                $this->updateLocalAmountByExchangeRate($cost->id, $exchangeRateAmount, $foreignCurrency, $orgId, $currency);
                            }
                        }
                    }
                }
            }
        }

        if (!empty($revIds)) {
            $this->updateVatToRevenues($revIds, $orgId, $currency, $userId);
        }

        $result = $this->checkOrdersChargesData($allOrders, $failureArr);

        // Save result to file (optional, depending on your needs)
        file_put_contents(storage_path('app/charges_exceluploaddata.txt'), json_encode($result));

        return $result;
    }

    public function checkDecimalAmountForVn(float $amount, int $orgId, string $sessionCurrency, string $currency): int
    {
        if ($sessionCurrency === $currency) {
            $decimalPart = fmod($amount, 1);
            return $decimalPart == 0 ? 1 : 0;
        }
        return 1;
    }

    public function getPartyNameAndCode(string $code, int $orgId, int $userId, string $partyType): array
    {
        $query = SxPartyMembers::select('name', 'code')
            ->join('sx_party_members as c', function ($join) use ($orgId) {
                $join->on('c.code', '=', 'sx_party_members.code')
                    ->where('c.org_id', '=', $orgId)
                    ->where('c.status', '=', '1');
            })
            ->where(function ($query) use ($code) {
                $query->where('sx_party_members.code', $code)
                    ->orWhere('sx_party_members.name', 'like', $code);
            })
            ->where('sx_party_members.org_id', $orgId)
            ->where('sx_party_members.status', '1')
            ->orderByDesc('sx_party_members.id')
            ->first();

        return $query ? ['name' => $query->name, 'party_code' => $query->code] : [];
    }

    public function updateLocalAmountByExchangeRate(int $revId, float $exchangeRateAmount, string $foreignCurrency, int $orgId, string $localCurrency): void
    {
        if ($revId <= 0) {
            return;
        }

        Revenue::where('id', $revId)->update([
            'exchange_rate' => $exchangeRateAmount,
            'foreign_currency' => $foreignCurrency,
        ]);

        if ($foreignCurrency !== '') {
            $charges = Charge::where(['revenue_id' => $revId, 'currency' => $foreignCurrency, 'status' => '1'])
                ->select(['id', 'amount', 'local_amount', 'vat_percentage', 'vat_amount'])
                ->get();

            foreach ($charges as $charge) {
                $amount = $charge->amount;
                $chargeRowId = $charge->id;
                $chargeLocalAmount = $charge->local_amount;
                $vatPercentage = $charge->vat_percentage;

                $updArr = [];
                if (!$chargeLocalAmount) {
                    $finalLocalAmount = $amount * $exchangeRateAmount;
                    if ($finalLocalAmount > 0) {
                        $updArr['local_amount'] = $orgId == 'VNKN' ? round($finalLocalAmount) : $finalLocalAmount;
                        $updArr['local_currency'] = $localCurrency;

                        $finalTotalAmount = 0;
                        if ($vatPercentage > 0) {
                            $vatAmount = $charge->vat_amount;
                            if ($vatAmount > 0) {
                                $localVatAmount = $vatAmount * $exchangeRateAmount;
                                $updArr['local_vat_amount'] = $orgId == 'VNKN' ? round($localVatAmount) : $localVatAmount;
                                $finalTotalAmount = $localVatAmount + $updArr['local_amount'];
                            }
                        }
                        $updArr['local_total_amount'] = $orgId == 'VNKN' ? round($finalTotalAmount) : $finalTotalAmount;

                        if (!empty($updArr)) {
                            Charge::where('id', $chargeRowId)->update($updArr);
                        }
                    }
                }
            }
        }
    }

    public function updateVatToRevenues(array $revIds, int $orgId, string $userCurrency, int $userId): void
    {
        foreach ($revIds as $revenueId) {
            $revenue = Revenue::where('id', $revenueId)->first();
            if (!$revenue) {
                continue;
            }

            $orderId = $revenue->order_id;
            $type = $revenue->type;
            $recipientCode = $revenue->recipient_code;
            $exchangeRate = $revenue->exchange_rate ?? 0;

            $orderDetails = $this->getTableRowData(
                ['id' => $orderId],
                'id,pickup_country,delivery_country,pickup_city,delivery_city,pickup_address2,delivery_address2',
                'order'
            );

            $pickupCountry = $orderDetails['pickup_country'] ?? '';
            $deliveryCountry = $orderDetails['delivery_country'] ?? '';
            $pickupCity = $orderDetails['pickup_city'] ?? '';
            $deliveryCity = $orderDetails['delivery_city'] ?? '';
            $pickupAddress2 = $orderDetails['pickup_address2'] ?? '';
            $deliveryAddress2 = $orderDetails['delivery_address2'] ?? '';

            $source = [1 => trim($pickupCountry), 2 => trim($pickupAddress2), 3 => trim($pickupCity)];
            $destination = [1 => trim($deliveryCountry), 2 => trim($deliveryAddress2), 3 => trim($deliveryCity)];

            $charges = Charge::where(['revenue_id' => $revenueId, 'status' => '1'])
                ->select(['id', 'charge_code', 'amount', 'currency'])
                ->get();

            foreach ($charges as $charge) {
                $chargeId = $charge->id;
                $chargeCodeId = $charge->charge_code;
                $chargeAmount = $charge->amount;
                $chargeCurrency = $charge->currency;

                $vatDetails = $this->getVatDetails([
                    'custcode' => $recipientCode,
                    'chargecodeid' => $chargeCodeId,
                    'user_id' => $userId,
                    'org_id' => $orgId,
                    'type' => $type,
                ]);

                $vatPercentage = 0;
                $catId = 0;
                $catVal = '';

                if (!empty($vatDetails)) {
                    foreach ($vatDetails as $res) {
                        $sourceGeo = $res['source_geo'];
                        $destinationGeo = $res['destination_geo'];
                        $sourceCountry = trim($res['source_country']);
                        $destinationCountry = trim($res['destination_country']);

                        if (strcasecmp($source[$sourceGeo], $sourceCountry) === 0 && strcasecmp($destination[$destinationGeo], $destinationCountry) === 0) {
                            $vatPercentage = round($res['vat'], 2);
                            $catId = $res['cat_id'];
                            $catVal = $res['cat_val'];
                        }
                    }
                } else {
                    $vatGeneric = $this->getVatGeneric([
                        'custcode' => $recipientCode,
                        'chargecodeid' => $chargeCodeId,
                        'user_id' => $userId,
                        'org_id' => $orgId,
                        'type' => $type,
                    ]);

                    foreach ($vatGeneric as $res) {
                        $sourceGeo = $res['source_geo'];
                        $destinationGeo = $res['destination_geo'];
                        $sourceCountry = trim($res['source_country']);
                        $destinationCountry = trim($res['destination_country']);

                        if (strcasecmp($source[$sourceGeo], $sourceCountry) === 0 && strcasecmp($destination[$destinationGeo], $destinationCountry) === 0) {
                            $vatPercentage = round($res['vat'], 2);
                            $catId = $res['cat_id'];
                            $catVal = $res['cat_val'];
                        }
                    }
                }

                $totalAmount = $chargeAmount;
                $vatAmount = 0;
                $localTotalAmount = 0;
                $localVatAmount = 0;

                if ($vatPercentage > 0) {
                    $vatAmount = ($vatPercentage * $chargeAmount) / 100;
                    $totalAmount = $vatAmount + $chargeAmount;
                }

                $totalAmount = round($totalAmount, 4);
                if ($userCurrency === $chargeCurrency) {
                    $localTotalAmount = round($totalAmount);
                    $localVatAmount = round($vatAmount, 4);
                } elseif ($exchangeRate > 0) {
                    $recentTotalAmount = round($totalAmount, 4);
                    $localTotalAmount = $recentTotalAmount * $exchangeRate;
                    $recentVatAmount = round($vatAmount, 4);
                    $localVatAmount = $recentVatAmount * $exchangeRate;
                }

                if ($orgId == 'VNKN') {
                    if ($userCurrency === $chargeCurrency) {
                        $totalAmount = round($totalAmount);
                        $localTotalAmount = round($totalAmount);
                    } elseif ($exchangeRate > 0) {
                        $recentTotalAmount = round($totalAmount);
                        $localTotalAmount = $recentTotalAmount * $exchangeRate;
                        $recentVatAmount = round($vatAmount, 4);
                        $localVatAmount = $recentVatAmount * $exchangeRate;
                    }
                }

                Charge::where('id', $chargeId)->update([
                    'vat_percentage' => $vatPercentage,
                    'vat_amount' => round($vatAmount, 4),
                    'total_amount' => $totalAmount,
                    'cat_id' => $catId,
                    'cat_val' => $catVal,
                    'local_vat_amount' => $localVatAmount,
                    'local_total_amount' => $localTotalAmount,
                ]);
            }

            $sumAmount = Charge::where('revenue_id', $revenueId)->sum('total_amount');
            $foreignCurrency = Revenue::where('id', $revenueId)->value('foreign_currency') ?? '';

            $updData = ['amount' => $orgId == 'VNKN' && $foreignCurrency === '' ? round($sumAmount) : round($sumAmount, 4)];
            Revenue::where('id', $revenueId)->update($updData);
        }
    }

    public function checkOrdersChargesData(array $allOrders, array $failureArr): array
    {
        $response = [];
        $orderIds = [];
        $currency = Auth::user()->timezone['currency'] ?? config('app.currency', 'USD');

        foreach ($allOrders as $ord) {
            if ($ord['id'] > 0 && !in_array($ord['id'], $orderIds)) {
                $orderIds[] = $ord['id'];

                $totalRevenue = 0;
                $revJfr = '';
                $totalCost = 0;
                $costJfr = '';
                $revBuJfrs = '';
                $costBuJfrs = '';
                $revFailure = '';
                $cstFailure = '';

                // Get total revenue
                $revData = $this->getTableRowData(
                    ['order_id' => $ord['id'], 'type' => '0', 'status' => '1'],
                    'SUM(amount) as total_amount, debtor_jfr',
                    'revenue'
                );
                if (!empty($revData)) {
                    $totalRevenue = $revData['total_amount'] ?? 0;
                    $revJfr = $revData['debtor_jfr'] ?? '';
                }

                // Get total cost
                $costData = $this->getTableRowData(
                    ['order_id' => $ord['id'], 'type' => '1', 'status' => '1'],
                    'SUM(amount) as total_amount, debtor_jfr',
                    'revenue'
                );
                if (!empty($costData)) {
                    $totalCost = $costData['total_amount'] ?? 0;
                    $costJfr = $costData['debtor_jfr'] ?? '';
                }

                // Get revenue BU JFRs
                $revBuJfr = Revenue::where([
                    'order_id' => $ord['id'],
                    'type' => '0',
                    'recipient_role' => 'Internal BU',
                    'status' => '1'
                ])->pluck('bu_jfr')->filter()->toArray();
                if (!empty($revBuJfr)) {
                    $revBuJfrs = implode(', ', $revBuJfr);
                }

                // Get cost BU JFRs
                $costBuJfr = Revenue::where([
                    'order_id' => $ord['id'],
                    'type' => '1',
                    'recipient_role' => 'Internal BU',
                    'status' => '1'
                ])->pluck('bu_jfr')->filter()->toArray();
                if (!empty($costBuJfr)) {
                    $costBuJfrs = implode(', ', $costBuJfr);
                }

                // Process failures
                foreach ($failureArr as $ff) {
                    $decimal = $ff['decimal'] ?? '0';
                    $tType = $ff['type'] === '0' ? 'REV' : 'COST';
                    $finalBuJfr = $ff['bu_jfr'] ?? '';
                    $simpleAr = ['bu_jfr' => $finalBuJfr, 'type' => $ff['type'], 'order_id' => $ff['order_id']];

                    if ($ord['id'] == $simpleAr['order_id']) {
                        if ($decimal === '1') {
                            $status = $ff['status'];
                            if ($tType === 'REV') {
                                $revFailure = $status;
                            } elseif ($tType === 'COST') {
                                $cstFailure = $status;
                            }
                        } elseif ($decimal === '0') {
                            $failureMsg = "Bu jfr ({$simpleAr['bu_jfr']}) is not in correct Format";
                            if ($tType === 'REV') {
                                $revFailure .= $revFailure ? " and $failureMsg" : $failureMsg;
                            } elseif ($tType === 'COST') {
                                $cstFailure .= $cstFailure ? " and $failureMsg" : $failureMsg;
                            }
                        }
                    }
                }

                $response[] = [
                    'booking_id' => $ord['booking_id'],
                    'total_revenue' => $totalRevenue,
                    'rev_debtor_jfr' => $revJfr,
                    'rev_bu_jfrs' => $revBuJfrs,
                    'total_cost' => $totalCost,
                    'cost_jfr' => $costJfr,
                    'cost_bu_jfrs' => $costBuJfrs,
                    'rev_failure' => $revFailure,
                    'cst_failure' => $cstFailure,
                ];
            }
        }

        return $response;
    }

    public function getVatDetails(array $data): array
    {
        $query = VatMaster::select([
            'vat_master.name',
            'vat_master.cat_id',
            'vat_master.cat_val',
            'vat_master.vat',
            'lane_vat.charge_id',
            'charge_codes.charge_code',
            'lanes.source_geo',
            'lanes.source_country',
            'lanes.destination_geo',
            'lanes.destination_country',
        ])
            ->from('vat_master')
            ->leftJoin('lanes', 'lanes.vatid', '=', 'vat_master.id')
            ->leftJoin('lane_vat', 'lane_vat.lane_id', '=', 'lanes.id')
            ->leftJoin('charge_codes', 'charge_codes.id', '=', 'lane_vat.charge_id')
            ->leftJoin('sx_party_members', function ($join) use ($data) {
                $join->on(
                    $data['type'] == '0' ? 'sx_party_members.customeridentifier' : 'sx_party_members.vendoridentifier',
                    '=',
                    'vat_master.' . ($data['type'] == '0' ? 'customeridentifier' : 'vendoridentifier')
                );
            })
            ->where('sx_party_members.code', $data['custcode'])
            ->where('charge_codes.id', $data['chargecodeid'])
            ->where('vat_master.org_id', $data['org_id'])
            ->where('vat_master.status', '1')
            ->where('lanes.status', '1')
            ->where('lane_vat.status', '1')
            ->where('sx_party_members.user_id', $data['user_id'])
            ->where('sx_party_members.status', '1');

        return $query->get()->toArray();
    }

    public function getVatGeneric(array $data): array
    {
        $query = VatMaster::select([
            'vat_master.name',
            'vat_master.cat_id',
            'vat_master.cat_val',
            'lane_vat.charge_id',
            'lane_vat.vat',
            'charge_codes.charge_code',
            'lanes.source_geo',
            'lanes.source_country',
            'lanes.destination_geo',
            'lanes.destination_country'
        ])
            ->leftJoin('lanes', 'lanes.vatid', '=', 'vat_master.id')
            ->leftJoin('lane_vat', 'lane_vat.lane_id', '=', 'lanes.id')
            ->leftJoin('charge_codes', 'charge_codes.id', '=', 'lane_vat.charge_id')
            ->where('charge_codes.id', $data['chargecodeid'])
            ->where('vat_master.org_id', $data['org_id'])
            ->where('vat_master.status', '1')
            ->where('lanes.status', '1')
            ->where('lane_vat.status', '1');

        return $query->get()->toArray();
    }

    
}
