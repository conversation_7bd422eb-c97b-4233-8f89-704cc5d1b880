<?php

namespace App\Http\Controllers;

use App\Models\ReturnTruck;
use App\Models\TrucksData;
use App\Models\TruckDriver;
use App\Models\Customer;
use App\Models\Vendor;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;

class ReturnTrucksController extends Controller
{
    /**
     * Display a listing of return trucks.
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $user_id = $user->id ?? 1;
            $org_id = $user->org_id ?? 1;

            $query = ReturnTruck::select([
                'return_trucks.id',
                'trucks_data.register_number as vehicle_register_number',
                'truck_drivers.name as driver_name',
                'customers.name as customer_name',
                'vendors.name as carrier_name',
                'return_trucks.splace',
                'return_trucks.eplace',
                'return_trucks.startdate',
                'return_trucks.shipmentid',
                'return_trucks.user_id',
                'return_trucks.org_id',
                'return_trucks.be_value',
                'return_trucks.status'
            ])
            ->leftJoin('trucks_data', 'return_trucks.vehicle_id', '=', 'trucks_data.id')
            ->leftJoin('truck_drivers', 'return_trucks.driver_id', '=', 'truck_drivers.id')
            ->leftJoin('customers', 'return_trucks.customer_id', '=', 'customers.id')
            ->leftJoin('vendors', 'return_trucks.vendor_id', '=', 'vendors.id')
            ->where('return_trucks.user_id', $user_id)
            ->where('return_trucks.status', 1);

            if ($request->filled('fromdate_search')) {
                $fromDate = date('Y-m-d', strtotime($request->fromdate_search));
                $query->whereDate('return_trucks.created_at', '>=', $fromDate);
            }
            if ($request->filled('todate_search')) {
                $toDate = date('Y-m-d', strtotime($request->todate_search));
                $query->whereDate('return_trucks.created_at', '<=', $toDate);
            }

            $returnTrucks = $query->orderBy('return_trucks.created_at', 'desc')->get();

            $transformedData = $returnTrucks->map(function ($returnTruck) {
                return [
                    'id' => $returnTruck->id,
                    'vehicle_register_number' => $returnTruck->vehicle_register_number ?? '',
                    'driver_name' => $returnTruck->driver_name ?? '',
                    'customer_name' => $returnTruck->customer_name ?? '',
                    'carrier_name' => $returnTruck->carrier_name ?? '',
                    'splace' => $returnTruck->splace,
                    'eplace' => $returnTruck->eplace,
                    'startdate' => $returnTruck->startdate,
                    'shipmentid' => $returnTruck->shipmentid,
                    'user_id' => $returnTruck->user_id,
                    'org_id' => $returnTruck->org_id,
                    'be_value' => $returnTruck->be_value,
                    'status' => $returnTruck->status
                ];
            });

            $datatableHeaders = [
                'Trip No.',
                'Vehicle Number',
                'Driver Name',
                'Customer Name',
                'Carrier Name',
                'Source',
                'Destination',
                'Pickup Date',
                'Shipment ID',
                'User ID',
                'Org ID',
                'Be Value'
            ];

            $datatableSettings = $this->getDatatableSettings($org_id);

            $data = [
                'return_trucks' => $transformedData,
                'datatable_headers' => $datatableHeaders,
                'column_visibility' => $datatableSettings['column_visibility'] ?? array_fill(0, count($datatableHeaders), true),
                'datatable_header_sequence' => $datatableSettings['sequence_data'] ?? [],
                'datatable_header_toggle' => $datatableSettings['toggle_data'] ?? [],
                'datatable_header_sequence_index' => $datatableSettings['sequence_index'] ?? range(0, count($datatableHeaders) - 1),
                'postData' => $request->all()
            ];

            return response()->json([
                'status' => 'success',
                'message' => 'Return trucks retrieved successfully',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get datatable settings from database
     */
    private function getDatatableSettings($org_id)
    {
        try {
            $settings = DB::table('datatable_settings')
                ->where('controller_name', 'ReturnTrucksController')
                ->where('method_name', 'index')
                ->where('org_id', $org_id)
                ->first();

            if (!$settings) {
                return [
                    'column_visibility' => array_fill(0, 10, true),
                    'sequence_data' => [],
                    'toggle_data' => [],
                    'sequence_index' => range(0, 9)
                ];
            }

            return [
                'column_visibility' => $settings->sequence_data ? unserialize($settings->sequence_data) : array_fill(0, 10, true),
                'sequence_data' => $settings->sequence_data ? unserialize($settings->sequence_data) : [],
                'toggle_data' => $settings->toggle_data ? unserialize($settings->toggle_data) : [],
                'sequence_index' => $settings->sequence_data ? $this->buildSequenceIndex(unserialize($settings->sequence_data)) : range(0, 9)
            ];

        } catch (\Exception $e) {
            return [
                'column_visibility' => array_fill(0, 10, true),
                'sequence_data' => [],
                'toggle_data' => [],
                'sequence_index' => range(0, 9)
            ];
        }
    }

    /**
     * Build sequence index from sequence data
     */
    private function buildSequenceIndex($sequenceData)
    {
        $datatableHeaders = [
            'Trip No.',
            'Vehicle Number',
            'Driver Name',
            'Customer Name',
            'Carrier Name',
            'Source',
            'Destination',
            'Pickup Date',
            'Org ID',
            'Be Value'
        ];

        $sequenceIndex = [0, 1]; // Start with checkbox and actions columns
        foreach ($sequenceData as $value) {
            $index = array_search($value, $datatableHeaders);
            if ($index !== false) {
                $sequenceIndex[] = $index + 2; // Add 2 for checkbox and actions columns
            }
        }

        // Add remaining columns
        foreach ($datatableHeaders as $key => $header) {
            $index = $key + 2;
            if (!in_array($index, $sequenceIndex)) {
                $sequenceIndex[] = $index;
            }
        }

        return array_values(array_filter($sequenceIndex));
    }
}
