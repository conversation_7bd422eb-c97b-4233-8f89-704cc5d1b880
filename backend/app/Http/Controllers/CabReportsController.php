<?php

namespace App\Http\Controllers;

use App\Models\Trip;
use App\Models\TripSummary;
use App\Models\TrucksData;
use App\Models\TruckDriver;
use App\Models\Shipment;
use App\Models\TripEmployee;
use App\Models\ShiporderStopSequence;
use App\Models\ShiporderStop;
use App\Models\ShiftZone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Database\QueryException;
use Illuminate\Validation\ValidationException;
use Illuminate\Pagination\LengthAwarePaginator;

class CabReportsController extends Controller
{
    /**
     * Fetch SLA reports data.
     */
    public function slareports(Request $request)
    {
        try {
            $user = Auth::user();
            $user_id = $user->id;
            $org_id = $user->org_id;
            $be_value = $user->be_value ?? null;
            $business_type = $user->business_type ?? 'Default';
            $timezone = $user->timezone ?? 'UTC';

            $enddt = Carbon::today()->setTimezone($timezone)->toDateString();
            $startdt = Carbon::today()->subDays(7)->setTimezone($timezone)->toDateString();

            $post = [
                'fromdate_search' => $request->input('fromdate_search', $startdt),
                'todate_search' => $request->input('todate_search', $enddt),
                'driver_search' => $request->input('driver_search', ''),
                'vehicle_search' => $request->input('vehicle_search', ''),
                'shift_search' => $request->input('shift_search', []),
                'zone_search' => $request->input('zone_search', ''),
                'type_search' => $request->input('type_search', ''),
                'user_id' => $user_id,
            ];

            if ($post['fromdate_search']) {
                $post['fromdate_search'] = Carbon::parse($post['fromdate_search'])->setTimezone($timezone)->toDateString();
            }
            if ($post['todate_search']) {
                $post['todate_search'] = Carbon::parse($post['todate_search'])->setTimezone($timezone)->toDateString();
            }

            $perPage = $request->input('per_page', 10);
            $page = $request->input('page', 1);
            $offset = ($page - 1) * $perPage;

            $tripsCount = $this->getslareports($post, 0, 0)->count();
            $tripsData = $this->getslareports($post, $perPage, $offset)->get();
            $tripIds = $tripsData->pluck('trip_id')->toArray();
            $tripsdata = $tripsData->toArray();

            $tripDataByTripId = $this->getTripDataByTripIds($tripIds, $timezone);
            $weightAndVolumeByTripIds = $this->getWeightAndVolumeByTripIds($tripIds);

            $tripSummaryData = $this->getslasummary($post)->get();
            $vehicleIds = $tripSummaryData->pluck('id')->toArray();
            $tripsummarydata = $tripSummaryData->toArray();

            $weightAndVolumeByVehicleIds = $this->getWeightAndVolumeByTripAndVehicleIds($tripIds, $vehicleIds);
            $driverDelayByVehicleIds = $this->getDriverDelayByVehicleIds($post, $vehicleIds);

            $shiftsQuery = Shipment::select('id', 'scity', 'dcity', 'shipmentid')
                ->where('status', 0)
                ->orderBy('startdate', 'desc');
            if ($business_type === 'Carrier') {
                $shiftsQuery->where('vendor_id', $user_id);
            } else {
                $shiftsQuery->where('user_id', $user_id);
            }
            $shifts = $shiftsQuery->get();

            $zones = ShiftZone::select('id', 'zone_name', 'splace', 'eplace')
                ->where('status', 'Active')
                ->where('user_id', $user_id)
                ->where('org_id', $org_id)
                ->get();

            return response()->json([
                'status' => true,
                'message' => 'SLA reports fetched successfully',
                'tripscount' => $tripsCount,
                'tripsdata' => $tripsdata,
                'tripDataByTripId' => $tripDataByTripId,
                'weightAndVolumeByTripIds' => $weightAndVolumeByTripIds,
                'tripsummarydata' => $tripsummarydata,
                'weightAndVolumeByVehicleIds' => $weightAndVolumeByVehicleIds,
                'driverDelayByVehicleIds' => $driverDelayByVehicleIds,
                'shifts' => $shifts,
                'zones' => $zones,
                'post' => $post,
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to fetch SLA reports: ' . $e->getMessage(),
            ], 500);
        }
    }


    /**
     * Get SLA reports data with filtering and pagination.
     */
    protected function getslareports($post, $limit, $offset)
    {
        $user_id = $post['user_id'];
        $business_type = Auth::user()->business_type ?? 'Default';
        $org_id = Auth::user()->org_id;
        $timezone = Auth::user()->timezone ?? 'UTC';

        $query = TripSummary::select(
            'trucks_data.register_number',
            DB::raw('IFNULL(trucks_data.truck_capacity, 0) as tcapacity'),
            'trip_summary.trip_id',
            DB::raw("DATE_FORMAT(CONVERT_TZ(trip_summary.start_time, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as start_time"),
            DB::raw("DATE_FORMAT(CONVERT_TZ(trip_summary.end_time, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as end_time"),
            'trip_summary.trip_distance',
            'trip_summary.attended_emp',
            'trip_summary.trip_type',
            'truck_drivers.name',
            'truck_drivers.contact_num',
            'trips.act_splace',
            'trips.act_eplace',
            'trips.splace',
            DB::raw('(SELECT shipment.etime FROM trips it INNER JOIN shipment ON it.shift_id = shipment.id WHERE it.id = trip_summary.trip_id) as dvrlate')
        )
            ->join('trucks_data', 'trip_summary.vehicle_id', '=', 'trucks_data.id')
            ->join('truck_drivers', 'trip_summary.driver_id', '=', 'truck_drivers.id')
            ->join('trips', 'trip_summary.trip_id', '=', 'trips.id')
            ->where('trip_summary.trip_status', 1)
            ->where('trip_summary.org_id', $org_id);

        if ($business_type === 'Carrier') {
            $query->where('trip_summary.vendor_id', $user_id);
        } else {
            $query->where('trip_summary.user_id', $user_id);
        }

        if (!empty($post['fromdate_search'])) {
            $query->whereDate('trip_summary.createdon', '>=', $post['fromdate_search']);
        }

        if (!empty($post['todate_search'])) {
            $query->whereDate('trip_summary.createdon', '<=', $post['todate_search']);
        }

        if (!empty($post['driver_search'])) {
            $driverIds = $this->getDriversBySearch($post['driver_search']);
            $query->whereIn('trip_summary.driver_id', $driverIds ?: [0]);
        }

        if (!empty($post['vehicle_search'])) {
            $vehicleIds = $this->getVehiclesBySearch($post['vehicle_search']);
            $query->whereIn('trip_summary.vehicle_id', $vehicleIds ?: [0]);
        }

        if (!empty($post['zone_search'])) {
            $tripIds = $this->getTripsByZoneSearch($post['zone_search']);
            $query->whereIn('trip_summary.trip_id', $tripIds ?: [0]);
        }

        if (!empty($post['shift_search'])) {
            $post['shift_search'] = array_filter($post['shift_search']);
            if (!empty($post['shift_search'])) {
                $tripIds = $this->getTripsByShiftSearch($post['shift_search']);
                $query->whereIn('trip_summary.trip_id', $tripIds ?: [0]);
            }
        }

        $query->orderBy('trip_summary.createdon', 'desc');

        if ($limit > 0) {
            $query->take($limit)->skip($offset);
        }

        return $query;
    }

    /**
     * Get trip data by trip IDs.
     */
    protected function getTripDataByTripIds(array $tripIds, $timezone)
    {
        if (empty($tripIds)) {
            return [];
        }

        $data = Shipment::select(
            'shipment.id',
            DB::raw("DATE_FORMAT(CONVERT_TZ(shipment.startdate, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as stime"),
            DB::raw("DATE_FORMAT(CONVERT_TZ(shipment.enddate, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as etime"),
            'trips.splace',
            'trips.eplace',
            'shipment.empshift_start',
            DB::raw("DATE_FORMAT(CONVERT_TZ(shipment.startdate, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as startdate"),
            DB::raw("DATE_FORMAT(CONVERT_TZ(shipment.enddate, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as enddate"),
            'shipment.shipmentid',
            'trips.id as trip_id'
        )
            ->join('trips', 'shipment.id', '=', 'trips.shift_id')
            ->whereIn('trips.id', $tripIds)
            ->get()
            ->keyBy('trip_id')
            ->toArray();

        return $data;
    }

    /**
     * Get weight and volume data by trip IDs.
     */
    protected function getWeightAndVolumeByTripIds(array $tripIds)
    {
        if (empty($tripIds)) {
            return [];
        }

        $data = Trip::select(
            'trips.id',
            DB::raw('IFNULL(ROUND(AVG(shiporder_stop_sequence.shipment_weight)*100/trucks_data.truck_weight, 2), 0) as avg_weight'),
            DB::raw('IFNULL(ROUND(AVG(shiporder_stop_sequence.shipment_volume)*100/trucks_data.truck_volume, 2), 0) as avg_volume')
        )
            ->join('trip_employee', 'trips.id', '=', 'trip_employee.trip_id')
            ->join('shiporder_stop_sequence', 'trip_employee.employee_id', '=', 'shiporder_stop_sequence.id')
            ->join('trucks_data', 'trips.vehicle_id', '=', 'trucks_data.id')
            ->whereIn('trips.id', $tripIds)
            ->groupBy('trips.id')
            ->get()
            ->keyBy('id')
            ->toArray();

        return $data;
    }

    /**
     * Get SLA summary data.
     */
    protected function getslasummary($post)
    {
        $user_id = $post['user_id'];
        $org_id = Auth::user()->org_id;
        $business_type = Auth::user()->business_type ?? 'Default';

        $query = TripSummary::select(
            'trucks_data.register_number',
            'trucks_data.id',
            DB::raw('SUM(IFNULL(trucks_data.truck_capacity, 0)) as tcapacity'),
            DB::raw('SUM(trip_summary.attended_emp) as attended_emp'),
            DB::raw('COUNT(trip_summary.trip_id) as tripcount'),
            DB::raw('SUM(trip_summary.trip_distance) as tripdist'),
            DB::raw('SUM(IF(trip_summary.trip_type = 0, 1, 0)) as regulartrip'),
            DB::raw('SUM(IF(trip_summary.trip_type = 1, 1, 0)) as returntrip'),
            DB::raw('SUM(IF(trip_summary.trip_type = 2, 1, 0)) as emptytrip'),
            DB::raw('SUM(IF(trip_summary.trip_type = 0, trip_summary.trip_distance, 0)) as regdist'),
            DB::raw('SUM(IF(trip_summary.trip_type = 1, trip_summary.trip_distance, 0)) as returndist'),
            DB::raw('SUM(IF(trip_summary.trip_type = 2, trip_summary.trip_distance, 0)) as emptydist')
        )
            ->join('trucks_data', 'trip_summary.vehicle_id', '=', 'trucks_data.id')
            ->join('truck_drivers', 'trip_summary.driver_id', '=', 'truck_drivers.id')
            ->where('trip_summary.trip_status', 1)
            ->where('trip_summary.org_id', $org_id);

        if ($business_type === 'Carrier') {
            $query->where('trip_summary.vendor_id', $user_id);
        } else {
            $query->where('trip_summary.user_id', $user_id);
        }

        if (!empty($post['fromdate_search'])) {
            $query->whereDate('trip_summary.createdon', '>=', $post['fromdate_search']);
        }

        if (!empty($post['todate_search'])) {
            $query->whereDate('trip_summary.createdon', '<=', $post['todate_search']);
        }

        if (!empty($post['driver_search'])) {
            $driverIds = $this->getDriversBySearch($post['driver_search']);
            $query->whereIn('trip_summary.driver_id', $driverIds ?: [0]);
        }

        if (!empty($post['vehicle_search'])) {
            $vehicleIds = $this->getVehiclesBySearch($post['vehicle_search']);
            $query->whereIn('trip_summary.vehicle_id', $vehicleIds ?: [0]);
        }

        if (!empty($post['zone_search'])) {
            $tripIds = $this->getTripsByZoneSearch($post['zone_search']);
            $query->whereIn('trip_summary.trip_id', $tripIds ?: [0]);
        }

        if (!empty($post['shift_search'])) {
            $post['shift_search'] = array_filter($post['shift_search']);
            if (!empty($post['shift_search'])) {
                $tripIds = $this->getTripsByShiftSearch($post['shift_search']);
                $query->whereIn('trip_summary.trip_id', $tripIds ?: [0]);
            }
        }

        $query->groupBy('trip_summary.vehicle_id');

        return $query;
    }

    /**
     * Get weight and volume data by trip and vehicle IDs.
     */
    protected function getWeightAndVolumeByTripAndVehicleIds(array $tripIds, array $vehicleIds)
    {
        if (empty($tripIds) || empty($vehicleIds)) {
            return [];
        }

        $data = Trip::select(
            'trips.vehicle_id',
            DB::raw('ROUND(AVG(shiporder_stop_sequence.shipment_weight)*100/trucks_data.truck_weight, 2) as avg_weight'),
            DB::raw('ROUND(AVG(shiporder_stop_sequence.shipment_volume)*100/trucks_data.truck_volume, 2) as avg_volume')
        )
            ->join('shiporder_stop_sequence', 'trips.shift_id', '=', 'shiporder_stop_sequence.shift_id')
            ->join('trucks_data', 'trips.vehicle_id', '=', 'trucks_data.id')
            ->whereIn('trips.id', $tripIds)
            ->whereIn('trips.vehicle_id', $vehicleIds)
            ->groupBy('trips.vehicle_id')
            ->get()
            ->keyBy('vehicle_id')
            ->toArray();

        return $data;
    }

    /**
     * Get driver delay percentage by vehicle IDs.
     */
    protected function getDriverDelayByVehicleIds(array $post, array $vehicleIds)
    {
        if (empty($vehicleIds)) {
            return [];
        }

        $user_id = $post['user_id'];
        $org_id = Auth::user()->org_id;
        $business_type = Auth::user()->business_type ?? 'Default';

        $query = TripSummary::select(
            'trips.vehicle_id',
            DB::raw('COUNT(trips.id) as ontime')
        )
            ->join('trips', 'trip_summary.trip_id', '=', 'trips.id')
            ->join('shipment', 'trips.shift_id', '=', 'shipment.id')
            ->where('trip_summary.trip_status', 1)
            ->where('trip_summary.org_id', $org_id)
            ->whereIn('trips.vehicle_id', $vehicleIds)
            ->whereRaw("DATE_FORMAT(trip_summary.end_time, '%H:%i') <= shipment.etime");

        if ($business_type === 'Carrier') {
            $query->where('trip_summary.vendor_id', $user_id);
        } else {
            $query->where('trip_summary.user_id', $user_id);
        }

        if (!empty($post['fromdate_search'])) {
            $query->whereDate('trip_summary.createdon', '>=', $post['fromdate_search']);
        }

        if (!empty($post['todate_search'])) {
            $query->whereDate('trip_summary.createdon', '<=', $post['todate_search']);
        }

        if (!empty($post['shift_search'])) {
            $post['shift_search'] = array_filter($post['shift_search']);
            if (!empty($post['shift_search'])) {
                $tripIds = $this->getTripsByShiftSearch($post['shift_search']);
                $query->whereIn('trip_summary.trip_id', $tripIds ?: [0]);
            }
        }

        $onTimeData = $query->groupBy('trips.vehicle_id')->get()->keyBy('vehicle_id')->toArray();

        $totalTripsQuery = TripSummary::select(
            'trips.vehicle_id',
            DB::raw('COUNT(trip_summary.id) as totcnt')
        )
            ->join('trips', 'trip_summary.trip_id', '=', 'trips.id')
            ->join('truck_drivers', 'trip_summary.driver_id', '=', 'truck_drivers.id')
            ->where('trip_summary.trip_status', 1)
            ->where('trip_summary.org_id', $org_id)
            ->whereIn('trips.vehicle_id', $vehicleIds);

        if ($business_type === 'Carrier') {
            $totalTripsQuery->where('trip_summary.vendor_id', $user_id);
        } else {
            $totalTripsQuery->where('trip_summary.user_id', $user_id);
        }

        if (!empty($post['fromdate_search'])) {
            $totalTripsQuery->whereDate('trip_summary.createdon', '>=', $post['fromdate_search']);
        }

        if (!empty($post['todate_search'])) {
            $totalTripsQuery->whereDate('trip_summary.createdon', '<=', $post['todate_search']);
        }

        if (!empty($post['shift_search'])) {
            $post['shift_search'] = array_filter($post['shift_search']);
            if (!empty($post['shift_search'])) {
                $tripIds = $this->getTripsByShiftSearch($post['shift_search']);
                $totalTripsQuery->whereIn('trip_summary.trip_id', $tripIds ?: [0]);
            }
        }

        $totalTrips = $totalTripsQuery->groupBy('trips.vehicle_id')->get()->keyBy('vehicle_id')->toArray();

        $result = [];
        foreach ($totalTrips as $vehicleId => $total) {
            if ($total['totcnt'] > 0 && isset($onTimeData[$vehicleId])) {
                $result[$vehicleId] = round(($onTimeData[$vehicleId]['ontime'] / $total['totcnt']) * 100, 2);
            } else {
                $result[$vehicleId] = 0;
            }
        }

        return $result;
    }

    /**
     * Mock implementation for getDriversBySearch.
     */
    protected function getDriversBySearch($search)
    {
        return TruckDriver::where('name', 'like', '%' . $search . '%')
            ->orWhere('contact_num', 'like', '%' . $search . '%')
            ->pluck('id')
            ->toArray();
    }

    /**
     * Mock implementation for getVehiclesBySearch.
     */
    protected function getVehiclesBySearch($search)
    {
        return TrucksData::where('register_number', 'like', '%' . $search . '%')
            ->pluck('id')
            ->toArray();
    }

    /**
     * Mock implementation for getTripsByZoneSearch.
     */
    protected function getTripsByZoneSearch($search)
    {
        return Trip::join('shift_zones', function ($join) {
                $join->on('trips.splace', '=', 'shift_zones.splace')
                     ->orOn('trips.eplace', '=', 'shift_zones.eplace');
            })
            ->where('shift_zones.zone_name', 'like', '%' . $search . '%')
            ->pluck('trips.id')
            ->toArray();
    }

    /**
     * Mock implementation for getTripsByShiftSearch.
     */
    protected function getTripsByShiftSearch($shiftIds)
    {
        return Trip::whereIn('shift_id', $shiftIds)
            ->pluck('id')
            ->toArray();
    }

    /**
     * Fetch shift reports data.
     */
    public function shiftreports(Request $request)
    {
        try {
            $user = Auth::user();
            $user_id = $user->id;
            $org_id = $user->org_id;
            $be_value = $user->be_value ?? null;
            $business_type = $user->business_type ?? 'Default';
            $timezone = $user->timezone ?? 'UTC';

            $enddt = Carbon::today()->setTimezone($timezone)->toDateString();
            $startdt = Carbon::today()->subDays(7)->setTimezone($timezone)->toDateString();

            $post = [
                'fromdate_search' => $request->input('fromdate_search', $startdt),
                'todate_search' => $request->input('todate_search', $enddt),
                'driver_search' => $request->input('driver_search', ''),
                'vehicle_search' => $request->input('vehicle_search', ''),
                'shift_search' => $request->input('shift_search', ''),
                'zone_search' => $request->input('zone_search', ''),
                'type_search' => $request->input('type_search', ''),
                'user_id' => $user_id,
            ];

            if ($post['fromdate_search']) {
                $post['fromdate_search'] = Carbon::parse($post['fromdate_search'])->setTimezone($timezone)->toDateString();
            }
            if ($post['todate_search']) {
                $post['todate_search'] = Carbon::parse($post['todate_search'])->setTimezone($timezone)->toDateString();
            }

            $perPage = $request->input('per_page', 10);
            $page = $request->input('page', 1);
            $offset = ($page - 1) * $perPage;

            $tripsCount = $this->getshiftreports($post, 0, 0)->count();
            $tripsData = $this->getshiftreports($post, $perPage, $offset)->get();
            $tripIds = $tripsData->pluck('trip_id')->toArray();
            $shiftIds = $tripsData->pluck('shift_id')->toArray();
            $tripsdata = $tripsData->toArray();

            $tripDetailsByTripId = $this->getTripDetailsByTripIds($tripIds, $timezone);
            $shipUnitsAndPackagesByShiftIds = $this->getShipUnitsAndPackagesByShiftIds($shiftIds);

            $shiftsQuery = Shipment::select('id', 'scity', 'dcity', 'shipmentid')
                ->where('status', 0)
                ->orderBy('startdate', 'desc');
            if ($business_type === 'Carrier') {
                $shiftsQuery->where('vendor_id', $user_id);
            } else {
                $shiftsQuery->where('user_id', $user_id);
            }
            $shifts = $shiftsQuery->get();

            return response()->json([
                'status' => true,
                'message' => 'Shift reports fetched successfully',
                'tripscount' => $tripsCount,
                'tripsdata' => $tripsdata,
                'tripDetailsByTripId' => $tripDetailsByTripId,
                'shipUnitsAndPackagesByShiftIds' => $shipUnitsAndPackagesByShiftIds,
                'shifts' => $shifts,
                'post' => $post,
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to fetch shift reports: ' . $e->getMessage(),
            ], 500);
        }
    }


    /**
     * Get shift reports data with filtering and pagination.
     */
    protected function getshiftreports($post, $limit, $offset)
    {
        $user_id = $post['user_id'];
        $business_type = Auth::user()->business_type ?? 'Default';
        $org_id = Auth::user()->org_id;
        $timezone = Auth::user()->timezone ?? 'UTC';

        $query = TripSummary::select(
            'trip_summary.trip_id',
            'trips.shift_id',
            'trucks_data.register_number',
            'truck_drivers.name as driver_name',
            'truck_drivers.contact_num',
            DB::raw("DATE_FORMAT(CONVERT_TZ(trip_summary.start_time, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as start_time"),
            DB::raw("DATE_FORMAT(CONVERT_TZ(trip_summary.end_time, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as end_time"),
            DB::raw("TIMESTAMPDIFF(MINUTE, shipment.startdate, shipment.enddate) as trip_duration"),
            DB::raw("DATE_FORMAT(CONVERT_TZ(trip_summary.createdon, 'UTC', '$timezone'), '%Y-%m-%d') as trip_date"),
            DB::raw("(SELECT shipment.etime FROM shipment WHERE shipment.id = trips.shift_id) as scheduled_end_time")
        )
            ->join('trips', 'trip_summary.trip_id', '=', 'trips.id')
            ->join('trucks_data', 'trip_summary.vehicle_id', '=', 'trucks_data.id')
            ->join('truck_drivers', 'trip_summary.driver_id', '=', 'truck_drivers.id')
            ->join('shipment', 'trips.shift_id', '=', 'shipment.id')
            ->where('trip_summary.trip_status', 1)
            ->where('trip_summary.org_id', $org_id);

        if ($business_type === 'Carrier') {
            $query->where('trip_summary.vendor_id', $user_id);
        } else {
            $query->where('trip_summary.user_id', $user_id);
        }

        if (!empty($post['fromdate_search'])) {
            $query->whereDate('trip_summary.createdon', '>=', $post['fromdate_search']);
        }

        if (!empty($post['todate_search'])) {
            $query->whereDate('trip_summary.createdon', '<=', $post['todate_search']);
        }

        if (!empty($post['driver_search'])) {
            $driverIds = $this->getDriversBySearch($post['driver_search']);
            $query->whereIn('trip_summary.driver_id', $driverIds ?: [0]);
        }

        if (!empty($post['vehicle_search'])) {
            $vehicleIds = $this->getVehiclesBySearch($post['vehicle_search']);
            $query->whereIn('trip_summary.vehicle_id', $vehicleIds ?: [0]);
        }

        if (!empty($post['zone_search'])) {
            $tripIds = $this->getTripsByZoneSearch($post['zone_search']);
            $query->whereIn('trip_summary.trip_id', $tripIds ?: [0]);
        }

        if (!empty($post['shift_search'])) {
            $tripIds = $this->getTripsByShiftSearch([$post['shift_search']]);
            $query->whereIn('trip_summary.trip_id', $tripIds ?: [0]);
        }

        $query->orderBy('trip_summary.createdon', 'desc');

        if ($limit > 0) {
            $query->take($limit)->skip($offset);
        }

        return $query;
    }

    /**
     * Get trip details by trip IDs.
     */
    protected function getTripDetailsByTripIds(array $tripIds, $timezone)
    {
        if (empty($tripIds)) {
            return [];
        }

        $data = Shipment::select(
            'shipment.id',
            DB::raw("DATE_FORMAT(CONVERT_TZ(shipment.startdate, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as stime"),
            DB::raw("DATE_FORMAT(CONVERT_TZ(shipment.enddate, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as etime"),
            'trips.splace',
            'trips.eplace',
            'shipment.empshift_start',
            DB::raw("DATE_FORMAT(CONVERT_TZ(shipment.startdate, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as startdate"),
            DB::raw("DATE_FORMAT(CONVERT_TZ(shipment.enddate, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as enddate"),
            'shipment.shipmentid',
            'truck_drivers.name',
            'truck_drivers.contact_num',
            DB::raw("DATE_FORMAT(CONVERT_TZ(stop_status.createdon, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as createdon"),
            DB::raw("DATE_FORMAT(CONVERT_TZ(orders.delivery_datetime, 'UTC', '$timezone'), '%Y-%m-%d %H:%i:%s') as delivery_datetime")
        )
            ->join('trips', 'shipment.id', '=', 'trips.shift_id')
            ->leftJoin('orders', function ($join) {
                $join->on('orders.trip_id', '=', 'trips.id')
                     ->where('orders.status', '!=', 0);
            })
            ->leftJoin('trip_drivers', function ($join) {
                $join->on('trip_drivers.trip_id', '=', 'trips.id')
                     ->where('trip_drivers.status', 1);
            })
            ->leftJoin('truck_drivers', 'trip_drivers.driver_id', '=', 'truck_drivers.id')
            ->leftJoin('stop_status', function ($join) {
                $join->on('stop_status.trip_id', '=', 'trips.id')
                     ->where('stop_status.status_code', '2300')
                     ->where('stop_status.status', 1);
            })
            ->whereIn('trips.id', $tripIds)
            ->get()
            ->keyBy('id')
            ->toArray();

        return $data;
    }

    /**
     * Get ship units and packages by shift IDs.
     */
    protected function getShipUnitsAndPackagesByShiftIds(array $shiftIds)
    {
        if (empty($shiftIds)) {
            return [];
        }

        $data = ShiporderStop::select(
            'shiporder_stops.shipment_id',
            DB::raw('SUM(IF(shiporder_stops.ship_units = "0" OR shiporder_stops.ship_units IS NULL, 1, shiporder_stops.ship_units)) as ship_units'),
            DB::raw('SUM(IF(shiporder_stop_sequence.no_of_pkgs = "0" OR shiporder_stop_sequence.no_of_pkgs IS NULL, 0, shiporder_stop_sequence.no_of_pkgs)) as no_of_pkgs')
        )
            ->join('shiporder_stop_sequence', function ($join) {
                $join->on('shiporder_stop_sequence.shift_id', '=', 'shiporder_stops.shipment_id')
                     ->where('shiporder_stop_sequence.status', 1);
            })
            ->whereIn('shiporder_stops.shipment_id', $shiftIds)
            ->where('shiporder_stops.stoptype', 'P')
            ->groupBy('shiporder_stops.shipment_id')
            ->get()
            ->keyBy('shipment_id')
            ->toArray();

        return $data;
    }

    /**
     * Fetch employee-level reports data.
     */
    public function emplevel(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                throw new \Exception('User not authenticated');
            }
            $user_id = $user->id;
            $org_id = $user->org_id;
            $business_type = $user->business_type ?? 'Default';
            $timezone = $user->timezone ?? 'UTC';

            // Default date range: last 7 days to today
            $enddt = Carbon::today()->setTimezone($timezone)->toDateString();
            $startdt = Carbon::today()->subDays(7)->setTimezone($timezone)->toDateString();

            // Handle request data
            $post = [
                'fromdate_search' => $request->input('fromdate_search', $startdt),
                'todate_search' => $request->input('todate_search', $enddt),
                'vehicle_search' => $request->input('vehicle_search', ''),
                'shift_search' => $request->input('shift_search', []),
                'emp_search' => $request->input('emp_search', ''),
                'user_id' => $user_id,
                'zone_search' => $request->input('zone_search', ''),
                'type_search' => $request->input('type_search', ''),
            ];

            // Handle search submit
            if ($request->has('searchsubmit')) {
                if ($post['fromdate_search']) {
                    $post['fromdate_search'] = Carbon::parse($post['fromdate_search'])->setTimezone($timezone)->toDateString();
                }
                if ($post['todate_search']) {
                    $post['todate_search'] = Carbon::parse($post['todate_search'])->setTimezone($timezone)->toDateString();
                }
            }

            // Handle export submit (placeholder for dwnslareport)
            if ($request->has('searchexportsubmit')) {
                if ($post['fromdate_search']) {
                    $post['fromdate_search'] = Carbon::parse($post['fromdate_search'])->setTimezone($timezone)->toDateString();
                }
                if ($post['todate_search']) {
                    $post['todate_search'] = Carbon::parse($post['todate_search'])->setTimezone($timezone)->toDateString();
                }
                // Placeholder: Implement dwnslareport for export
                throw new \Exception('Export functionality not implemented');
            }

            // Pagination settings
            $perPage = $request->input('per_page', 10);
            $page = $request->input('page', 1);
            $offset = ($page - 1) * $perPage;

            // Get trip count
            $tripsCountResult = $this->getempreports($post, 0, 0);
            $tripsCount = count($tripsCountResult);

            // Get trip data
            $getTripsData = $this->getempreports($post, $perPage, $offset);
            $tripIds = [];
            $tripsdata = $getTripsData;

            // Get additional trip data
            $tripDataByTripId = $this->getTripDataByTripIds($tripIds, $timezone);

            // Get employee summary data
            $empdelaysummarydata = $this->getempsummary($post);

            // Fetch shifts for filters
            $shiftsQuery = Shipment::select('id', 'scity', 'dcity', 'shipmentid')
                ->where('status', 0)
                ->orderBy('startdate', 'desc');
            if ($business_type === 'Carrier') {
                $shiftsQuery->where('vendor_id', $user_id);
            } else {
                $shiftsQuery->where('user_id', $user_id);
            }
            $shifts = $shiftsQuery->get()->toArray();

            // Fetch zones for filters
            $zones = ShiftZone::select('id', 'zone_name', 'splace', 'eplace')
                ->where('status', 'Active')
                ->where('user_id', $user_id)
                ->where('org_id', $org_id)
                ->get()
                ->toArray();

            // Datatable headers (for frontend rendering)
            $datatable_headers = [
                'Trip',
                'Shipments/Shipment Stops',
                'Delayed',
            ];

            $column_visibility = array_map(function ($item) use ($datatable_headers) {
                return in_array($item, $datatable_headers);
            }, $datatable_headers);

            $datatable_header_sequence_index = [0];
            for ($i = 1; $i < count($datatable_headers) + 1; $i++) {
                $datatable_header_sequence_index[] = $i;
            }

            // Fetch datatable settings
            $settings = DB::table('datatable_settings')
                ->select('sequence_data', 'toggle_data')
                ->where('controller_name', 'CabReportsController')
                ->where('method_name', 'emplevel')
                ->where('org_id', $org_id)
                ->first();

            $datatable_header_sequence = [];
            $datatable_header_toggle = [];
            if ($settings) {
                if ($settings->sequence_data) {
                    $datatable_header_sequence = unserialize($settings->sequence_data);
                    $datatable_header_sequence_index = [0];
                    foreach ($datatable_header_sequence as $value) {
                        $index = array_search($value, $datatable_headers);
                        if ($index !== false) {
                            $datatable_header_sequence_index[] = $index + 1;
                        }
                    }
                    $datatable_header_sequence_index = array_filter($datatable_header_sequence_index);
                    $datatable_header_sequence_index = array_values($datatable_header_sequence_index);
                    foreach ($datatable_headers as $key => $header) {
                        $index = $key + 1;
                        if (!in_array($index, $datatable_header_sequence_index)) {
                            $datatable_header_sequence_index[] = $index;
                        }
                    }
                }
                if ($settings->toggle_data) {
                    $datatable_header_toggle = unserialize($settings->toggle_data);
                    $column_visibility = array_map(function ($item) use ($datatable_header_toggle) {
                        return in_array($item, $datatable_header_toggle);
                    }, $datatable_headers);
                }
            }

            // New datatable headers for report details
            $new_datatable_headers = [
                'ID',
                'Trip/Route',
                'Driver',
                'OnTime',
                'Stop',
                'Weight',
                'Volume',
                'D/P?',
                'OnTime',
                'TripStart',
                'TripEnd',
            ];

            $new_column_visibility = array_map(function ($item) use ($new_datatable_headers) {
                return in_array($item, $new_datatable_headers);
            }, $new_datatable_headers);

            $new_datatable_header_sequence_index = [0];
            for ($i = 1; $i < count($new_datatable_headers) + 1; $i++) {
                $new_datatable_header_sequence_index[] = $i;
            }

            // Fetch new datatable settings
            $new_settings = DB::table('datatable_settings')
                ->select('sequence_data', 'toggle_data')
                ->where('controller_name', 'CabReportsController')
                ->where('method_name', 'staticEmplevel')
                ->where('org_id', $org_id)
                ->first();

            $new_datatable_header_sequence = [];
            $new_datatable_header_toggle = [];
            if ($new_settings) {
                if ($new_settings->sequence_data) {
                    $new_datatable_header_sequence = unserialize($new_settings->sequence_data);
                    $new_datatable_header_sequence_index = [0];
                    foreach ($new_datatable_header_sequence as $value) {
                        $index = array_search($value, $new_datatable_headers);
                        if ($index !== false) {
                            $new_datatable_header_sequence_index[] = $index + 1;
                        }
                    }
                    $new_datatable_header_sequence_index = array_filter($new_datatable_header_sequence_index);
                    $new_datatable_header_sequence_index = array_values($new_datatable_header_sequence_index);
                    foreach ($new_datatable_headers as $key => $header) {
                        $index = $key + 1;
                        if (!in_array($index, $new_datatable_header_sequence_index)) {
                            $new_datatable_header_sequence_index[] = $index;
                        }
                    }
                }
                if ($new_settings->toggle_data) {
                    $new_datatable_header_toggle = unserialize($new_settings->toggle_data);
                    $new_column_visibility = array_map(function ($item) use ($new_datatable_header_toggle) {
                        return in_array($item, $new_datatable_header_toggle);
                    }, $new_datatable_headers);
                }
            }

            // Manual pagination for tripsdata
            $paginator = new LengthAwarePaginator(
                $tripsdata,
                $tripsCount,
                $perPage,
                $page,
                ['path' => $request->url(), 'query' => $request->query()]
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Employee-level reports retrieved successfully',
                'data' => [
                    'tripscount' => $tripsCount,
                    'tripsdata' => $paginator->items(),
                    'tripDataByTripId' => $tripDataByTripId,
                    'empdelaysummarydata' => $empdelaysummarydata,
                    'shifts' => $shifts,
                    'zones' => $zones,
                    'post' => $post,
                    'datatable_headers' => $datatable_headers,
                    'column_visibility' => $column_visibility,
                    'datatable_header_sequence' => $datatable_header_sequence,
                    'datatable_header_toggle' => $datatable_header_toggle,
                    'datatable_header_sequence_index' => $datatable_header_sequence_index,
                    'new_datatable_headers' => $new_datatable_headers,
                    'new_column_visibility' => $new_column_visibility,
                    'new_datatable_header_sequence' => $new_datatable_header_sequence,
                    'new_datatable_header_toggle' => $new_datatable_header_toggle,
                    'new_datatable_header_sequence_index' => $new_datatable_header_sequence_index,
                    'pagination' => [
                        'total' => $paginator->total(),
                        'per_page' => $paginator->perPage(),
                        'current_page' => $paginator->currentPage(),
                        'last_page' => $paginator->lastPage(),
                        'from' => $paginator->firstItem(),
                        'to' => $paginator->lastItem(),
                    ],
                ],
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (QueryException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Database error occurred',
                'errors' => ['database' => $e->getMessage()],
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'An unexpected error occurred',
                'errors' => ['general' => $e->getMessage()],
            ], 500);
        }
    }

    /**
         * Get employee reports data with filtering and pagination.
         */
        public function getempreports($post, $limit, $offset)
        {
            try {
                // $empid = "1";
                $where = ["tr.trip_status = 1"];
                $user_id = $post['user_id'] ?? Auth::user()->id;
                $business_type = Auth::user()->business_type ?? 'Default';
                $timezone = Auth::user()->timezone ?? 'UTC';
    
                if ($business_type === "Carrier") {
                    $uid = Auth::user()->id;
                    $where[] = "tr.vendor_id = '$uid'";
                } else {
                    $where[] = "e.user_id = '$user_id'";
                }
    
                if (!empty($post['fromdate_search'])) {
                    $where[] = "DATE(tr.createdon) >= '" . DB::connection()->getPdo()->quote($post['fromdate_search']) . "'";
                }
    
                if (!empty($post['todate_search'])) {
                    $where[] = "DATE(tr.createdon) <= '" . DB::connection()->getPdo()->quote($post['todate_search']) . "'";
                }
    
                if (!empty($post['emp_search'])) {
                    $enam = trim($post['emp_search']);
                    $where[] = "e.order_id LIKE '%" . DB::connection()->getPdo()->quote($enam) . "%'";
                }
    
                if (!empty($post['vehicle_search'])) {
                    $getlocid = $this->getVehiclesBySearch($post['vehicle_search']);
                    $dd = !empty($getlocid) ? implode(",", array_column($getlocid, 'id')) : '0';
                    $where[] = "tr.vehicle_id IN ($dd)";
                }
    
                if (!empty($post['zone_search'])) {
                    $getlocid = $this->getTripsByZoneSearch($post['zone_search']);
                    $zs = !empty($getlocid) ? implode(",", array_column($getlocid, 'id')) : '0';
                    $where[] = "tr.trip_id IN ($zs)";
                }
    
                if (!empty($post['shift_search'])) {
                    $post['shift_search'] = array_filter($post['shift_search']);
                    if (!empty($post['shift_search'])) {
                        $getlocid = $this->getTripsByShiftSearch($post['shift_search']);
                        $dd = !empty($getlocid) ? implode(",", array_column($getlocid, 'id')) : '0';
                        $where[] = "tr.trip_id IN ($dd)";
                    }
                }
    
                $whereClause = implode(" AND ", $where);
    
                $query = "SELECT e.assoc_id, tr.trip_id, 
                    CONVERT_TZ(tr.start_time, 'UTC', '$timezone') as start_time, 
                    CONVERT_TZ(tr.end_time, 'UTC', '$timezone') as end_time, 
                    us.check_in, us.status, e.shipment_weight, e.shipment_volume, us.driver_late, 
                    IFNULL((SELECT (IFNULL(us.driver_late, 0) - IFNULL(t2.emp_late, 0)) 
                            FROM trip_employee t2 
                            WHERE t2.id = us.id - 1 AND us.trip_id = t2.trip_id), 0) as driverlate, 
                    us.emp_late, e.name as ename, e.phone, e.ship_type, dr.name, dr.contact_num 
                    FROM trip_summary as tr, trip_employee as us, shiporder_stop_sequence as e, truck_drivers as dr 
                    WHERE $whereClause 
                    AND tr.trip_id = us.trip_id 
                    AND us.employee_id = e.id 
                    AND us.status = 1 
                    AND dr.id = tr.driver_id 
                    ORDER BY tr.trip_id DESC";
    
                if ($limit > 0) {
                    $query .= " LIMIT $offset, $limit";
                }
    
                $results = DB::select($query);
                return $results;
            } catch (QueryException $e) {
                throw new \Exception('Database error in getempreports: ' . $e->getMessage());
            } catch (\Exception $e) {
                throw new \Exception('Error in getempreports: ' . $e->getMessage());
            }
        }
    
        /**
         * Get employee summary data.
         */
        public function getempsummary($post)
        {
            try {
                $empid = ""; // Empty as in original
                $chkdt = [];
                $where = ["tr.trip_status = 1"];
                $user_id = $post['user_id'] ?? Auth::user()->id;
                $business_type = Auth::user()->business_type ?? 'Default';
    
                if ($business_type === "Carrier") {
                    $uid = Auth::user()->id;
                    $where[] = "tr.vendor_id = '$uid'";
                } else {
                    $where[] = "e.user_id = '$user_id'";
                }
    
                if (!empty($post['fromdate_search'])) {
                    $where[] = "DATE(tr.createdon) >= '" . DB::connection()->getPdo()->quote($post['fromdate_search']) . "'";
                    $chkdt[] = "DATE(stime) >= '" . DB::connection()->getPdo()->quote($post['fromdate_search']) . "'";
                }
    
                if (!empty($post['todate_search'])) {
                    $where[] = "DATE(tr.createdon) <= '" . DB::connection()->getPdo()->quote($post['todate_search']) . "'";
                    $chkdt[] = "DATE(stime) <= '" . DB::connection()->getPdo()->quote($post['todate_search']) . "'";
                }
    
                if (!empty($post['emp_search'])) {
                    $enam = trim($post['emp_search']);
                    $where[] = "e.order_id LIKE '%" . DB::connection()->getPdo()->quote($enam) . "%'";
                }
    
                if (!empty($post['vehicle_search'])) {
                    $getlocid = $this->getVehiclesBySearch($post['vehicle_search']);
                    $dd = !empty($getlocid) ? implode(",", array_column($getlocid, 'id')) : '0';
                    $where[] = "tr.vehicle_id IN ($dd)";
                }
    
                if (!empty($post['zone_search'])) {
                    $getlocid = $this->getTripsByZoneSearch($post['zone_search']);
                    $zs = !empty($getlocid) ? implode(",", array_column($getlocid, 'id')) : '0';
                    $where[] = "tr.trip_id IN ($zs)";
                }
    
                if (!empty($post['shift_search'])) {
                    $post['shift_search'] = array_filter($post['shift_search']);
                    if (!empty($post['shift_search'])) {
                        $getlocid = $this->getTripsByShiftSearch($post['shift_search']);
                        $dd = !empty($getlocid) ? implode(",", array_column($getlocid, 'id')) : '0';
                        $where[] = "tr.trip_id IN ($dd)";
                    }
                }
    
                $whereClause = implode(" AND ", $where);
    
                $query = "SELECT tr.trip_id, 
                    COUNT(us.employee_id) as attendcount, 
                    SUM(IF(us.emp_late > 0, 1, 0)) AS delaycnt, 
                    e.id, e.name, e.phone, e.pickup, e.drop, e.shipment_weight, e.shipment_volume, e.ship_type 
                    FROM trip_summary as tr, trip_employee as us, shiporder_stop_sequence as e 
                    WHERE $whereClause 
                    AND tr.trip_id = us.trip_id 
                    AND us.employee_id = e.id 
                    AND us.status = 1 $empid 
                    GROUP BY us.employee_id";
    
                $results = DB::select($query);
                return $results;
            } catch (QueryException $e) {
                throw new \Exception('Database error in getempsummary: ' . $e->getMessage());
            } catch (\Exception $e) {
                throw new \Exception('Error in getempsummary: ' . $e->getMessage());
            }
        }
}