<?php

namespace App\Http\Controllers;

use App\Models\Charge;
use App\Models\ChargeCode;
use App\Models\Order;
use App\Models\Revenue;
use App\Models\Shipment;
use App\Models\User;
use App\Services\MultipleAconEdiService;
use App\Services\SgMyJfrService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;
use Ramsey\Uuid\Uuid;

class SendMultipleAconEdiController extends Controller
{
    protected $sgMyJfrService;
    protected $multipleAconEdiService;

    public function __construct(SgMyJfrService $sgMyJfrService, MultipleAconEdiService $multipleAconEdiService)
    {
        $this->sgMyJfrService = $sgMyJfrService;
        $this->multipleAconEdiService = $multipleAconEdiService;
    }

    public function fileTransferForSelectedOrders(Request $request)
    {
        ini_set('max_execution_time', 300);

        try {
            $post = $request->all();
            $orderIds = $post['order_ids'] ?? [];
            $result = [];
            $ids = [];
            $notripIds = [];
            $invoicedData = [];
            $tobeinvoiced = [];
            $sendOrders = [];
            $norevenues = [];
            $invoicedRevIds = [];
            $legRowIds = [];

            if (empty($orderIds)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No order IDs provided',
                    'data' => [],
                ], 422);
            }

            foreach ($orderIds as $oids) {
                $status = 0;
                if ($oids['ids'] > 0) {
                    $order = Order::where('id', $oids['ids'])
                        ->select('id', 'order_id', 'shift_id', 'trip_id')
                        ->first();

                    if ($order) {
                        $tripId = $order->trip_id;
                        $shiftId = $order->shift_id;

                        if ($tripId > 0) {
                            $status = 1;
                        } else {
                            if ($shiftId > 0) {
                                $legRowIds = Shipment::where('shift_leg_id', $shiftId)
                                    ->where('status', '1')
                                    ->pluck('id')
                                    ->toArray();

                                if (!empty($legRowIds)) {
                                    $status = $this->sgMyJfrService->checkActiveTripOrNot($legRowIds);
                                }
                            }
                        }
                    }

                    if ($status == 1) {
                        $ids[] = $oids['ids'];
                    } else {
                        $notripIds[] = $oids['ids'];
                    }
                }
            }

            $response = $this->multipleAconEdiService->checkMultipleOrdersInternalBu($ids);

            if (!empty($response)) {
                $allOrdIds = [];
                foreach ($response as $row) {
                    $allOrdIds[] = $row['order_id'];
                    $invoiceStatus = $row['invoice_status'];

                    if ($invoiceStatus == '1') {
                        $invoicedRevIds[] = $row['rev_id'];
                        $invoicedData[] = $row['order_id'];
                    } elseif ($invoiceStatus == '0') {
                        $tobeinvoiced[] = $row['order_id'];
                    } elseif ($invoiceStatus > 2) {
                        $sendOrders[] = $row['order_id'];
                    }
                }

                $norevenues = !empty($allOrdIds) ? array_diff($ids, $allOrdIds) : $ids;
            } else {
                $norevenues = $ids;
            }

            if (!empty($invoicedRevIds)) {
                $this->multipleFileTransfer($invoicedRevIds);
            }

            $invoicedOrders = !empty($invoicedData) ? $this->checkFileSentOrNot($invoicedData) : [];
            $tobeinvoiceOrders = !empty($tobeinvoiced) ? $this->getBookingId($tobeinvoiced, 'Order status is not in "Ready To Invoice"') : [];
            $sendOrders = !empty($sendOrders) ? $this->getBookingId($sendOrders, 'File Transfer is already Done') : [];
            $norevOrders = !empty($norevenues) ? $this->getBookingId($norevenues, 'Internal BU not found for these orders') : [];
            $notripOrders = !empty($notripIds) ? $this->getBookingId($notripIds, 'Driver Acceptance not done') : [];

            $result = array_merge(
                array_map(fn($ord) => ['id' => $ord['bookingid'], 'status' => $ord['status']], $sendOrders),
                array_map(fn($ord) => ['id' => $ord['bookingid'], 'status' => $ord['status']], $tobeinvoiceOrders),
                array_map(fn($ord) => ['id' => $ord['bookingid'], 'status' => $ord['status']], $invoicedOrders),
                array_map(fn($ord) => ['id' => $ord['bookingid'], 'status' => $ord['status']], $norevOrders),
                array_map(fn($ord) => ['id' => $ord['bookingid'], 'status' => $ord['status']], $notripOrders)
            );

            return response()->json([
                'status' => 'success',
                'message' => 'File transfer processed successfully',
                'data' => $result,
            ], 200);
        } catch (\Exception $e) {
            Log::error('FileTransferForSelectedOrders Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error processing file transfer',
                'data' => [],
            ], 500);
        }
    }

    protected function multipleFileTransfer(array $revIds)
    {
        $buData = [];
        $revIdsProcessed = [];
        $orderIds = [];
        $curDt = now();
        $userId = Auth::user()->id ?? 0;
        $orgId = Auth::user()->org_id ?? 0;
        $timezone = Auth::user()->timezone['timezone'] ?? config('app.timezone', 'UTC');
        $countryName = Auth::user()->timezone['country_name'] ?? '';
        $countryCode = Auth::user()->timezone['country'] ?? '';
        $timestamp = $curDt->format('Y-m-d\TH:i:s\Z');
        $invoiceDate = $curDt->format('Y-m-d');
        $filePeriod = $curDt->format('Ym');

        $userName = User::where('id', $userId)->value('name') ?? '';
        $countryCodePrefix = substr($orgId, 0, 2);
        $where = "party_master.status = '1' AND party_master.acon_debitor_code != '' AND revenues.amount > 0 AND revenues.invoice_status = '1' AND revenues.status = '1'";

        $buRevenues = $this->multipleAconEdiService->getBuDetailsForRevenue($revIds, $where, $orgId);

        foreach ($buRevenues as $buRev) {
            $orderId = $buRev['order_id'];
            $orderWeight = 0;
            $ordernum = '';
            $createdon = '';

            if ($orderId > 0) {
                $orderIds[] = $orderId;
                $orderDetails = Order::where('id', $orderId)
                    ->where('status', '!=', '0')
                    ->select('order_id', 'weight', 'createdon')
                    ->first();

                if ($orderDetails) {
                    $ordernum = $orderDetails->order_id;
                    $createdon = $orderDetails->createdon;
                    $orderWeight = $orderDetails->weight;
                }

                $currency = $buRev['currency'];
                $type = $buRev['type'];
                $debtorJfr = $buRev['debtor_jfr'];
                $buJfr = $buRev['bu_jfr'];
                $aconCode = $buRev['acon_debitor_code'];
                $revenueId = $buRev['id'];

                $buCharges = [];
                $charges = $this->getCharges($revenueId);

                foreach ($charges as $charge) {
                    $buCharges[] = [
                        'amount' => $charge->amount,
                        'debtor_jfr' => $debtorJfr,
                        'local_amount' => $charge->local_amount,
                        'local_currency' => $charge->local_currency,
                    ];
                }

                if (!empty($buCharges)) {
                    $revIdsProcessed[] = $revenueId;
                    $buData[] = [
                        'acon_country' => '',
                        'acon_code' => $aconCode,
                        'issuedate' => $filePeriod,
                        'currency' => $currency,
                        'invoice_no' => '',
                        'charges' => $buCharges,
                        'invoice_date' => $invoiceDate,
                        'debtor_jfr' => $debtorJfr,
                        'bu_jfr' => $buJfr,
                        'ordernum' => $ordernum,
                        'type' => $type,
                    ];
                }
            }
        }

        if (!empty($buData)) {
            $uuid = Uuid::uuid4()->toString();
            $headerData = [
                'uuid' => $uuid,
                'routing_country' => $countryCodePrefix,
                'routing_company' => '',
            ];

            if (!empty($revIdsProcessed)) {
                $updateData = [
                    'invoice_number' => '',
                    'invoice_date' => $invoiceDate,
                    'invoice_creation_date' => $curDt,
                    'invoice_receivdon_date' => $curDt,
                    'invoice_status' => '3',
                ];
                $this->multipleAconEdiService->updateMultipleRevs($revIdsProcessed, $updateData);

                $shipmentPayLoad = array_map(fn($budata) => $this->multipleFileTransferXml($budata, $filePeriod), $buData);
                $this->publishMultipleFileTransferXml($shipmentPayLoad, $headerData);
            }
        }

        $this->checkAccrualToSend($orderIds);

        return '1';
    }

    protected function checkAccrualToSend(array $ids)
    {
        $curDt = now();
        $orgId = Auth::user()->org_id ?? 0;
        $beValue = Auth::user()->be_value ?? 0;
        $countryName = Auth::user()->timezone['country_name'] ?? '';
        $countryCode = Auth::user()->timezone['country'] ?? '';
        $sessionCurrency = Auth::user()->timezone['currency'] ?? config('app.currency', 'USD');
        $timezone = Auth::user()->timezone['timezone'] ?? config('app.timezone', 'UTC');
        $timestamp = $curDt->format('Y-m-d\TH:i:s\Z');
        $countryCodePrefix = substr($orgId, 0, 2);
        $invoiceDate = $curDt->format('Y-m-d');
        $filePeriod = $curDt->format('Ym');
        $userId = Auth::user()->id ?? 0;
        $userName = User::where('id', $userId)->value('name') ?? '';
        $logicalSender = 'Shipmentx';

        if ($orgId == 'PLKN') {
            $sessionCurrency = 'PLN';
        }

        if (!empty($ids)) {
            $revenueCheck = $this->multipleAconEdiService->checkRevenueExistsOrNot($ids);
            $norevOrders = $revenueCheck['norev_orders'] ?? [];
            $revOrders = $revenueCheck['rev_orders'] ?? [];

            if (!empty($norevOrders)) {
                $invoicedIds = $this->getDebitorCodeMultipleRevenue($norevOrders);

                if (!empty($invoicedIds)) {
                    $where = "party_master.status = '1' AND party_master.acon_debitor_code != '' AND revenues.type = '1' AND revenues.amount > 0 AND revenues.invoice_status = '1' AND revenues.status = '1'";
                    $costs = $this->multipleAconEdiService->getBuDetailsForRevenue($invoicedIds, $where, $orgId);

                    $dataArr = [];
                    $costIds = [];

                    foreach ($costs as $cost) {
                        $orderId = $cost['order_id'];
                        $costRowId = $cost['id'];
                        $costIds[] = $costRowId;
                        $carrierCode = $cost['recipient_code'];
                        $carrierAconCode = $cost['acon_debitor_code'];
                        $debtorJfr = $cost['debtor_jfr'];
                        $currency = $cost['currency'];
                        $invoiceNumber = $cost['invoice_number'];

                        $charges = [];
                        $chargeRecords = $this->getCharges($costRowId);

                        foreach ($chargeRecords as $charge) {
                            $chargeCurrency = $charge->currency;
                            if ($chargeCurrency != $sessionCurrency) {
                                $charges[] = [
                                    'amount' => round($charge->amount, 2),
                                    'currency' => $chargeCurrency,
                                    'local_amount' => round($charge->local_amount, 2),
                                    'local_currency' => $charge->local_currency,
                                ];
                            } else {
                                $charges[] = [
                                    'amount' => round($charge->amount, 2),
                                    'currency' => $chargeCurrency,
                                    'local_amount' => 0,
                                    'local_currency' => '',
                                ];
                            }
                        }

                        $orderWeight = Order::where('id', $orderId)->value('weight') ?? 0;

                        if ($debtorJfr && $carrierAconCode) {
                            $updateData = [
                                'debtor_jfr' => $debtorJfr,
                                'invoice_number' => $debtorJfr,
                                'invoice_date' => $invoiceDate,
                                'invoice_creation_date' => $curDt,
                                'invoice_receivdon_date' => $curDt,
                                'invoice_status' => '2',
                            ];
                            $this->multipleAconEdiService->updateMultipleRevs([$costRowId], $updateData);

                            $dataArr[] = [
                                'order_id' => $orderId,
                                'carrier_aconcode' => $carrierAconCode,
                                'order_weight' => $orderWeight,
                                'invoice_date' => $invoiceDate,
                                'currency' => $currency,
                                'charges' => $charges,
                                'fileperiod' => $filePeriod,
                                'user_name' => $userName,
                                'acon_country' => '',
                                'debtor_jfr' => $debtorJfr,
                            ];
                        }
                    }

                    if (!empty($dataArr)) {
                        $uuid = Uuid::uuid4()->toString();
                        $headerData = [
                            'uuid' => $uuid,
                            'logical_sender' => $logicalSender,
                            'country_code' => $countryCodePrefix,
                            'acon_country' => '',
                            'branch_code' => $beValue,
                            'routing_department' => '',
                        ];
                        $accrualXml = [$this->multipleAccrualXml($dataArr)];
                        $this->sendMultiplePublishAccrualXml($accrualXml, $headerData);
                    }
                }
            }
        }

        return '1';
    }

    protected function sendMultiplePublishAccrualXml(array $accrualXml, array $headerData)
    {
        if (!empty($accrualXml)) {
            $connection = new AMQPStreamConnection(
                config('amqp.url'),
                config('amqp.port'),
                config('amqp.username'),
                config('amqp.password'),
                config('amqp.vhost'),
                ['verify_peer' => false, 'verify_peer_name' => false]
            );
            $channel = $connection->channel();

            $headers = new AMQPTable([
                'uuid' => $headerData['uuid'],
                'KN_MS_PhysSenderID' => config('amqp.sender_id'),
                'KN_MS_LogSenderID' => $headerData['logical_sender'],
                'KNESB_Routing_SenderId' => config('amqp.username'),
                'KNESB_Routing_SenderInstance' => 'TEST_ACCEPTANCE',
                'KNESB_Routing_ReceiverId' => config('amqp.receiver_id'),
                'KNESB_Routing_ReceiverInstance' => config('amqp.receiver_instance'),
                'KNESB_Routing_Country' => $headerData['country_code'],
                'KNESB_Routing_Company' => $headerData['acon_country'],
                'KNESB_Routing_Branch' => $headerData['branch_code'],
                'KNESB_Routing_Department' => 'CTP',
            ]);
            $headers->set('short', -1024, AMQPTable::T_INT_SHORT);

            foreach ($accrualXml as $xml) {
                $message = new AMQPMessage($xml, [
                    'content_type' => 'Application/xml',
                    'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT,
                ]);
                $message->set('application_headers', $headers);
                $channel->basic_publish($message, config('amqp.accrual_url'));
            }

            $channel->close();
            $connection->close();
        }

        return '1';
    }

    protected function multipleAccrualXml(array $dataArr): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>';
        $xml .= '<AccruedExpenses xmlns="http://services.kn.com/xsd/acon/fsl/AccuredExpenses/v1">';
        $i = 1;

        foreach ($dataArr as $data) {
            foreach ($data['charges'] as $charge) {
                $xml .= '<AccruedExpense>';
                $xml .= '<CompanyCode>' . htmlspecialchars($data['acon_country']) . '</CompanyCode>';
                $xml .= '<CallingApplication>ETRUCACC</CallingApplication>';
                $xml .= '<TxDate>' . htmlspecialchars($data['invoice_date']) . '</TxDate>';
                $xml .= '<CreditorCode>' . htmlspecialchars($data['carrier_aconcode']) . '</CreditorCode>';
                $xml .= '<JobFileNo>' . htmlspecialchars($data['debtor_jfr']) . '</JobFileNo>';
                $xml .= '<FilePeriod>' . htmlspecialchars($data['fileperiod']) . '</FilePeriod>';
                $xml .= '<ChargeCategory>045</ChargeCategory>';

                if ($charge['local_amount'] > 0) {
                    $xml .= '<CurrencyCode>' . htmlspecialchars($charge['local_currency']) . '</CurrencyCode>';
                    $xml .= '<LCDecimalPlace>2</LCDecimalPlace>';
                    $xml .= '<AccrualLCAmount>' . htmlspecialchars($charge['local_amount']) . '</AccrualLCAmount>';
                } else {
                    $xml .= '<CurrencyCode>' . htmlspecialchars($charge['currency']) . '</CurrencyCode>';
                    $xml .= '<LCDecimalPlace>2</LCDecimalPlace>';
                    $xml .= '<AccrualLCAmount>' . htmlspecialchars($charge['amount']) . '</AccrualLCAmount>';
                }

                $xml .= '<Weight>' . htmlspecialchars($data['order_weight']) . '</Weight>';
                $xml .= '<SequenceNo>' . $i . '</SequenceNo>';
                $xml .= '<InterfaceStatus>A</InterfaceStatus>';
                $xml .= '<CostType>R</CostType>';

                if ($charge['local_amount'] > 0) {
                    $xml .= '<ForeignCurrencyCode>' . htmlspecialchars($charge['currency']) . '</ForeignCurrencyCode>';
                    $xml .= '<AccrualFCAmount>' . htmlspecialchars($charge['amount']) . '</AccrualFCAmount>';
                }

                $xml .= '<LastUpdatedUser>' . htmlspecialchars($data['user_name']) . '</LastUpdatedUser>';
                $xml .= '</AccruedExpense>';
                $i++;
            }
        }

        $xml .= '</AccruedExpenses>';
        return $xml;
    }

    protected function publishMultipleFileTransferXml(array $fileXml, array $headerData)
    {
        if (!empty($fileXml)) {
            $connection = new AMQPStreamConnection(
                config('amqp.url'),
                config('amqp.port'),
                config('amqp.username'),
                config('amqp.password'),
                config('amqp.vhost'),
                ['verify_peer' => false, 'verify_peer_name' => false]
            );
            $channel = $connection->channel();

            $headers = new AMQPTable([
                'uuid' => $headerData['uuid'],
                'KNESB_Routing_SenderId' => config('amqp.sender_id'),
                'KNESB_Routing_SenderInstance' => 'PROD',
                'KNESB_Routing_Country' => $headerData['routing_country'],
                'KNESB_Routing_Company' => $headerData['routing_company'],
            ]);
            $headers->set('short', -1024, AMQPTable::T_INT_SHORT);

            foreach ($fileXml as $xml) {
                $message = new AMQPMessage($xml, [
                    'content_type' => 'Application/xml',
                    'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT,
                ]);
                $message->set('application_headers', $headers);
                $channel->basic_publish($message, config('amqp.file_publish_url'));
            }

            $channel->close();
            $connection->close();
        }

        return '1';
    }

    protected function multipleFileTransferXml(array $revenue, string $yearMonth): string
    {
        $orgId = Auth::user()->org_id ?? 0;
        $transferLcAmount = 0;
        $type = $revenue['type'] ?? '0';
        $date = now()->format('Y-m-d');
        $localCurrency = '';

        foreach ($revenue['charges'] as $charge) {
            $localCurrency = $charge['local_currency'];
            $transferLcAmount += $charge['local_amount'];
        }

        $xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>';
        $xml .= '<FileTransfers xmlns="http://services.kn.com/xsd/acon/fsl/FileTransfers/v1">';
        $xml .= '<FileTransfer>';
        $xml .= '<CompanyCodeFrom>' . htmlspecialchars($revenue['acon_country']) . '</CompanyCodeFrom>';
        $xml .= '<FilePeriodFrom>' . htmlspecialchars($yearMonth) . '</FilePeriodFrom>';

        $buJfr = $revenue['bu_jfr'];
        $debtorJfr = $revenue['debtor_jfr'];

        if ($orgId == 'RUKN') {
            $buJfr = substr($buJfr, 2);
            $xml .= '<JobFileNoFrom>' . htmlspecialchars($buJfr) . '</JobFileNoFrom>';
        } else {
            if ($type == '1') {
                $buJfr = substr($buJfr, 2);
                $xml .= '<JobFileNoFrom>' . htmlspecialchars($buJfr) . '</JobFileNoFrom>';
            } else {
                $xml .= '<JobFileNoFrom>' . htmlspecialchars($debtorJfr) . '</JobFileNoFrom>';
            }
        }

        $xml .= '<BillingCompletedInFrom>Y</BillingCompletedInFrom>';
        $xml .= '<CompanyCodeTo>' . htmlspecialchars($revenue['acon_country']) . '</CompanyCodeTo>';

        if ($orgId == 'RUKN') {
            $xml .= '<FilePeriodTo>' . htmlspecialchars($yearMonth) . '</FilePeriodTo>';
            $xml .= '<JobFileNoTo>' . htmlspecialchars($debtorJfr) . '</JobFileNoTo>';
        } else {
            if ($type == '1') {
                $xml .= '<FilePeriodTo>' . htmlspecialchars($yearMonth) . '</FilePeriodTo>';
                $xml .= '<JobFileNoTo>' . htmlspecialchars($debtorJfr) . '</JobFileNoTo>';
            } else {
                $trackingNo = '';
                $profitCentre = '';
                if (strlen($buJfr) == 15 && strpos($buJfr, '-') !== false) {
                    [$trackingNo, $profitCentre] = explode('-', $buJfr);
                    $xml .= '<TrackingNoTo>' . htmlspecialchars($trackingNo) . '</TrackingNoTo>';
                    $xml .= '<ProfitCentreTo>' . htmlspecialchars($profitCentre) . '</ProfitCentreTo>';
                    $xml .= '<FilePeriodTo>' . htmlspecialchars($yearMonth) . '</FilePeriodTo>';
                } elseif (strlen($buJfr) == 16) {
                    $buJfr = substr($buJfr, 2);
                    $xml .= '<FilePeriodTo>' . htmlspecialchars($yearMonth) . '</FilePeriodTo>';
                    $xml .= '<JobFileNoTo>' . htmlspecialchars($buJfr) . '</JobFileNoTo>';
                }
            }
        }

        $xml .= '<CallingApplication>ETRUCTXF</CallingApplication>';
        $xml .= '<TransferDate>' . htmlspecialchars($date) . '</TransferDate>';
        $xml .= '<CreditorCode>' . htmlspecialchars($revenue['acon_code']) . '</CreditorCode>';
        $xml .= '<ChargeCode>045</ChargeCode>';
        $xml .= '<ChargeCategory>045</ChargeCategory>';
        $xml .= '<TransferType>I</TransferType>';
        $xml .= '<CurrencyCode>' . htmlspecialchars($localCurrency) . '</CurrencyCode>';
        $xml .= '<LCDecimalPlace>2</LCDecimalPlace>';
        $xml .= '<TransferLCAmount>' . htmlspecialchars(round($transferLcAmount, 2)) . '</TransferLCAmount>';
        $xml .= '<ItemType>I</ItemType>';
        $xml .= '<ItemNo>' . htmlspecialchars($revenue['ordernum']) . '</ItemNo>';
        $xml .= '<SequenceNo>1</SequenceNo>';
        $xml .= '</FileTransfer>';
        $xml .= '</FileTransfers>';

        return $xml;
    }

    protected function checkFileSentOrNot(array $invoicedData): array
    {
        $invoicedOrders = [];

        $results = Order::whereIn('orders.id', $invoicedData)
            ->join('revenues', 'orders.id', '=', 'revenues.order_id')
            ->where('orders.status', '!=', '0')
            ->where('revenues.recipient_role', 'LIKE', 'Internal BU')
            ->where('revenues.amount', '>', 0)
            ->where('revenues.status', '1')
            ->select('orders.id', 'orders.order_id', 'revenues.invoice_status')
            ->get();

        foreach ($results as $res) {
            $invoiceStatus = $res->invoice_status;
            if ($invoiceStatus < '3') {
                $invoicedOrders[] = [
                    'bookingid' => $res->order_id,
                    'status' => 'Something Went Wrong',
                ];
            } else {
                $accrualStatus = '';
                $status = 'File Transfer Done Successfully';
                $orderRowId = $res->id;

                $chk = Revenue::where([
                    'type' => '1',
                    'recipient_role' => 'Carrier',
                    'order_id' => $orderRowId,
                    'status' => '1',
                ])->select('id', 'invoice_status')
                    ->first();

                if ($chk) {
                    $accrualStatus = $chk->invoice_status == '2'
                        ? "<p style='font-weight:bold;color:#099e59eb;'> (Accrual Sent)</p>"
                        : "<p style='font-weight:bold;color:#b71209d6;'> (Accrual Not Sent)</p>";
                } else {
                    $accrualStatus = "<b>(No Accrual)</b>";
                }

                $invoicedOrders[] = [
                    'bookingid' => $res->order_id,
                    'status' => $status . $accrualStatus,
                ];
            }
        }

        return $invoicedOrders;
    }

    protected function getBookingId(array $ids, string $status): array
    {
        $response = [];

        if (!empty($ids)) {
            $orders = Order::whereIn('id', $ids)
                ->where('status', '!=', '0')
                ->select('order_id')
                ->get();

            foreach ($orders as $res) {
                $response[] = [
                    'bookingid' => $res->order_id,
                    'status' => $status,
                ];
            }
        }

        return $response;
    }

    protected function getCharges(int $revenueId)
    {
        return Charge::where('revenue_id', $revenueId)
            ->where('charges.status', '1')
            ->join('charge_codes', 'charges.charge_code', '=', 'charge_codes.id')
            ->select('charges.*', 'charge_codes.charge_code as chargecode')
            ->get();
    }

    protected function getDebitorCodeMultipleRevenue(array $orderIds): array
    {
        // Placeholder: Implement logic to fetch debitor codes for multiple revenues
        // This function is not provided in the original code, so returning order IDs as a placeholder
        return $orderIds;
    }
}
