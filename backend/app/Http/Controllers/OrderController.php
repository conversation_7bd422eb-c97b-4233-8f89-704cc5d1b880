<?php

namespace App\Http\Controllers;

use App\Models\CountryMaster;
use App\Models\InnerCargo;
use App\Models\OrderType;
use App\Models\SxPartyMembers;
use App\Models\ResolutionMaster;
use App\Models\StoppageMaster;
use App\Models\VasMaster;
use App\Models\SxPartyTypes;
use App\Models\ChargeCode;
use App\Models\VatCategory;
use App\Models\CostCenter;
use App\Models\TransportMode;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\User;
use App\Models\ShipunitType;
use App\Models\OrderCargodetail;
use App\Models\CargoDetail;
use App\Models\OrderParty;
use App\Models\OrderReference;
use App\Models\Item;
use App\Models\Revenue;
use App\Models\ReferenceMaster;
use App\Models\OrderpartyAddress;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

use App\Services\OrderList\AdvancedSearch;
use App\Services\OrderList\ExcelUpload;
use App\Services\OrderList\Properties;
use App\Services\OrderList\Search;
use App\Services\OrderList\GetArgumentsResolver;
use App\Services\OrderList\Pagination;
use App\Services\OrderList\OrderProcessor;
use App\Services\OrderList\OrderQueryService;
use App\Services\TripCreateFromOrders;
use App\Services\RateManagement;

use Carbon\Carbon;
use Exception;


class OrderController extends Controller
{

    protected $orderProcessor;
    protected $tripCreateFromOrders;
    protected $rateManagement;

    public function __construct(OrderProcessor $orderProcessor, TripCreateFromOrders $tripCreateFromOrders, RateManagement $rateManagement)
    {
        $this->orderProcessor = $orderProcessor;
        $this->tripCreateFromOrders = $tripCreateFromOrders;
        $this->rateManagement = $rateManagement;
    }

    public function index(Request $request, $id = null)
    {
        // if (Auth::user()->business_type === 'Carrier') {
        //     return redirect()->route('logout');
        // }

        return $this->orderslist($request, $id);
    }

    public function orderslist(Request $request, $id = null)
    {
        $user = Auth::user();
        $sessionData = [
            'user_id' => $user->id ?? $request->input('user_id', 0),
            'cust_id' => $user->cust_id ?? $request->input('cust_id', 0),
            'country_user_ids' => $user->country_user_ids ?? $request->input('country_user_ids', []),
            'be_value' => $user->be_value ?? $request->input('be_value', 0),
            'org_id' => $user->org_id ?? $request->input('org_id', 0),
            'user_role_id' => $user->user_role_id ?? $request->input('user_role_id', 0),
            'sub_cust' => $user->sub_cust ?? $request->input('sub_cust', []),
            'curtz' => $user->usr_tzone['timezone'] ?? 'UTC',
        ];
        $orgId = $user->org_id ?? $request->input('org_id', 0);

        $data = [
            'postData' => $request->all(),
        ];

        $getArguments = [];
        $properties = new Properties();
        $order = [];
        $excel_uploaddata = $clexcel_uploaddata = $chexcel_uploaddata = $knlogin_uploaddata = [];
        $ats_parties = [];
        $whr = $conditions = [];

        if (in_array($id, ExcelUpload::IDS_FOR_UPLOAD)) {
            $excel = new ExcelUpload($id);
            $result = $excel->upload($sessionData['org_id']);

            if (!empty($result['booking_ids'])) {
                $properties->getOrderBookings($result['booking_ids'], $ats_parties);
                $data['ats_parties'] = !empty($ats_parties) ? json_encode($ats_parties, JSON_HEX_TAG) : [];
            }

            $list_type = $result['list_types']['list_type'] ?? 0;
            $cllist_type = $result['list_types']['cllist_type'] ?? 0;
            $charge_list_type = $result['list_types']['charge_list_type'] ?? 0;
            $knlogin_list_type = $result['list_types']['knlogin_list_type'] ?? 0;
        } elseif ($id) {
            $getArguments['bookingid'] = $properties->getBooking($id, $ats_parties);
            $data['getbookingid'] = $getArguments['bookingid'];
            $data['ats_parties'] = !empty($ats_parties) ? json_encode($ats_parties, JSON_HEX_TAG) : [];
        }

        $getArguments = (new GetArgumentsResolver())->resolve($this->getDefaultFilters(), $getArguments, ['page', 'limit']);

        if (isset($getArguments['search_type']) && $getArguments['search_type'] === 'advanced') {
            $search = new AdvancedSearch($getArguments);
            $conditions = $search->buildWhereClause($request, $sessionData['user_role_id'], $sessionData['user_id'], $sessionData['org_id']);
        } else {
            $search = new Search($getArguments);
        }

        $field = $properties->getDateFieldName($getArguments);
        $search->fromDate($field, $whr);
        $search->toDate($field, $whr);

        $orderQueryService = new OrderQueryService();
        $orderQueryService->addDateRangeFilter($whr, $getArguments, 'advpickupfrom_date', 'advpickupto_date', 'pickup_datetime');
        $orderQueryService->addDateRangeFilter($whr, $getArguments, 'advdeliveryfrom_date', 'advdeliveryto_date', 'delivery_datetime');


        $whr = array_merge($whr, $conditions);

        $status_search = $getArguments['status'] ?? $getArguments['order_status'] ?? '';

        $searchids = $getArguments['bookingid'] ?? [];
        if (empty($searchids) && !empty($getArguments['container_no'])) {
            $searchids = OrderReference::getContainerNum($sessionData['user_id'], $sessionData['country_user_ids'], $sessionData['be_value'], $getArguments['container_no']);
        } elseif (!empty($searchids) && !empty($getArguments['container_no'])) {
            $container_num_arr = OrderReference::getContainerNum($sessionData['user_id'], $sessionData['country_user_ids'], $sessionData['be_value'], $container_no = '');
            $searchids = array_intersect($container_num_arr, $searchids);
        }

        if (!empty($getArguments['order_reftype'])) {
            $advancedids = OrderReference::getRefNum($sessionData['user_id'], $getArguments['order_reftype'], $getArguments['ref_val']);
            $searchids = !empty($advancedids) ? $advancedids : [0];
        }

        if (!empty($getArguments['salog_ref'])) {
            $advancedids = OrderReference::getSalogRefNum($sessionData['user_id'], $getArguments['salog_ref'], $sessionData['org_id']);
            $searchids = !empty($advancedids) ? $advancedids : [0];
        }

        $wildcard_search = $getArguments['wildcard_order_id'] ?? '';
        if (!empty($getArguments['order_id'])) {
            $searchids = [$getArguments['order_id']];
        }

        if (!empty($getArguments['order_references'])) {
            $referenceResults = OrderReference::getOrderIdsByReferenceValues($getArguments['order_references']);
            $searchids = empty($referenceResults) ? [0] : $referenceResults;
        }

        $orderIdsMap = $properties->getOrderIdsMapping($wildcard_search, $searchids, $sessionData['org_id'], $sessionData['user_id'], $sessionData['country_user_ids']);

        $subcusts = $sessionData['sub_cust'];
        if ($sessionData['user_role_id'] == '4' && $subcusts && !empty($subcusts)) {
            $subcusts[] = $sessionData['cust_id'];
        }
        $orderdataQuery = $orderQueryService->indexQuery($sessionData['user_id'], $orgId, $searchids, $status_search, $sessionData['cust_id'], $sessionData['country_user_ids'], $whr, $subcusts, $orderIdsMap);


        $limit = $request->query('limit', 10);
        $limit = $limit === 'All' ? 5000 : (int)$limit;
        $page = (int)$request->query('page', 1);

        $pagination = new Pagination($orderdataQuery, $limit, $page, $getArguments);
        $paginationResponse = $pagination->paginate();

        if ($paginationResponse->hasItems() && !$request->query('debug')) {
            $this->orderProcessor->processOrderItems($paginationResponse, $properties, $sessionData);
        }

        $data = array_merge($data, [
            'list_type' => $list_type ?? 0,
            'excel_uploaddata' => $excel_uploaddata,
            'bill_type' => 'ShipmentX',
            'charge_list_type' => $charge_list_type ?? 0,
            'chexcel_uploaddata' => $chexcel_uploaddata,
            'cllist_type' => $cllist_type ?? 0,
            'knlogin_uploaddata' => $knlogin_uploaddata,
            'knlogin_list_type' => $knlogin_list_type ?? 0,
            'clexcel_uploaddata' => $clexcel_uploaddata,
            'bill_types' => [],
            'ref_names_arr' => ReferenceMaster::getRefNums(),
            'user_currency' => $user->usr_tzone['currency'] ?? 'USD',
            'currencies' => CountryMaster::getCurrencies(),
            'pagination' => $paginationResponse,
        ]);
        if (empty($data)) {
            return response()->json([
                'status' => 'success',
                'message' => 'No records found for the specified type and organization',
                'data' => []
            ], 200);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Orders retrieved successfully',
            'data' => $data
        ], 200);
    }

    protected function getDefaultFilters(): array
    {
        return [];
    }

    public function neworder(Request $request)
    {

        // if (Auth::user()->business_type === 'Carrier') {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Unauthorized access for Carrier',
        //         'data' => ['status' => 0]
        //     ], 403);
        // }
        $post = $request->all() ?? [];
        $user = Auth::user();
        $user_id = $user->id ?? $post['user_id'] ?? 0;
        $org_id = $user->org_id ?? $post['org_id'] ?? 0;
        $be_value = $user->be_value ?? $post['be_value'] ?? 0;

        $currencies = $user->usr_tzone['currency'] ?? [];
        $country_masters = CountryMaster::where('status', 1)->pluck('currency')->toArray();
        $currencies = array_unique(array_merge($currencies, $country_masters));

        $pickup_details = [];
        $cust_id = $user->cust_id ?? null;
        if ($cust_id) {
            $pickupResponse = $this->orderProcessor->getPickupDetails($cust_id);
            $pickup_details = $pickupResponse->getData()->data; // Extract data from JSON response
        }

        // Get order types
        $ordertypes = [];
        if ($cust_id) {
            $ordertypes = OrderType::where('customer_id', $cust_id)
                ->where('org_id', $org_id)
                ->where('status', 1)
                ->select('id', 'type_name')
                ->groupBy('type_name', 'id')
                ->get()
                ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                ->toArray();

            if (empty($ordertypes)) {
                $ordertypes = OrderType::where('org_id', $org_id)
                    ->where('status', 1)
                    ->select('id', 'type_name')
                    ->groupBy('type_name')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();

                if (empty($ordertypes)) {
                    $ordertypes = OrderType::where('org_id', 'SGKN')
                        ->where('status', 1)
                        ->select('id', 'type_name')
                        ->groupBy('type_name')
                        ->get()
                        ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                        ->toArray();
                }
            }
        } else {
            $branch_exists = OrderType::where('status', 1)
                ->where('be_value', $be_value)
                ->where('org_id', $org_id)
                ->exists();

            if ($branch_exists) {
                $ordertypes = OrderType::where('status', 1)->where('org_id', $org_id)
                    ->select('id', 'type_name')
                    ->groupBy('type_name', 'id')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();
            } else {
                $ordertypes = OrderType::where('status', 1)
                    ->where('org_id', $org_id)
                    ->select('id', 'type_name')
                    ->groupBy('type_name', 'id')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();
            }
        }

        $roles = SxPartyTypes::where('org_id', $org_id)
            ->where('status', 1)
            ->select('id', 'type_name')
            ->groupBy('id', 'type_name')
            ->get()
            ->map(fn($role) => ['id' => $role->id, 'name' => $role->name])
            ->toArray();

        // Get charge codes
        $chargecodes = ChargeCode::where('status', 1)
            ->select('id', 'charge_code')
            ->get()
            ->map(fn($charge) => ['charge_id' => $charge->id, 'charge_code' => $charge->charge_code])
            ->toArray();

        // Get VAT categories
        $vatcategory = VatCategory::where('org_id', $org_id)
            ->where('status', 1)
            ->select('id', 'description', 'vat_category', 'vat_percentage')
            ->get()
            ->map(fn($vat) => [
                'id' => $vat->id,
                'val' => "{$vat->id}_{$vat->vat_category}",
                'desc' => "{$vat->description} ({$vat->vat_category}-{$vat->vat_percentage})",
            ])
            ->toArray();

        // Get cost centers
        $costcenter = CostCenter::where('org_id', $org_id)
            ->where('status', 1)
            ->select('id', 'type_name')
            ->groupBy('type_name', 'id')
            ->get()
            ->map(fn($cost) => ['type_id' => $cost->id, 'type_name' => $cost->type_name])
            ->toArray();

        // Prepare response data
        $data = [
            'currencies' => $currencies,
            'org_id' => $org_id,
            'be_value' => $be_value,
            'chargecodes' => $chargecodes,
            'pickup_details' => $pickup_details,
            'ordertypes' => $ordertypes,
            'costcenter' => $costcenter,
            'roles' => $roles,
            'vatcategory' => $vatcategory,
            'marks_numbers_column' => true,
        ];

        return response()->json([
            'status' => 'success',
            'message' => 'Order data retrieved successfully',
            'data' => $data
        ], 200);
    }

    public function insertorder(Request $request)
    {
        $cdate = now()->format('Y-m-d H:i:s');
        $user = Auth::user();
        $user_id = $user->id ?? $request->input('user_id', 0);
        $org_id = $user->org_id ?? $request->input('org_id', 0);
        $be_value = $user->be_value ?? $request->input('be_value', 0);
        $curtz = $user->usr_tzone['timezone'] ?? 'UTC';

        // Validate request data
        // $validated = $request->validate([
        //     'product' => 'nullable|string|max:255',
        //     'service' => 'nullable|string|max:50',
        //     'order_shipper_id' => 'nullable|string|max:50',
        //     'delivery_terms' => 'nullable|string|max:50',
        //     'incoterm' => 'nullable|string|max:50',
        //     'delivery_note' => 'nullable|string|max:50',
        //     'container_num' => 'nullable|string|max:50',
        //     'purchase_order' => 'nullable|string|max:50',
        //     'notify_party' => 'nullable|string|max:50',
        //     'currency' => 'nullable|string|max:3',
        //     'goods_value' => 'nullable|numeric|min:0',
        //     'external_order_id' => 'nullable|string|max:50',
        //     'shipment_type' => 'nullable|integer',
        //     'region' => 'nullable|string|max:50',
        //     'p_latitude' => 'nullable|numeric',
        //     'p_longitude' => 'nullable|numeric',
        //     'd_latitude' => 'nullable|numeric',
        //     'd_longitude' => 'nullable|numeric',
        //     'order_party_row_id' => 'nullable|string|max:50',
        //     'order_inv_row_id' => 'nullable|string|max:50',
        //     'order_cargo_id' => 'nullable|string|max:50',
        //     'order_pickup_id' => 'nullable|string|max:50',
        //     'order_drop_id' => 'nullable|string|max:50',
        //     'early_pickup' => 'nullable|date_format:Y-m-d H:i:s',
        //     'late_pickup' => 'nullable|date_format:Y-m-d H:i:s',
        //     'early_delivery' => 'nullable|date_format:Y-m-d H:i:s',
        //     'late_delivery' => 'nullable|date_format:Y-m-d H:i:s',
        //     'modeof_trasnport' => 'nullable|string|max:50|default:LTL',
        //     'order_type' => 'nullable|string|max:50',
        //     'cost_center' => 'nullable|string|max:50',
        //     'rev_row_id' => 'nullable|string|max:50',
        //     'ordcost_row_id' => 'nullable|string|max:50',
        //     'customer_id' => 'nullable|string|max:50',
        //     'driver_pickup_instructions' => 'nullable|string',
        //     'driver_delivery_instructions' => 'nullable|string',
        //     'multiple_marks_numbers' => 'nullable|string',
        //     'docs_sent_datetime' => 'nullable|date_format:Y-m-d H:i:s',
        //     'docs_received_datetime' => 'nullable|date_format:Y-m-d H:i:s',
        //     'third_party_post' => 'nullable|array',
        // ]);

        // Extract validated data
        $product = $request->input('product', '');
        $service = $request->input('service', '');
        $order_shipper_id = $request->input('order_shipper_id', '');
        $delivery_terms = $request->input('delivery_terms', '');
        $incoterm = $request->input('incoterm', '');
        $shipment_id = $request->input('delivery_note', 'SX' . time());
        $container_no = $request->input('container_num', '');
        $porder = $request->input('purchase_order', '');
        $notify_party = $request->input('notify_party', '');
        $currency = $request->input('currency', '');
        $goods_value = $request->input('goods_value', 0.00);
        $external_order_id = $request->input('external_order_id', '');
        $shipment_type = $request->input('shipment_type', 0);
        $region = $request->input('region', 0);
        $p_latitude = $request->input('p_latitude', '');
        $p_longitude = $request->input('p_longitude', '');
        $d_latitude = $request->input('d_latitude', '');
        $d_longitude = $request->input('d_longitude', '');
        $party_row_id = $request->input('order_party_row_id', '0');
        $order_inv_row_id = $request->input('order_inv_row_id', '0');
        $order_cargo_id = $request->input('order_cargo_id', '');
        $pickup = $request->input('order_pickup_id', '0');
        $delivery = $request->input('order_drop_id', '');
        $early_pickup = $request->input('early_pickup', '');
        $late_pickup = $request->input('late_pickup', '');
        $early_delivery = $request->input('early_delivery', '');
        $late_delivery = $request->input('late_delivery', '');
        $modeof_trasnport = $request->input('modeof_trasnport', 'LTL');
        $order_type = $request->input('order_type', '');
        $cost_center = $request->input('cost_center', null);
        $rev_row_id = $request->input('rev_row_id', '');
        $ordcost_row_id = $request->input('ordcost_row_id', '');
        $customer_code = $request->input('customer_id', '');
        $driver_pickup_instructions = $request->input('driver_pickup_instructions', '');
        $driver_delivery_instructions = $request->input('driver_delivery_instructions', '');
        $multiple_marks_numbers = $request->input('multiple_marks_numbers', '');
        $multiple_marks_numbers = str_replace(["\r\n", "\r", "\n"], ", ", $multiple_marks_numbers);
        $docs_sent_datetime = $request->input('docs_sent_datetime', '');
        $docs_received_datetime = $request->input('docs_received_datetime', '');
        $third_party_post = $request->input('third_party_post', []);
        $third_party_post_str = !empty($third_party_post) ? implode(',', $third_party_post) : '';

        // Process pickup and delivery times
        $pickup_times = $this->orderProcessor->processOrderDatetimes($early_pickup, $late_pickup);
        $e_pickup = $this->orderProcessor->getDateTimeByTimezone('UTC', $pickup_times['early'], $curtz)['datetime'];
        $l_pickup = $this->orderProcessor->getDateTimeByTimezone('UTC', $pickup_times['late'], $curtz)['datetime'];

        $delivery_times = $this->orderProcessor->processOrderDatetimes($early_delivery, $late_delivery);
        $e_delivery = $this->orderProcessor->getDateTimeByTimezone('UTC', $delivery_times['early'], $curtz)['datetime'];
        $l_delivery = $this->orderProcessor->getDateTimeByTimezone('UTC', $delivery_times['late'], $curtz)['datetime'];

        // Get party details
        $drop_id = $pickup_custid = 0;
        $pickup_name = $pickup_country = $pickup_street = $pickup_pincode = $pickup_city = $pickup_state = '';
        $drop_name = $drop_country = $drop_street = $drop_pincode = $drop_city = $drop_state = '';
        $pickup_latitude = $pickup_longitude = $drop_latitude = $drop_longitude = $pickup_address = $drop_address = '';

        $drop_details = $this->orderProcessor->getPartyDetailsOptimized($delivery);
        if ($drop_details) {
            $drop_id = $drop_details->customeridentifier ?? $drop_details->code;
            $drop_name = $drop_details->name;
            $drop_state = $drop_details->state;
            $drop_country = $drop_details->country;
            $drop_street = $drop_details->street;
            $drop_pincode = $drop_details->pincode;
            $drop_city = $drop_details->city;
            $drop_latitude = $drop_details->latitude;
            $drop_longitude = $drop_details->longitude;
            $drop_mobile = $drop_details->mobile;
        }

        $shipper_details = $this->orderProcessor->getPartyDetailsOptimized($order_shipper_id);
        if ($shipper_details) {
            $pickup_custid = $shipper_details->customeridentifier ?? $shipper_details->code;
            $pickup_name = $shipper_details->name;
            $pickup_state = $shipper_details->state;
            $pickup_country = $shipper_details->country;
            $pickup_street = $shipper_details->street;
            $pickup_pincode = $shipper_details->pincode;
            $pickup_city = $shipper_details->city;
            $pickup_latitude = $shipper_details->latitude;
            $pickup_longitude = $shipper_details->longitude;
            $pickup_mobile = $shipper_details->mobile;
        }

        // Determine latitude and longitude
        $lat1 = $lng1 = $lat2 = $lng2 = '';
        if ($pickup_latitude && $pickup_longitude && $drop_latitude && $drop_longitude) {
            $lat1 = $pickup_latitude;
            $lng1 = $pickup_longitude;
            $lat2 = $drop_latitude;
            $lng2 = $drop_longitude;
        } elseif ($p_latitude && $p_longitude && $d_latitude && $d_longitude) {
            $lat1 = $p_latitude;
            $lng1 = $p_longitude;
            $lat2 = $d_latitude;
            $lng2 = $d_longitude;
        } else {
            $add1 = implode(',', array_filter([$pickup_street, $pickup_city, $pickup_country, $pickup_pincode]));
            $add2 = implode(',', array_filter([$drop_street, $drop_city, $drop_country, $drop_pincode]));
            $data1 = $this->orderProcessor->getLatLngsByPlace($add1);
            $lat1 = $data1[0] ?? '';
            $lng1 = $data1[1] ?? '';
            $data2 = $this->orderProcessor->getLatLngsByPlace($add2);
            $lat2 = $data2[0] ?? '';
            $lng2 = $data2[1] ?? '';
        }

        // Get transport mode
        $transport_mode = TransportMode::where('code', $modeof_trasnport)->select('id', 'name')->first();
        $tid = $transport_mode->id ?? 0;
        $tname = $transport_mode->name ?? '';

        // Determine customer_id
        $customer_id = $pickup ? (int)$pickup : 0;
        if (!$customer_id && $customer_code) {
            $customer = SxPartyMembers::where('code', $customer_code)->where('user_id', $user_id)->where('status', 1)->select('id')->first();
            $customer_id = $customer->id ?? 0;
        }

        // Get timezone-converted dates
        $logdate = $this->orderProcessor->getDateTimeByTimezone('UTC', now()->format('Y-m-d H:i:s'), $curtz)['datetime'];
        $docs_sent_datetime = $docs_sent_datetime && $docs_sent_datetime !== '0000-00-00 00:00:00'
            ? $this->orderProcessor->getDateTimeByTimezone('UTC', $docs_sent_datetime, $curtz)['datetime']
            : '';
        $docs_received_datetime = $docs_received_datetime && $docs_received_datetime !== '0000-00-00 00:00:00'
            ? $this->orderProcessor->getDateTimeByTimezone('UTC', $docs_received_datetime, $curtz)['datetime']
            : '';

        // Child ID from session
        $childid = $user->childid ?? 0;

        // Created source logic
        $created_source = '4';
        // Add logic for be_value == 'INCL' if needed
        // if ($be_value == 'INCL') { ... }

        // Insert order
        $orderinfo = [
            'shipment_id' => 0,
            'customer_id' => $customer_id,
            'product' => $product,
            'pickup_datetime' => $e_pickup,
            'delivery_datetime' => $e_delivery,
            'pickup_endtime' => $l_pickup,
            'drop_endtime' => $l_delivery,
            'goods_value' => $goods_value,
            'currency' => $currency,
            'org_id' => $org_id,
            'be_value' => $be_value,
            'created_at' => $logdate,
            'drop_custid' => $drop_id,
            'drop_partyid' => $drop_id,
            'user_id' => $user_id,
            'sub_uid' => $childid,
            'pickup_custid' => $pickup_custid,
            'pickup_partyid' => $pickup_custid,
            'pickup_country' => $pickup_country,
            'pickup_city' => $pickup_city,
            'pickup_pincode' => $pickup_pincode,
            'pickup_company' => $pickup_name,
            'pickup_address1' => $pickup_street,
            'pickup_address2' => $pickup_state,
            'delivery_country' => $drop_country,
            'delivery_city' => $drop_city,
            'delivery_pincode' => $drop_pincode,
            'delivery_company' => $drop_name,
            'delivery_address1' => $drop_street,
            'delivery_address2' => $drop_state,
            'is_created' => '1',
            'plat' => $lat1,
            'plng' => $lng1,
            'dlat' => $lat2,
            'dlng' => $lng2,
            'transport_mode' => $modeof_trasnport,
            'created_source' => $created_source,
            'external_order_id' => $external_order_id,
            'shipment_type' => $shipment_type,
            'region' => $region,
            'third_party_post' => $third_party_post_str,
        ];

        $order = Order::create($orderinfo);
        $order_id = $order->id;

        // Generate booking ID
        $user7_data = User::where('id', $user_id)->select('country_code', 'default_org_id')->first();
        $country_code = $user_data->country_code ?? '';
        $bookingInfo = ['user_id' => $user_id, 'order_id' => $order_id, 'country_code' => $country_code, 'org_id' => $org_id];
        $booking_id = $this->orderProcessor->generateBookingId($bookingInfo);

        $order->update(['order_id' => $booking_id]);

        // Insert order details
        $details = [
            'service' => $service,
            'delivery_term' => $delivery_terms,
            'incoterm' => $incoterm,
            'notify_party' => $notify_party,
            'order_row_id' => $order_id,
            'order_id' => $booking_id,
            'created_at' => $logdate,
            'shipper_id' => $order_shipper_id,
            'order_type' => $order_type,
            'docs_received_datetime' => $docs_received_datetime ?: null,
            'docs_sent_datetime' => $docs_sent_datetime ?: null,
            'temperature_control' => '0',
            'valorance_insurance' => '0',
            'high_cargo_value' => '0',
            'customs_required' => '0',
            'user_id' => $user_id,
            'be_value' => $be_value,
            'order_id_ref' => $order_id, // Renamed to avoid conflict with order_id
        ];

        OrderDetail::create($details);

        $this->orderProcessor->insertOrdersRefFileLineIdentifier([
            'pickupCity' => $pickup_city,
            'pickupState' => $pickup_state,
            'pickupCountry' => $pickup_country,
            'dropCity' => $drop_city,
            'dropState' => $drop_state,
            'dropCountry' => $drop_country,
            'orgId' => $org_id,
            'beValue' => $be_value,
            'orderRowId' => $order_id,
            'date' => $logdate,
        ]);

        $shipper_address = [
            'order_id' => $order_id,
            'be_value' => $be_value,
            'user_id' => $user_id,
            'party_master_id' => $order_shipper_id,
            'location_id' => $pickup_city,
            'street' => $pickup_street,
            'state' => $pickup_state,
            'address' => $pickup_address,
            'pincode' => $pickup_pincode,
            'country' => $pickup_country,
            'status' => 1,
            'created_at' => $cdate,
        ];
        $existing_shipper_address = OrderpartyAddress::where([
            'order_id' => $order_id,
            'party_master_id' => $order_shipper_id,
            'status' => 1,
        ])->first();

        if ($existing_shipper_address) {
            $existing_shipper_address->update($shipper_address);
            $shipperadd_id = $existing_shipper_address->id;
        } else {
            $shipper_address = OrderpartyAddress::create($shipper_address);
            $shipperadd_id = $shipper_address->id;
        }

        $delivery_address = [
            'order_id' => $order_id,
            'be_value' => $be_value,
            'user_id' => $user_id,
            'party_master_id' => $delivery,
            'location_id' => $drop_city,
            'street' => $drop_street,
            'state' => $drop_state,
            'address' => $drop_address,
            'pincode' => $drop_pincode,
            'country' => $drop_country,
            'status' => 1,
            'created_at' => $cdate,
        ];

        $existing_delivery_address = OrderpartyAddress::where([
            'order_id' => $order_id,
            'party_master_id' => $delivery,
            'status' => 1,
        ])->first();

        if ($existing_delivery_address) {
            $existing_delivery_address->update($delivery_address);
            $dropadd_id = $existing_delivery_address->id;
        } else {
            $delivery_address = OrderpartyAddress::create($delivery_address);
            $dropadd_id = $delivery_address->id;
        }

        // Handle cargo details
        $cargo_forship = [];
        if ($order_cargo_id) {
            $cargo_ids = array_unique(array_filter(explode(',', $order_cargo_id)));
            foreach ($cargo_ids as $cargo_id) {
                $cargo_details = CargoDetail::where('id', $cargo_id)->first();
                if ($cargo_details) {
                    $length = $cargo_details->length ?? 0;
                    $width = $cargo_details->width ?? 0;
                    $height = $cargo_details->height ?? 0;
                    $weight = $cargo_details->weight ?? 0;
                    $volume = $cargo_details->volume ?? 0;
                    $quantity = $cargo_details->quantity ?? 1;
                    $cargo_type = htmlspecialchars(str_replace(["'", "\""], '', $cargo_details->cargo_type ?? ''));
                    $description = htmlspecialchars(str_replace(["'", "\""], '', $cargo_details->goods_description ?? ''));
                    $volumetric_weight = $cargo_details->volumetric_weight ?? 0;
                    $ldm = $cargo_details->ldm ?? 0;
                    $second_weight = $cargo_details->second_weight ?? 0;
                    $second_volume = $cargo_details->second_volume ?? 0;
                    $item_id = $cargo_details->item_id ?? 0;
                    $marks_numbers = $cargo_details->marks_numbers ?? '';

                    $cargo_forship[] = $cargo_type;

                    $handling_unit = ShipunitType::where('unit_name', $cargo_type)
                        ->where('status', true)
                        ->value('id');

                    if (!$handling_unit) {
                        $handling_unit_data = [
                            'order_id' => $order_id,
                            'be_value' => $be_value,
                            'user_id' => $user_id,
                            'unit_name' => $cargo_type,
                            'unit_code' => $cargo_type,
                            'description' => $cargo_type,
                            'status' => 1,
                            'created_at' => $cdate,
                        ];
                        $handling_unit_model = ShipunitType::create($handling_unit_data);
                        $handling_unit = $handling_unit_model->id;
                    }

                    $qr_code = '';
                    if ($item_id > 0) {
                        $item = Item::where('id', $item_id)->value('item_number');
                        $qr_code = $item ?? '';
                    }

                    $cargo = [
                        'order_id' => $order_id,
                        'be_value' => $be_value,
                        'user_id' => $user_id,
                        'cargo_id' => $cargo_id,
                        'handling_unit' => $handling_unit,
                        'length' => $length,
                        'width' => $width,
                        'height' => $height,
                        'weight' => $weight,
                        'volumetric_weight' => $volumetric_weight,
                        'volweight_uom' => 'kg',
                        'ldm' => $ldm,
                        'volume' => $volume,
                        'second_volume' => $second_volume,
                        'second_weight' => $second_weight,
                        'quantity' => $quantity,
                        'quantity_type' => $cargo_type,
                        'cargo_content' => $description,
                        'qr_code' => $qr_code,
                        'marks_numbers' => $marks_numbers,
                        'status' => 1,
                        'created_at' => $cdate,
                    ];

                    OrderCargoDetail::create($cargo);
                }
            }
        }

        $unitspec = !empty($cargo_forship) ? implode(',', $cargo_forship) : '1';

        $totals = OrderCargoDetail::where('order_id', $order_id)->where('status', true)->selectRaw('COALESCE(SUM(weight), 0) as total_weight, COALESCE(SUM(volume), 0) as total_volume, COALESCE(SUM(quantity), 0) as total_quantity')->first();
        $order->update([
            'volume' => $totals->total_volume,
            'weight' => $totals->total_weight,
            'quantity' => $totals->total_quantity,
        ]);

        // Insert order references
        if ($porder) {
            OrderReference::create([
                'order_id' => $order_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'reference_id' => 'PO',
                'ref_value' => $porder,
                'created_at' => $cdate,
            ]);
        }

        if ($driver_pickup_instructions) {
            OrderReference::create([
                'order_id' => $order_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'reference_id' => 'ORD_PIKINST',
                'ref_value' => $driver_pickup_instructions,
                'created_at' => $cdate,
            ]);
        }

        if ($driver_delivery_instructions) {
            OrderReference::create([
                'order_id' => $order_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'reference_id' => 'ORD_DLVINST',
                'ref_value' => $driver_delivery_instructions,
                'created_at' => $cdate,
            ]);
        }

        if ($multiple_marks_numbers) {
            OrderReference::create([
                'order_id' => $order_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'reference_id' => 'MARKS_NUMBERS',
                'ref_value' => $multiple_marks_numbers,
                'created_at' => $cdate,
            ]);
        }

        // Handle order parties
        $ids = $party_row_id !== '0' ? array_filter(explode(',', $party_row_id)) : [];
        $inv_ids = $order_inv_row_id !== '0' ? array_filter(explode(',', $order_inv_row_id)) : [];
        $ids = array_merge($ids, $inv_ids);

        foreach ($ids as $id) {
            if ($id) {
                $party_type = SxPartyMembers::where('id', $id)->value('party_type') ?? 1;
                $existing_party = OrderParty::where([
                    'order_id' => $order_id,
                    'party_id' => $id,
                    'party_type' => $party_type,
                    'status' => 1,
                ])->first();

                if (!$existing_party) {
                    OrderParty::create([
                        'order_id' => $order_id,
                        'be_value' => $be_value,
                        'user_id' => $user_id,
                        'order_number' => $booking_id,
                        'party_id' => $id,
                        'party_type' => $party_type,
                        'status' => 1,
                        'created_at' => $cdate,
                    ]);
                }
            }
        }

        // Sub customer parties
        $sub_cut_parties = [$order_shipper_id, $delivery, '0', '0'];
        $this->orderProcessor->subcustpartiesinsert($order_id, $booking_id, $sub_cut_parties, $org_id, $be_value, $user_id, $cdate);

        // Handle revenue updates
        $rev_ids = $rev_row_id && $rev_row_id !== '0' ? array_filter(explode(',', $rev_row_id)) : [];
        if ($ordcost_row_id) {
            $cost_ids = array_filter(explode(',', $ordcost_row_id));
            $rev_ids = array_merge($rev_ids, $cost_ids);
        }
        if ($rev_ids) {
            Revenue::whereIn('id', $rev_ids)->update(['order_id' => $order_id]);
        }

        // Customer details
        $customer_details = SxPartyMembers::where('id', $customer_id)
            ->select('name', 'mobile', 'email')
            ->first();
        $customer_email = $customer_details->email ?? '';
        $customer_phone = $customer_details->mobile ?? '';

        // Geocode and shipment
        $pickupinfo = [
            'country' => trim($pickup_country),
            'order_country' => trim($pickup_country),
            'order_city' => trim($pickup_city),
            'order_zipcode' => trim($pickup_pincode),
            'state' => trim($pickup_state),
            'city' => trim($pickup_city),
            'region' => trim($pickup_street),
            'zipcode' => trim($pickup_pincode),
            'stoptype' => 'P',
        ];

        $dropinfo = [
            'country' => trim($drop_country),
            'order_country' => trim($drop_country),
            'order_city' => trim($drop_city),
            'order_zipcode' => trim($drop_pincode),
            'state' => trim($drop_state),
            'city' => trim($drop_city),
            'region' => trim($drop_street),
            'zipcode' => trim($drop_pincode),
            'stoptype' => 'D',
        ];

        $pickupgeocode = $this->orderProcessor->checkgeocode($pickupinfo);
        $dropgeocode = $this->orderProcessor->checkgeocode($dropinfo);

        if ($pickupgeocode && $dropgeocode) {
            $pickupgeocode['stoptype'] = 'P';
            $dropgeocode['stoptype'] = 'D';
            $pickupgeocode['order_country'] = trim($pickup_country);
            $pickupgeocode['order_city'] = trim($pickup_city);
            $pickupgeocode['order_zipcode'] = trim($pickup_pincode);
            $dropgeocode['order_country'] = trim($drop_country);
            $dropgeocode['order_city'] = trim($drop_city);
            $dropgeocode['order_zipcode'] = trim($drop_pincode);
            $pickupgeocode['cargo'] = $cargo ?? [];
            $dropgeocode['cargo'] = $cargo ?? [];
            $pickuproute = $this->orderProcessor->getcust_routeautomate($customer_id, $pickupgeocode);
            $droproute = $this->orderProcessor->getcust_routeautomate($customer_id, $dropgeocode);

            if ($pickuproute && $droproute) {
                $orderinfo = [
                    'id' => $order_id,
                    'order_id' => $booking_id,
                    'shipment_name' => 'BOXES',
                    'customer_phone' => $customer_phone,
                    'customer_email' => $customer_email,
                    'volume' => $totals->total_volume,
                    'weight' => $totals->total_weight,
                    'quantity' => $totals->total_quantity,
                ];
                $this->orderProcessor->createshipmentbyorder($pickuproute, $orderinfo);
            } else {
                $pickupinfo['cargo'] = $cargo ?? [];
                $dropinfo['cargo'] = $cargo ?? [];
                $pickuproute1 = $this->orderProcessor->getcust_routeautomate($customer_id, $pickupinfo);
                $droproute1 = $this->orderProcessor->getcust_routeautomate($customer_id, $dropinfo);
                if ($pickuproute1 && $droproute1) {
                    $orderinfo = [
                        'id' => $order_id,
                        'order_id' => $booking_id,
                        'shipment_name' => 'BOXES',
                        'customer_phone' => $customer_phone,
                        'customer_email' => $customer_email,
                        'volume' => $totals->total_volume,
                        'weight' => $totals->total_weight,
                        'quantity' => $totals->total_quantity,
                    ];
                    $this->orderProcessor->createshipmentbyorder($pickuproute1, $orderinfo);
                }
            }
        } else {
            $pickupinfo['cargo'] = $cargo ?? [];
            $dropinfo['cargo'] = $cargo ?? [];
            $pickuproute1 = $this->orderProcessor->getcust_routeautomate($customer_id, $pickupinfo);
            $droproute1 = $this->orderProcessor->getcust_routeautomate($customer_id, $dropinfo);
            if ($pickuproute1 && $droproute1) {
                $orderinfo = [
                    'id' => $order_id,
                    'order_id' => $booking_id,
                    'shipment_name' => 'BOXES',
                    'customer_phone' => $customer_phone,
                    'customer_email' => $customer_email,
                    'volume' => $totals->total_volume,
                    'weight' => $totals->total_weight,
                    'quantity' => $totals->total_quantity,
                ];
                $this->orderProcessor->createshipmentbyorder($pickuproute1, $orderinfo);
            }
        }


        // Rate management
        $pref_arr = [
            'pickup' => strtoupper($pickup_country),
            'pickup_state' => strtoupper($pickup_state),
            'pickup_city' => strtoupper($pickup_city),
            'pickup_pincode' => $pickup_pincode,
            'drop' => strtoupper($drop_country),
            'drop_state' => strtoupper($drop_state),
            'drop_city' => strtoupper($drop_city),
            'drop_pincode' => $drop_pincode,
            'customer_id' => $customer_code,
            'service' => $service,
            'product' => $product,
            'user_id' => $user_id,
            'org_id' => $org_id,
            'order_type' => $order_type,
            'order_id' => $order_id,
            'customer_row_id' => $customer_id,
        ];

        // $this->addrecodfororderinsertion($pref_arr);
        $this->orderProcessor->addRecordForOrderInsertion($pref_arr);
        // addRecordForOrderInsertion

        return response()->json([
            'status' => 'success',
            'message' => 'Order created successfully',
            'data' => ['status' => 1, 'order_id' => $order_id, 'booking_id' => $booking_id]
        ], 200);
    }

    public function orderTypeIndex(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'org_id' => 'integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        $org_id = $request->input('org_id');
        $where = ['order_types.org_id' => $org_id, 'order_types.status' => 1];

        if ($org_id !== 'RUKN') {
            $be_value = $request->input('be_value');
            if (!$be_value) {
                return response()->json([
                    'status' => 'error',
                    'errors' => ['be_value' => 'Business entity value is required']
                ], 422);
            }
            $where['order_types.be_value'] = $be_value;
        }

        $search_params = $request->all();
        unset($search_params['org_id']);
        unset($search_params['be_value']);

        $search_where = [];
        if (!empty($search_params['type_name'])) {
            $search_where['order_types.type_name'] = ['LIKE', '%' . $search_params['type_name'] . '%'];
        }
        if (!empty($search_params['department_code'])) {
            $search_where['order_types.department_code'] = $search_params['department_code'];
        }
        if (!empty($search_params['customer_name'])) {
            $search_where['sx_users.employee_name'] = ['LIKE', '%' . $search_params['customer_name'] . '%'];
        }
        if (isset($search_params['status']) && $search_params['status'] !== '') {
            $search_where['order_types.status'] = $search_params['status'];
            unset($where['order_types.status']);
        }

        $query = OrderType::select('order_types.*', 'sx_users.employee_name as customer_name')
            ->leftJoin('sx_users', 'order_types.customer_id', '=', 'sx_users.id')
            ->where($where);

        foreach ($search_where as $key => $value) {
            if (is_array($value)) {
                $query->where($key, $value[0], $value[1]);
            } else {
                $query->where($key, $value);
            }
        }

        $order_types = $query->get()->map(function ($row) {
            return [
                'id' => $row->id,
                'type_name' => $row->type_name,
                'org_id' => $row->org_id,
                'be_value' => $row->be_value,
                'department_code' => $row->department_code,
                'customer_name' => $row->customer_name,
                'description' => $row->description,
                'status' => $row->status,
                'ordtype_code' => $row->ordtype_code
            ];
        });

        return response()->json([
            'status' => 'success',
            'data' => $order_types
        ]);
    }

    public function orderTypeShow(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        if (!$id) {
            return response()->json([
                'status' => 'error',
                'errors' => ['id' => 'The ID is required']
            ], 422);
        }

        $org_id = $request->input('org_id');
        $where = ['order_types.id' => $id, 'order_types.org_id' => $org_id];

        if ($org_id !== 'RUKN') {
            $be_value = $request->input('be_value');
            if (!$be_value) {
                return response()->json([
                    'status' => 'error',
                    'errors' => ['be_value' => 'Business entity value is required']
                ], 422);
            }
            $where['order_types.be_value'] = $be_value;
        }

        $order_type = OrderType::select('order_types.*', 'sx_users.employee_name as customer_name')
            ->leftJoin('sx_users', 'order_types.customer_id', '=', 'sx_users.id')
            ->where($where)
            ->first();

        if (!$order_type) {
            return response()->json([
                'status' => 'error',
                'message' => 'Order type not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $order_type->id,
                'type_name' => $order_type->type_name,
                'org_id' => $order_type->org_id,
                'be_value' => $order_type->be_value,
                'department_code' => $order_type->department_code,
                'customer_name' => $order_type->customer_name,
                'description' => $order_type->description,
                'status' => $order_type->status,
                'ordtype_code' => $order_type->ordtype_code,
                'customer_id' => $order_type->customer_id
            ]
        ]);
    }

    /**
     * Show the form for creating a new order type
     */
    public function orderTypeAdd(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $org_id = $user->org_id;
        $be_value = $user->be_value;
        $uid = $user->id;
        $cust_id = $user->cust_id ?? 0;

        $data = [
            'org_id' => $org_id,
            'be_value' => $be_value,
            'customers' => SxPartyMembers::select('id', 'name', 'code')
                ->where(['org_id' => $org_id, 'be_value' => $be_value, 'status' => 1])
                ->orderBy('name')
                ->get(),
            'countries' => CountryMaster::select('country_code', 'country_name')
                ->where('status', 1)
                ->orderBy('country_name')
                ->get(),
            'business_entities' => DB::table('sx_business_entity_value')
                ->select('entity_id')
                ->where(['org_id' => $org_id, 'status' => 1])
                ->orderBy('entity_id')
                ->get()
        ];

        return response()->json([
            'status' => 'success',
            'data' => $data
        ]);
    }

    public function orderTypeCreate(Request $request)
    {
        Log::debug('OrderTypeController::store called with data: ' . json_encode($request->all()));

        $user = Auth::user();
        if (!$user) {
            Log::error('Unauthorized access attempt');
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $rules = [
            'type_name' => 'required|min:1',
            'org_id' => 'integer',
        ];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            Log::error('Validation errors: ' . json_encode($validator->errors()));
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();
        $data['customer_id'] = $request->input('customer_id', 0);
        $data['ordtype_code'] = !empty($data['type_name']) ? substr($data['type_name'], 0, 1) : '';
        $data['status'] = $request->input('status', 1);
        $data['created_at'] = now();

        try {
            $order_type = OrderType::create($data);
            return response()->json([
                'status' => 'success',
                'message' => 'Order type created successfully'
            ], 201);
        } catch (\Exception $e) {
            Log::error('Database error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified order type
     */
    public function orderTypeEdit(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $org_id = $user->org_id;
        $uid = $user->id;

        try {
            $orderType = OrderType::where(['id' => $id, 'org_id' => $org_id, 'status' => 1])->first();
            if (!$orderType) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Order type not found'
                ], 404);
            }

            $data = [
                'id' => $orderType->id,
                'org_id' => $orderType->org_id,
                'be_value' => $orderType->be_value,
                'type_name' => $orderType->type_name,
                'description' => $orderType->description,
                'ordtype_code' => $orderType->ordtype_code,
                'customer_id' => $orderType->customer_id,
                'customers' => SxPartyMembers::select('id', 'name', 'code')
                    ->where(['org_id' => $org_id, 'be_value' => $orderType->be_value, 'status' => 1])
                    ->orderBy('name')
                    ->get(),
                'countries' => CountryMaster::select('country_code', 'country_name')
                    ->where('status', 1)
                    ->orderBy('country_name')
                    ->get(),
                'business_entities' => DB::table('sx_business_entity_value')
                    ->select('entity_id')
                    ->where(['org_id' => $org_id, 'status' => 1])
                    ->orderBy('entity_id')
                    ->get()
            ];

            return response()->json([
                'status' => 'success',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve order type edit data: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve order type edit data: ' . $e->getMessage()
            ], 500);
        }
    }

    public function orderTypeUpdate(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        if (!$id) {
            return response()->json([
                'status' => 'error',
                'errors' => ['id' => 'The ID is required']
            ], 422);
        }

        $order_type = OrderType::find($id);
        if (!$order_type) {
            return response()->json([
                'status' => 'error',
                'message' => 'Order type not found'
            ], 404);
        }

        $data = $request->all();
        unset($data['org_id']);
        unset($data['be_value']);
        $data['customer_id'] = $request->input('customer_id', 0);
        $data['ordtype_code'] = !empty($data['type_name']) ? substr($data['type_name'], 0, 1) : '';
        $data['updated_at'] = now();

        try {
            $order_type->update($data);
            return response()->json([
                'status' => 'success',
                'message' => 'Order type updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Database error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function orderTypeDestroy($id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        if (!$id) {
            return response()->json([
                'status' => 'error',
                'errors' => ['id' => 'The ID is required']
            ], 422);
        }

        $order_type = OrderType::find($id);
        if (!$order_type) {
            return response()->json([
                'status' => 'error',
                'message' => 'Order type not found'
            ], 404);
        }

        try {
            $order_type->update(['status' => 0]);
            Log::info('Order type deleted successfully for user_id: ' . $user->id);
            return response()->json([
                'status' => 'success',
                'message' => 'Order type deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Database error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function editOrder(Request $request, $id = null)
    {
        $user = Auth::user();
        $data = $orderTypes = [];
        $orderDetails = $shipperDetails = $dropDetails = $pickupDetails = $deliveryArray = $chargeCodes = [];

        // Get currencies
        $data['currencies'] = CountryMaster::getCurrencies();
        $data['currency'] = $request->input('currency', 'USD'); // Default currency if not provided

        // Fetch cargo details using OrderCargodetail model
        $cargo = OrderCargodetail::where(['status' => 1, 'order_id' => $id])->first();
        $data['orderCargoId'] = $cargo ? $cargo->cargo_id : '';

        if ($id) {
            $order = $this->orderProcessor->getOrderToEdit($id);
            if (!$order) {
                return response()->json(['status' => 'error', 'message' => 'Order not found'], 404);
            }

            $incoterm = $order->incoterm;
            $shipmentId = $pickupInst = $deliveryInst = $containerNo = $purchaseOrder = $multipleMarksNumbers = '';

            $references = OrderReference::where('order_id', $id)
                ->whereIn('reference_id', ['DQ', 'PO', 'ORD_DLVINST', 'ORD_PIKINST', 'CTR', 'MARKS_NUMBERS'])
                ->get();

            foreach ($references as $ref) {
                switch ($ref->reference_id) {
                    case 'DQ':
                        $shipmentId = $ref->ref_value;
                        break;
                    case 'ORD_DLVINST':
                        $deliveryInst = $ref->ref_value;
                        break;
                    case 'ORD_PIKINST':
                        $pickupInst = $ref->ref_value;
                        break;
                    case 'CTR':
                        $containerNo = $ref->ref_value;
                        break;
                    case 'PO':
                        $purchaseOrder = $ref->ref_value;
                        break;
                    case 'MARKS_NUMBERS':
                        $multipleMarksNumbers = $ref->ref_value;
                        break;
                }
            }

            $pickupCustId = $order->pickup_custid;
            $status = $order->status;
            $tripId = $order->trip_id;
            $tripSts = $order->trip_sts;

            if ($status == 3) {
                return response()->json(['status' => 'error', 'message' => 'Forbidden access'], 403);
            }

            $orderStatus = 'PENDING';
            if ($tripId != 0 && $tripSts == 0) {
                $orderStatus = 'ACTIVE';
            }
            if ($tripId != 0 && $tripSts == 1) {
                $orderStatus = 'CLOSED';
            }

            $chkDate = Carbon::parse('2020-07-01 00:00:00');
            $created_at = Carbon::parse($order->created_at);
            $earlyPickup = $order->pickup_datetime;
            $earlyDelivery = $order->delivery_datetime;
            $latePickup = $order->pickup_endtime;
            $lateDelivery = $order->drop_endtime;
            $docsReceivedDatetime = $order->docs_received_datetime;
            $docsSentDatetime = $order->docs_sent_datetime;
            $timezone = $request->input('timezone', 'UTC');

            if ($created_at->gt($chkDate)) {
                if ($earlyPickup && $earlyPickup != '0000-00-00 00:00:00') {
                    $earlyPickup = $this->orderProcessor->getDateTimeByTimezone($timezone, $earlyPickup, 'UTC')['datetime'];
                }
                if ($earlyDelivery && $earlyDelivery != '0000-00-00 00:00:00') {
                    $earlyDelivery = $this->orderProcessor->getDateTimeByTimezone($timezone, $earlyDelivery, 'UTC')['datetime'];
                }
                if ($latePickup && $latePickup != '0000-00-00 00:00:00') {
                    $latePickup = $this->orderProcessor->getDateTimeByTimezone($timezone, $latePickup, 'UTC')['datetime'];
                }
                if ($lateDelivery && $lateDelivery != '0000-00-00 00:00:00') {
                    $lateDelivery = $this->orderProcessor->getDateTimeByTimezone($timezone, $lateDelivery, 'UTC')['datetime'];
                }
            }

            if ($docsSentDatetime && $docsSentDatetime != '0000-00-00 00:00:00') {
                $docsSentDatetime = $this->orderProcessor->getDateTimeByTimezone($timezone, $docsSentDatetime, 'UTC')['datetime'];
            }
            if ($docsReceivedDatetime && $docsReceivedDatetime != '0000-00-00 00:00:00') {
                $docsReceivedDatetime = $this->orderProcessor->getDateTimeByTimezone($timezone, $docsReceivedDatetime, 'UTC')['datetime'];
            }

            $orderDetails = [
                'id' => $order->id,
                'orderId' => $order->order_id,
                'shipmentId' => $shipmentId,
                'earlyPickup' => $earlyPickup,
                'earlyDelivery' => $earlyDelivery,
                'latePickup' => $latePickup,
                'lateDelivery' => $lateDelivery,
                'product' => $order->product,
                'service' => $order->service,
                'deliveryTerm' => $order->delivery_term,
                'incoterm' => $order->incoterm,
                'deliveryNote' => $order->delivery_note,
                'purchaseOrder' => $purchaseOrder,
                'multipleMarksNumbers' => $multipleMarksNumbers,
                'notifyParty' => $order->notify_party,
                'goodsValue' => $order->goods_value,
                'currency' => $order->currency,
                'laneReference' => $order->lane_reference,
                'distance' => $order->distance,
                'customsRequired' => $order->customs_required,
                'highCargoValue' => $order->high_cargo_value,
                'valoranceInsurance' => $order->valorance_insurance,
                'temperatureControl' => $order->temperature_control,
                'orgId' => $order->org_id,
                'beValue' => $order->be_value,
                'created_at' => $order->created_at,
                'orderType' => $order->order_type,
                'costCenter' => $order->cost_center_id,
                'transportMode' => $order->transport_mode,
                'shipmentType' => $order->shipment_type,
                'region' => $order->region,
                'pickupInst' => $pickupInst,
                'deliveryInst' => $deliveryInst,
                'containerNo' => $containerNo,
                'docsReceivedDatetime' => $docsReceivedDatetime,
                'docsSentDatetime' => $docsSentDatetime,
                'externalOrderId' => $order->external_order_id ?? '',
                'thirdPartyPost' => $order->third_party_post ?? '',
                'orderStatus' => $orderStatus,
            ];

            if ($incoterm) {
                $deliveryArray = $this->orderProcessor->getDeliveryTermsByIncoterm($incoterm);
            }

            $pickupId = $order->customer_id;
            $vendorId = $order->vendor_id;
            $userId = $user->user_id ?? $request->input('user_id', 0);
            $pickupLocation = [
                'country' => $order->pickup_country,
                'zipcode' => $order->pickup_pincode,
                'user_id' => $userId,
                'city' => $order->pickup_city,
            ];
            $deliveryLocation = [
                'country' => $order->delivery_country,
                'zipcode' => $order->delivery_pincode,
                'user_id' => $userId,
                'city' => $order->delivery_city,
            ];
            $info = ['order_id' => $id, 'product' => $order->product];

            // Assuming these methods are implemented elsewhere
            $data['rates'] = $this->orderProcessor->getCustomerProfileDetailsById($pickupId, $order->service, $pickupLocation, $deliveryLocation, $info);
            $data['vendorRates'] = $this->rateManagement->getvendorprofiledetailsbyid($vendorId, $order->service, $info);

            $orgId = $user->org_id ??  $request->input('org_id', $order->org_id);
            $custId = $user->cust_id ??  $request->input('cust_id', 0);

            if ($custId) {
                $orderTypes = OrderType::where(['org_id' => $orgId, 'customer_id' => $pickupId, 'status' => 1])
                    ->groupBy('type_name', 'id')
                    ->select('id', 'type_name')
                    ->get()
                    ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                    ->toArray();
            } else {
                $orderTypes = OrderType::where(['org_id' => $orgId, 'status' => 1])
                    ->groupBy('type_name')
                    ->select('id', 'type_name')
                    ->get()
                    ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                    ->toArray();
            }

            if (empty($orderTypes) && $created_at->lt(Carbon::parse('2021-03-19 00:00:00'))) {
                $orderTypes = OrderType::where(['org_id' => $orgId, 'status' => 1])
                    ->groupBy('type_name', 'id')
                    ->select('id', 'type_name')
                    ->get()
                    ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
                    ->toArray();
            }

            $pickupDetails = $this->orderProcessor->getPickupDetails($pickupId);
            // $pickupDetails = $this->getPickupDetails($pickupId)['data'];

            $dropId = $order->drop_custid;
            $partyDetails = DB::table('sx_party_members as p')
                ->join('order_parties as o', fn($join) => $join->on('p.id', '=', 'o.party_id')->where('o.status', 1))
                ->where('p.status', 1)
                ->where('o.order_id', $id)
                ->groupBy('o.party_type', 'p.id')
                ->select('p.id', 'p.party_type', 'p.name', 'p.mobile', 'p.email', 'p.code', 'p.fax', 'o.party_type')
                ->get();

            foreach ($partyDetails as $rr) {
                $partyType = SxPartyTypes::where('id', $rr->party_type)->select('type_name')->first();
                if ($partyType) {
                    if ($rr->name == $order->delivery || $partyType->type_name == 'Consignee') {
                        $dropDetails = [
                            'id' => $rr->id,
                            'name' => $rr->name,
                            'phone' => $rr->mobile,
                            'email' => $rr->email,
                            'fax' => $rr->fax,
                            'partyId' => $rr->code,
                        ];
                    }
                    if ($rr->name == $order->pickup || $partyType->type_name == 'Shipper') {
                        $shipperDetails = [
                            'id' => $rr->id,
                            'name' => $rr->name,
                            'phone' => $rr->mobile,
                            'email' => $rr->email,
                            'fax' => $rr->fax,
                            'partyId' => $rr->code,
                        ];
                    }
                }
            }

            $shipperDetails['name'] = $order->pickup;
            $shipperDetails['street'] = $order->pickup_address1;
            $shipperDetails['state'] = $order->pickup_address2;
            $shipperDetails['city'] = $order->pickup_city;
            $shipperDetails['country'] = $order->pickup_country;
            $shipperDetails['pincode'] = $order->pickup_pincode;

            $dropDetails['name'] = $order->delivery;
            $dropDetails['street'] = $order->delivery_address1;
            $dropDetails['state'] = $order->delivery_address2;
            $dropDetails['city'] = $order->delivery_city;
            $dropDetails['country'] = $order->delivery_country;
            $dropDetails['pincode'] = $order->delivery_pincode;

            $roles = SxPartyTypes::where(['org_id' => $orgId, 'status' => 1])
                ->groupBy('type_name', 'id')
                ->select('id', 'type_name')
                ->get()
                ->map(fn($res) => ['id' => $res->id, 'name' => $res->type_name])
                ->toArray();

            $chargeCodes = ChargeCode::where('status', 1)
                ->select('id', 'charge_code')
                ->get()
                ->map(fn($res) => ['chargeId' => $res->id, 'chargeCode' => $res->charge_code])
                ->toArray();

            $vasIds = VasMaster::where(['status' => 1, 'org_id' => $orgId])
                ->select('id', 'vas_id', 'vas_name')
                ->get()
                ->map(fn($res) => ['vasRowId' => $res->id, 'vasId' => $res->vas_id . '-' . $res->vas_name])
                ->toArray();
            $transportMode = new TransportMode();
            $transport = $transportMode->getTransportMode(
                $request->input('order_date', $created_at),
                $request->input('less_date', '2021-03-19 00:00:00'),
                $orgId,
                $request->input('be_value', $order->be_value)
            );

            $data['stoppageCodes'] = StoppageMaster::where('status', 1)
                ->select('id', 'code')
                ->get()
                ->map(fn($res) => ['id' => $res->id, 'code' => $res->code])
                ->toArray();

            $data['resolutionCodes'] = ResolutionMaster::where('status', 1)
                ->select('id', 'name')
                ->get()
                ->map(fn($res) => ['id' => $res->id, 'name' => $res->name])
                ->toArray();

            $data['vatCategory'] = VatCategory::where(['org_id' => $orgId, 'status' => 1])
                ->select('id', 'description', 'vat_category', 'vat_percentage')
                ->get()
                ->map(fn($res) => [
                    'id' => $res->id,
                    'val' => $res->id . '_' . $res->vat_category,
                    'desc' => $res->description . ' (' . $res->vat_category . '-' . $res->vat_percentage . ')',
                ])
                ->toArray();

            $data['spotOnData'] = OrderReference::where(['order_id' => $id, 'reference_id' => 'BN'])
                ->select('ref_value', 'updated_at')
                ->get()
                ->toArray();
            $data['spotOnLrExist'] = count($data['spotOnData']) > 0 ? 1 : 0;

            $arrCostCenter = [];
            // Example for specific org_id
            // if ($orgId == 'NZPG') {
            //     $arrCostCenter = CostCenter::where(['org_id' => $orgId, 'status' => 1])
            //         ->groupBy('type_name')
            //         ->select('id', 'type_name')
            //         ->get()
            //         ->map(fn($res) => ['typeId' => $res->id, 'typeName' => $res->type_name])
            //         ->toArray();
            // }

            $data['transport'] = $transport;
            $data['orderDetails'] = $orderDetails;
            $data['pickupDetails'] = $pickupDetails;
            $data['dropDetails'] = $dropDetails;
            $data['shipperDetails'] = $shipperDetails;
            $data['orderTypes'] = $orderTypes;
            $data['costCenter'] = $arrCostCenter;
            $data['deliveryArray'] = $deliveryArray;
            $data['chargeCodes'] = $chargeCodes;
            $data['roles'] = $roles;
            $data['vasIds'] = $vasIds;
            $data['marksNumbersColumn'] = [];

            return response()->json([
                'status' => 'success',
                'message' => 'Order details retrieved successfully',
                'data' => $data
            ], 200);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Invalid order ID',
            'data' => []
        ], 400);
    }

    public function getShipperListID(Request $request)
    {
        try {
            $orgId = Auth::user()->org_id ?? $request->input('org_id', 0);
            if ($orgId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID',
                    'data' => []
                ], 422);
            }
            $parties = $this->orderProcessor->getShipperList($orgId);
            return response()->json([
                'status' => 'success',
                'message' => 'Shipper list retrieved successfully',
                'data' => $parties
            ], 200);
        } catch (\Exception $e) {
            Log::error('GetShipperListID Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving shipper list',
                'data' => []
            ], 500);
        }
    }

    public function getShipperDetailsByID(Request $request)
    {
        $user = Auth::user();
        $orgId = $user->org_id ?? $request->input('org_id', 0);
        $userId = $user->id ?? $request->input('user_id', 0);
        $code = $request->input('id');
        $parties = ['customerDetails' => [], 'ordParties' => []];

        // Fetch shipper details
        $customer = SxPartyMembers::where([
            'id' => $code,
            'org_id' => $orgId,
            'status' => 1
        ])
            ->select('id', 'name', 'mobile', 'city', 'address', 'street', 'state', 'pincode', 'code', 'country', 'email', 'fax', 'org_id', 'be_value')
            ->first();

        if ($customer) {
            $parties['customerDetails'][] = [
                'id' => $customer->id,
                'name' => $customer->name,
                'phone' => $customer->mobile,
                'street' => $customer->street,
                'city' => $customer->city,
                'pincode' => $customer->pincode,
                'code' => $customer->code,
                'country' => $customer->country,
                'emailId' => $customer->email,
                'fax' => $customer->fax,
                'state' => $customer->state,
                'location' => $customer->city,
                'address' => $customer->address,
                'orgId' => $customer->org_id,
                'beValue' => $customer->be_value
            ];

            $customerId = $customer->id;

            // Fetch order types for the customer
            $orderTypes = OrderType::where([
                'customer_id' => $customerId,
                'status' => 1,
                'org_id' => $orgId
            ])
                ->select('id', 'type_name')
                ->groupBy('type_name')
                ->get();

            if ($orderTypes->isNotEmpty()) {
                foreach ($orderTypes as $res) {
                    $parties['ordParties'][] = [
                        'typeId' => $res->id,
                        'typeName' => $res->type_name
                    ];
                }
            } else {
                // Fallback to org_id order types
                $orderTypes = OrderType::where([
                    'org_id' => $orgId,
                    'status' => 1
                ])
                    ->select('id', 'type_name')
                    ->groupBy('type_name')
                    ->get();

                if ($orderTypes->isNotEmpty()) {
                    foreach ($orderTypes as $res) {
                        $parties['ordParties'][] = [
                            'typeId' => $res->id,
                            'typeName' => $res->type_name
                        ];
                    }
                } else {
                    // Fallback to SGKN org_id
                    $orderTypes = OrderType::where([
                        'org_id' => 'SGKN',
                        'status' => 1
                    ])
                        ->select('id', 'type_name')
                        ->groupBy('type_name')
                        ->get();

                    foreach ($orderTypes as $res) {
                        $parties['ordParties'][] = [
                            'typeId' => $res->id,
                            'typeName' => $res->type_name
                        ];
                    }
                }
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Shipper details retrieved successfully',
            'data' => $parties
        ], 200);
    }

    public function viewPartyList(Request $request)
    {
        $user = Auth::user();
        $userId = $user->id ?? $request->input('user_id', 0);
        $orgId = $user->org_id ?? $request->input('org_id', 0);
        $type = $request->input('type', '0');
        $partyType = $request->input('party', '');
        $customerId = $request->input('customer_id', '0');
        $parties = [];
        $masterIds = [];
        $contactIds = [];

        // Handle sub_cust session data
        $subCusts = $user->sub_cust ?? [];
        if ($customerId !== '0') {
            if (!in_array($customerId, $subCusts)) {
                $subCusts[] = $customerId;
            }
        }

        // Get customer codes
        $customerCodes = [];
        if (!empty($subCusts)) {
            $customerCodes = SxPartyMembers::whereIn('id', $subCusts)
                ->pluck('code')
                ->toArray();
        }

        // Party type condition
        $partyTypeCondition = $partyType ? ['name' => $partyType] : [];

        // Get customer row ID
        $customerRowId = null;
        if ($customerId !== '0') {
            $customer = SxPartyMembers::where('code', $customerId)
                ->select('id')
                ->first();
            $customerRowId = $customer ? $customer->id : null;
        }

        // Fetch parties with parent_id matching customer_row_id
        if ($customerRowId) {
            $custParties = SxPartyMembers::select([
                'sx_party_members.id as master_id',
                'sx_party_members.name as master_name',
                'sx_party_members.email as master_email_id',
                'sx_party_members.mobile as master_mobile',
                'sx_party_members.code',
                'sx_party_members.org_id',
                'sx_party_members.be_value',
                'sx_party_members.location_id as city',
                'sx_party_members.country',
                'sx_party_members.street',
                'sx_party_types.type_name as party_name'
            ])
                ->join('sx_party_types', 'sx_party_types.id', '=', 'sx_party_members.party_type')
                ->where([
                    'sx_party_members.status' => 1,
                    'sx_party_members.org_id' => $orgId,
                    'sx_party_members.parent_id' => $customerRowId
                ])
                ->whereNotNull('sx_party_members.code')
                ->where('sx_party_members.code', '!=', '0')
                // ->whereNotNull('sx_party_members.org_id')
                ->where('sx_party_members.org_id', '>', 0)
                // ->whereNotNull('sx_party_members.be_value')
                ->where('sx_party_members.be_value', '>', 0)
                ->when($partyType, function ($query) use ($partyType) {
                    return $query->where('sx_party_types.type_name', $partyType);
                })
                ->groupBy('sx_party_members.id', 'sx_party_types.type_name')
                ->orderByDesc('sx_party_members.id')
                ->get();

            foreach ($custParties as $res) {
                $check = "<input type='radio' name='consigneelist' id='consigneelist_{$res->master_id}' class='consigneelist' onchange='selectparty({$res->master_id})' value='{$res->master_id}'>";
                $contactIds[] = $res->master_id;
                $masterIds[] = $res->master_id;
                $parties[] = [
                    'check' => $check,
                    'id' => $res->code,
                    'name' => $res->master_name,
                    'email' => $res->master_email_id,
                    'mobile' => $res->master_mobile,
                    'partyName' => $res->party_name,
                    'orgId' => $res->org_id,
                    'beValue' => $res->be_value,
                    'partyType' => $partyType,
                    'city' => $res->city,
                    'country' => $res->country,
                    'street' => $res->street
                ];
            }
        }

        // Fetch parties with parent_id = 0
        $mainParties = SxPartyMembers::select([
            'sx_party_members.id as master_id',
            'sx_party_members.name as master_name',
            'sx_party_members.email as master_email_id',
            'sx_party_members.mobile as master_mobile',
            'sx_party_members.code',
            'sx_party_members.org_id',
            'sx_party_members.be_value',
            'sx_party_members.location_id as city',
            'sx_party_members.country',
            'sx_party_members.street',
            'sx_party_types.type_name as party_name'
        ])
            ->join('sx_party_types', 'sx_party_types.id', '=', 'sx_party_members.party_type')
            ->where([
                'sx_party_members.status' => 1,
                'sx_party_members.org_id' => $orgId,
                'sx_party_members.parent_id' => 0
            ])
            ->whereNotNull('sx_party_members.code')
            ->where('sx_party_members.code', '!=', '0')
            // ->whereNotNull('sx_party_members.org_id')
            ->where('sx_party_members.org_id', '>', 0)
            // ->whereNotNull('sx_party_members.be_value')
            ->where('sx_party_members.be_value', '>', 0)
            ->when($customerCodes, function ($query) use ($customerCodes) {
                return $query->whereIn('sx_party_members.customer_code', $customerCodes);
            })
            ->when($partyType, function ($query) use ($partyType) {
                return $query->where('sx_party_types.type_name', $partyType);
            })
            ->groupBy('sx_party_members.id', 'sx_party_types.type_name')
            ->orderByDesc('sx_party_members.id')
            ->get();

        foreach ($mainParties as $res) {
            $check = "<input type='radio' name='consigneelist' id='consigneelist_{$res->master_id}' class='consigneelist' onchange='selectparty({$res->master_id})' value='{$res->master_id}'>";
            $masterIds[] = $res->master_id;
            $parties[] = [
                'check' => $check,
                'id' => $res->code,
                'name' => $res->master_name,
                'email' => $res->master_email_id,
                'mobile' => $res->master_mobile,
                'partyName' => $res->party_name,
                'orgId' => $res->org_id,
                'beValue' => $res->be_value,
                'city' => $res->city,
                'country' => $res->country,
                'street' => $res->street
            ];
        }

        // Fetch additional parties with multiple party types
        $additionalIds = [];
        $multipleParties = SxPartyMembers::whereNotNull('party_types')
            ->where('org_id', $orgId)
            ->when($customerCodes, function ($query) use ($customerCodes) {
                return $query->whereIn('customer_code', $customerCodes);
            })
            ->when($masterIds, function ($query) use ($masterIds) {
                return $query->whereNotIn('id', $masterIds);
            })
            ->pluck('id', 'party_types')
            ->toArray();

        foreach ($multipleParties as $partyTypes => $masterId) {
            $partyTypesArray = array_filter(explode(',', $partyTypes));
            if (!empty($partyTypesArray)) {
                $shipperTypes = SxPartyTypes::whereIn('id', $partyTypesArray)
                    ->where('name', 'LIKE', $partyType)
                    ->pluck('id')
                    ->toArray();
                if (!empty($shipperTypes)) {
                    $additionalIds[] = $masterId;
                }
            }
        }

        if (!empty($additionalIds)) {
            $additionalParties = SxPartyMembers::select([
                'sx_party_members.id as master_id',
                'sx_party_members.name as master_name',
                'sx_party_members.email as master_email_id',
                'sx_party_members.mobile as master_mobile',
                'sx_party_members.code',
                'sx_party_members.org_id',
                'sx_party_members.be_value',
                'sx_party_members.location_id as city',
                'sx_party_members.country',
                'sx_party_members.street'
            ])
                ->whereIn('sx_party_members.id', $additionalIds)
                ->where([
                    'sx_party_members.status' => 1,
                    'sx_party_members.org_id' => $orgId,
                    'sx_party_members.parent_id' => 0
                ])
                ->whereNotNull('sx_party_members.code')
                ->where('sx_party_members.code', '!=', '0')
                // ->whereNotNull('sx_party_members.org_id')
                ->where('sx_party_members.org_id', '>', 0)
                // ->whereNotNull('sx_party_members.be_value')
                ->where('sx_party_members.be_value', '>', 0)
                ->groupBy('sx_party_members.id')
                ->orderByDesc('sx_party_members.id')
                ->get();

            foreach ($additionalParties as $res) {
                $check = "<input type='radio' name='consigneelist' id='consigneelist_{$res->master_id}' class='consigneelist' onchange='selectparty({$res->master_id})' value='{$res->master_id}'>";
                $masterIds[] = $res->master_id;
                $parties[] = [
                    'check' => $check,
                    'id' => $res->code,
                    'name' => $res->master_name,
                    'email' => $res->master_email_id,
                    'mobile' => $res->master_mobile,
                    'partyName' => $partyType,
                    'orgId' => $res->org_id,
                    'beValue' => $res->be_value,
                    'city' => $res->city,
                    'country' => $res->country,
                    'street' => $res->street
                ];
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Party list retrieved successfully',
            'data' => $parties
        ], 200);
    }

    public function getConsigneeDetailsListByID(Request $request)
    {
        $user = Auth::user();
        $orgId = $user->org_id ?? $request->input('org_id', 0);
        $userId = $user->id ?? $request->input('user_id', 0);
        $code = $request->input('id');
        $parties = [];

        $whereData = [
            'sx_party_members.status' => 1,
            'sx_party_members.org_id' => $orgId
        ];

        if ($code) {
            $whereData['sx_party_members.id'] = $code;
        }

        $consignee = SxPartyMembers::select([
            'sx_party_members.id',
            'sx_party_members.party_type',
            'sx_party_members.name',
            'sx_party_members.email',
            'sx_party_members.street',
            'sx_party_members.location_id as city',
            'sx_party_members.state',
            'sx_party_members.mobile',
            'sx_party_members.address',
            'sx_party_members.pincode',
            'sx_party_members.country',
            'sx_party_members.code',
            'sx_party_members.fax',
            'sx_party_members.additional_info as delivery_instructions',
            'sx_party_members.additional_info as pickup_instructions',
            'sx_party_types.type_name as partytype'
        ])
            ->leftJoin('sx_party_types', 'sx_party_members.party_type', '=', 'sx_party_types.id')
            ->where($whereData)
            ->first();

        if ($consignee) {
            $parties[] = [
                'id' => $consignee->id,
                'name' => $consignee->name,
                'phone' => $consignee->mobile,
                'street' => $consignee->street,
                'city' => $consignee->city,
                'pincode' => $consignee->pincode,
                'code' => $consignee->code,
                'country' => $consignee->country,
                'emailId' => $consignee->email,
                'fax' => $consignee->fax,
                'state' => $consignee->state,
                'address' => $consignee->address,
                'partyTypeId' => $consignee->party_type_id,
                'deliveryInstructions' => $consignee->delivery_instructions,
                'pickupInstructions' => $consignee->pickup_instructions,
                'roleName' => $consignee->partytype
            ];
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Consignee details retrieved successfully',
            'data' => $parties
        ], 200);
    }

    public function updateOrder(Request $request)
    {
        $user = Auth::user();
        $orderId = $request->input('order_id', '0');
        $orgId = $request->input('org_id', $user->org_id ?? '0');
        $userId = $user->id ?? $request->input('user_id', 0);

        if ($orderId === '0') {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid order ID'
            ], 400);
        }

        $cdate = Carbon::now()->format('Y-m-d H:i:s');
        $bookingId = $request->input('booking_id', '');
        $beValue = $request->input('be_value', '');
        $product = $request->input('product', '');
        $service = $request->input('service', '');
        $deliveryTerms = $request->input('delivery_terms', '');
        $modeOfTransport = $request->input('modeof_trasnport', 'TL');
        $orderType = $request->input('order_type', '');
        $costCenter = $request->input('cost_center', null);
        $incoterm = $request->input('incoterm', '');
        $shipmentId = $request->input('delivery_note', '');
        $containerNo = $request->input('container_num', '');
        $porder = $request->input('purchase_order', '');
        $orderShipperId = $request->input('order_shipper_id', '0');
        $customerId = $request->input('customer_id', '0');
        $pickup = $request->input('order_pickup_id', '');
        $delivery = $request->input('order_drop_id', '');
        $docsSentDatetime = $request->input('docs_sent_datetime', '');
        $docsReceivedDatetime = $request->input('docs_received_datetime', '');
        $consigneeName = $request->input('consignee_name', '');
        $consigneeStreet = $request->input('consignee_street', '');
        $consigneeCity = $request->input('consignee_city', '');
        $consigneeState = $request->input('consignee_state', '');
        $consigneeZipcode = $request->input('consignee_zipcode', '');
        $shipmentType = $request->input('shipment_type', 0);
        $region = $request->input('region', 0);
        $thirdPartyPost = $request->input('third_party_post', []);
        $thirdPartyPostStr = !empty($thirdPartyPost) ? implode(',', $thirdPartyPost) : '';
        $notifyParty = $request->input('notify_party', '');
        $driverPickupInstructions = $request->input('driver_pickup_instructions', '');
        $driverDeliveryInstructions = $request->input('driver_delivery_instructions', '');
        $multipleMarksNumbers = $request->input('multiple_marks_numbers', '');
        $multipleMarksNumbers = str_replace(["\r\n", "\r", "\n"], ", ", $multipleMarksNumbers);
        $goodsValue = $request->input('goods_value', '0.00') ?: '0.00';
        $currency = $request->input('currency', '');
        $earlyPickup = $request->input('early_pickup', '');
        $latePickup = $request->input('late_pickup', '');
        $earlyDelivery = $request->input('early_delivery', '');
        $lateDelivery = $request->input('late_delivery', '');

        // Lookup pickup ID if not provided but customer_id is
        if (empty($pickup) && !empty($customerId) && $customerId !== '0') {
            $customer = SxPartyMembers::where([
                'code' => $customerId,
                'user_id' => $userId,
                'status' => 1
            ])->select('id')->first();
            $pickup = $customer ? $customer->id : '';
        }

        // Get booking ID
        $order = Order::where('id', $orderId)->select('order_id')->first();
        if ($order) {
            $bookingId = $order->order_id;
        }

        // Process references
        $referenceUpdates = [];
        if (!empty($shipmentId)) {
            $referenceUpdates['DQ'] = $shipmentId;
        }
        if (!empty($driverPickupInstructions)) {
            $referenceUpdates['ORD_PIKINST'] = $driverPickupInstructions;
        }
        if (!empty($driverDeliveryInstructions)) {
            $referenceUpdates['ORD_DLVINST'] = $driverDeliveryInstructions;
        }
        if (!empty($multipleMarksNumbers)) {
            $referenceUpdates['MARKS_NUMBERS'] = $multipleMarksNumbers;
        }
        if (!empty($porder)) {
            $referenceUpdates['PO'] = $porder;
        }
        if (!empty($containerNo)) {
            $referenceUpdates['CTR'] = $containerNo;
        }

        // Batch update references
        if (!empty($referenceUpdates)) {
            $this->orderProcessor->updateOrderReferencesBatch($orderId, $referenceUpdates);
        }

        // Process pickup and delivery times
        $pickupTimes = $this->orderProcessor->processOrderDatetimes($earlyPickup, $latePickup);
        $ePickup = $pickupTimes['early'];
        $lPickup = $pickupTimes['late'];
        $deliveryTimes = $this->orderProcessor->processOrderDatetimes($earlyDelivery, $lateDelivery);
        $eDelivery = $deliveryTimes['early'];
        $lDelivery = $deliveryTimes['late'];

        // Process document timestamps with timezone conversion
        $curtz = $user->usr_tzone['timezone'] ?? 'UTC';
        if (!empty($docsSentDatetime) && $docsSentDatetime !== '0000-00-00 00:00:00') {
            $docsSentDatetime = $this->orderProcessor->getDateTimeByTimezone('UTC', $docsSentDatetime, $curtz)['datetime'];
        }
        if (!empty($docsReceivedDatetime) && $docsReceivedDatetime !== '0000-00-00 00:00:00') {
            $docsReceivedDatetime = $this->orderProcessor->getDateTimeByTimezone('UTC', $docsReceivedDatetime, $curtz)['datetime'];
        }

        // Fetch party details
        $pickupName = $pickupCountry = $pickupStreet = $pickupPincode = $pickupCity = $pickupState = $pickupAddress = '';
        $dropId = $dropName = $dropCountry = $dropStreet = $dropPincode = $dropCity = $dropState = $dropAddress = '';
        $pickupCustid = $dropCustid = $pickupId = $dropRowId = $shipperPartyId = $consigneePartyId = 0;

        $parties = OrderParty::select([
            'sx_party_members.id',
            'sx_party_members.party_type',
            'sx_party_members.name',
            'sx_party_members.code',
            'order_parties.id as order_party_id',
            'order_parties.party_type',
            'sx_party_types.type_name as party_type_name'
        ])
            ->join('sx_party_members', 'sx_party_members.id', '=', 'order_parties.party_id')
            ->join('sx_party_types', 'order_parties.party_type', '=', 'sx_party_types.id')
            ->where('sx_party_members.status', 1)
            ->where('order_parties.order_id', $orderId)
            ->where('order_parties.status', 1)
            ->groupBy('order_parties.party_type', 'sx_party_members.id', 'order_parties.id', 'sx_party_types.type_name')
            ->get();

        foreach ($parties as $party) {
            if ($party->party_type_name === 'Consignee') {
                $dropRowId = $party->id;
                $dropId = $party->code;
                $dropName = $party->name;
                $dropCustid = $party->code;
                $consigneePartyId = $party->order_party_id;
            } elseif ($party->party_type_name === 'Shipper') {
                $pickupId = $party->id;
                $pickupCustid = $party->code;
                $pickupName = $party->name;
                $shipperPartyId = $party->order_party_id;
            }
        }

        // Process shipper address changes
        if ($orderShipperId !== '0' && $orderShipperId != 0) {
            if ($pickupId != $orderShipperId) {
                $this->orderProcessor->processShipperChange($orderId, $orderShipperId, $pickupId, $shipperPartyId, $userId, $cdate, $pickupCustid, $pickupName, $pickupState, $pickupAddress, $pickupCountry, $pickupStreet, $pickupPincode, $pickupCity);
            } else {
                $this->orderProcessor->loadExistingShipperAddress($orderId, $orderShipperId, $pickupCity, $pickupCountry, $pickupStreet, $pickupPincode, $pickupState, $pickupAddress);
            }
        }

        // Process delivery address changes
        if ($delivery !== '0' && $delivery != 0) {
            if ($dropRowId != $delivery) {
                $this->orderProcessor->processDeliveryChange($orderId, $delivery, $dropRowId, $consigneePartyId, $userId, $cdate, $dropId, $dropCustid, $dropName, $dropState, $dropAddress, $dropCountry, $dropStreet, $dropPincode, $dropCity);
            } else {
                $this->orderProcessor->loadExistingDeliveryAddress($orderId, $delivery, $dropCity, $dropState, $dropCountry, $dropStreet, $dropPincode, $dropAddress);
            }
        }

        // Update sub customer parties
        $subCutParties = [
            0 => $orderShipperId,
            1 => $delivery,
            2 => '0',
            3 => '0'
        ];
        if (!empty(array_filter($subCutParties))) {
            $this->orderProcessor->subcustpartiesinsert($orderId, $bookingId, $subCutParties, $orgId, $beValue, $userId, $cdate);
        }

        // Get transport mode
        $transportMode = TransportMode::where('code', $modeOfTransport)
            ->select('id', 'name')
            ->first();
        $tid = $transportMode ? $transportMode->id : '';
        $tname = $transportMode ? $transportMode->name : '';

        // Prepare shipment data
        $shipArr = [
            'unitspec' => 1,
            'shipid' => $shipmentId,
            'txnid' => $shipmentId,
            'trucktype' => $tname,
            'pickupcnt' => '1',
            'dropcnt' => '1',
            'insertusr' => $pickupCustid,
            'carrier' => '0',
            'insertuserdate' => $cdate,
            'enddate' => Carbon::now()->addDay()->format('Y-m-d H:i:s'),
            'insdate' => $cdate,
            'upddate' => $cdate,
            'reason' => 'SHIPMENT',
            'purpose' => 'SEND INTEGRATION',
            'ship_object' => 'SHIPMENT',
            'logdate' => $cdate,
            'transport_mode' => $modeOfTransport,
            'domainname' => $beValue,
            'org_id' => $orgId,
            'be_value' => $beValue,
            'product' => $product,
            'freight_term' => '60',
            'freight_termname' => 'Free of Charge',
            'incoterm' => $incoterm,
            'modeoftransport' => $tid
        ];

        // Prepare order data
        $ins = [
            'shipment_id' => 0,
            'product' => $product,
            'pickup_datetime' => $ePickup,
            'delivery_datetime' => $eDelivery,
            'pickup_endtime' => $lPickup,
            'drop_endtime' => $lDelivery,
            'goods_value' => $goodsValue,
            'currency' => $currency,
            'org_id' => $orgId,
            'be_value' => $beValue,
            'transport_mode' => $modeOfTransport,
            'third_party_post' => $thirdPartyPostStr,
            'updated_at' => Carbon::now()->format('Y-m-d H:i:s')
        ];

        $fieldMappings = [
            'pickup_company' => $pickupName,
            'pickup_country' => $pickupCountry,
            'pickup_address1' => $pickupStreet,
            'pickup_city' => $pickupCity,
            'pickup_address2' => $pickupState,
            'pickup_pincode' => $pickupPincode,
            'delivery_company' => $consigneeName,
            'delivery_country' => $dropCountry,
            'delivery_address1' => $consigneeStreet,
            'delivery_address2' => $consigneeState,
            'delivery_city' => $consigneeCity,
            'delivery_pincode' => $consigneeZipcode,
            'customer_id' => $pickup,
            'shipment_type' => $shipmentType,
            'region' => $region
        ];

        foreach ($fieldMappings as $dbField => $value) {
            if (!empty($value)) {
                $ins[$dbField] = $value;
            }
        }

        // Process geolocation
        if (!empty($pickupPincode)) {
            $pickupCoordinates = $this->orderProcessor->getCoordinatesForAddress([$pickupStreet, $pickupCity, $pickupCountry, $pickupPincode]);
            if ($pickupCoordinates) {
                $ins['plat'] = $pickupCoordinates['lat'];
                $ins['plng'] = $pickupCoordinates['lng'];
            }
        }

        if (!empty($dropPincode)) {
            $deliveryCoordinates = $this->orderProcessor->getCoordinatesForAddress([$dropStreet, $dropCity, $dropCountry, $dropPincode]);
            if ($deliveryCoordinates) {
                $ins['dlat'] = $deliveryCoordinates['lat'];
                $ins['dlng'] = $deliveryCoordinates['lng'];
            }
        }

        // Update order
        Order::where('id', $orderId)->update($ins);

        $this->orderProcessor->insertOrdersRefFileLineIdentifier([
            'pickupCity' => $pickupCity,
            'pickupState' => $pickupState,
            'pickupCountry' => $pickupCountry,
            'dropCity' => $dropCity,
            'dropState' => $dropState,
            'dropCountry' => $dropCountry,
            'orgId' => $orgId,
            'beValue' => $beValue,
            'orderRowId' => $orderId,
            'date' => $cdate
        ]);

        // Update order details
        $detailsIns = [
            'service' => $service,
            'delivery_term' => $deliveryTerms,
            'incoterm' => $incoterm,
            'notify_party' => $notifyParty,
            'lane_reference' => 'LR',
            'distance' => '0',
            'temperature_control' => '0',
            'valorance_insurance' => '0',
            'high_cargo_value' => '0',
            'customs_required' => '0',
            'order_type' => $orderType,
            'cost_center_id' => $costCenter,
            'docs_received_datetime' => $docsReceivedDatetime,
            'docs_sent_datetime' => $docsSentDatetime
        ];

        $orderDetail = OrderDetail::where('order_row_id', $orderId)->first();
        if ($orderDetail) {
            OrderDetail::where('order_row_id', $orderId)->update($detailsIns);
        } else {
            $detailsIns['created_at'] = $cdate;
            $detailsIns['order_row_id'] = $orderId;
            $detailsIns['order_id'] = $bookingId;
            OrderDetail::create($detailsIns);
        }

        // Calculate total weight, volume, and quantity
        $totals = DB::table('order_cargodetails as ocd')
            ->leftJoin('cargo_details as cd', 'cd.id', '=', 'ocd.cargo_id')
            ->where('ocd.order_id', $orderId)
            ->where('ocd.status', '1')
            ->selectRaw("
                ROUND(SUM(CASE
                    WHEN cd.weight_unit IN ('G', 'Gms', 'gms', 'grm') THEN ocd.weight / 1000
                    WHEN cd.weight_unit IN ('Kg', 'kg') THEN ocd.weight
                    WHEN cd.weight_unit IN ('Tons', 'tons') THEN ocd.weight * 1000
                    ELSE 0
                END), 3) as total_weight,
                SUM(ocd.volume) as total_volume,
                SUM(ocd.quantity) as total_quantity
            ")
            ->first();

        $totalWeight = $totals->total_weight ?? 0;
        $totalVolume = $totals->total_volume ?? 0;
        $totalQuantity = $totals->total_quantity ?? 0;

        Order::where('id', $orderId)->update([
            'volume' => $totalVolume,
            'weight' => $totalWeight,
            'quantity' => $totalQuantity
        ]);

        // Update shipment and employee data
        $this->orderProcessor->updateShipmentEmployeeData($orderId, $bookingId, $ePickup, $lPickup, $eDelivery, $lDelivery, $totalWeight);

        // Update additional stop details
        $this->orderProcessor->updateAdditionalStopDetails($orderId, $eDelivery, $lDelivery);

        return response()->json([
            'status' => 'success',
            'message' => 'Order Updated Successfully - ' . $bookingId,
            'data' => ['order_id' => $orderId]
        ], 200);
    }

    public function saveShipper(Request $request)
    {
        $user = Auth::user();
        $userId = $user->id ?? $request->input('user_id', 0);
        $orgId = $request->input('shipper_org_id', $user->org_id ?? '0');
        $beValue = $request->input('shipper_be_value', $user->be_value ?? '0');
        $custId = $request->input('shipper_row_id', '');
        $customerCode = $request->input('scustomer_code', '');
        $orderId = $request->input('shipper_orderrow_id', '');
        $pickupInstructions = $request->input('shipper_pickup_instructions') ? json_encode(['pickup_instructions' => $request->input('shipper_pickup_instructions')]) : null;
        $cdate = Carbon::now()->format('Y-m-d H:i:s');
        $status = '1';
        $address = implode(',', array_filter([
            $request->input('shipper_street'),
            $request->input('shipper_city'),
            $request->input('shipper_state')
        ]));
        $shipperId = $request->input('shipper_id', '0');
        $masterId = 0;

        // Check or create Shipper party type
        $partyType = SxPartyTypes::where([
            'type_name' => 'Shipper',
            'org_id' => $orgId
        ])->orderBy('created_at', 'desc')->select('id')->first();

        if ($partyType) {
            $partyId = $partyType->id;
        } else {
            $partyType = SxPartyTypes::create([
                'type_name' => 'Shipper',
                'description' => 'Shipper',
                'org_id' => $orgId,
                'be_value' => $beValue,
                'user_id' => $userId,
                'created_at' => $cdate
            ]);
            $partyId = $partyType->id;
        }
        // Prepare master data
        $master = [
            'party_type_id' => $partyId,
            'name' => $request->input('shipper_name'),
            'email_id' => $request->input('shipper_email'),
            'street' => $request->input('shipper_street'),
            'state' => $request->input('shipper_state'),
            'mobile' => $request->input('shipper_phone'),
            'pincode' => $request->input('shipper_zipcode'),
            'country' => $request->input('shipper_country'),
            'user_id' => $userId,
            'code' => $shipperId,
            'customeridentifier' => $shipperId,
            'org_id' => $orgId,
            'be_value' => $beValue,
            'status' => '1',
            'fax' => $request->input('shipper_fax'),
            'address' => $address,
            'location_id' => $request->input('shipper_city'),
            'additional_info' => $pickupInstructions
        ];

        // Check for Consignee party type
        $consigneeType = SxPartyTypes::where([
            'type_name' => 'Consignee',
            'org_id' => $orgId
        ])->orderBy('created_at', 'desc')->select('id')->first();
        $consigneeId = $consigneeType ? $consigneeType->id : null;

        if ($shipperId !== '0') {
            $chkmaster = SxPartyMembers::where([
                'code' => $shipperId,
                'status' => 1
            ])->select('id', 'customer_code')->first();

            if ($chkmaster) {
                $masterId = $chkmaster->id;
                $shipperCustomerCode = $chkmaster->customer_code;

                if ($orderId) {
                    $updateData = [
                        'mobile' => $request->input('shipper_phone'),
                        'email_id' => $request->input('shipper_email'),
                        'fax' => $request->input('shipper_fax')
                    ];
                    if (empty($shipperCustomerCode)) {
                        $updateData['customer_code'] = $customerCode;
                    }
                    SxPartyMembers::where('id', $masterId)->update($updateData);
                } else {
                    $master['customer_code'] = empty($shipperCustomerCode) ? $customerCode : $shipperCustomerCode;
                    SxPartyMembers::where('id', $masterId)->update($master);
                }
                $status = '1';
            } else {
                $master['party_types'] = $consigneeId;
                $master['party_type'] = $consigneeId;
                $master['created_at'] = $cdate;
                $master['customer_code'] = $customerCode;
                $newShipper = SxPartyMembers::create($master);
                $masterId = $newShipper->id;
                $status = '0';
            }
        } else {
            $master['party_types'] = $consigneeId ?: $partyId;
            $master['party_type'] = $consigneeId ?: $partyId;
            $master['created_at'] = $cdate;
            $master['customer_code'] = $customerCode;
            $newShipper = SxPartyMembers::create($master);
            $masterId = $newShipper->id;

            $countryCode = $user->phone_code ?? 'IN';
            $year = date('y');
            $week = date('W');
            $shipperId = $countryCode . $year . $week . $masterId;

            SxPartyMembers::where('id', $masterId)->update([
                'code' => $shipperId,
                'customeridentifier' => $shipperId
            ]);
            $status = '0';
        }

        // Handle orderparty_address
        if ($orderId && $masterId !== '0') {
            $chkAddress = OrderpartyAddress::where([
                'order_id' => $orderId,
                'party_master_id' => $masterId,
                'status' => 1
            ])->select('id')->first();

            $addressData = [
                'order_id' => $orderId,
                'party_master_id' => $masterId,
                'location_id' => $request->input('shipper_city'),
                'street' => $request->input('shipper_street'),
                'state' => $request->input('shipper_state'),
                'address' => $address,
                'pincode' => $request->input('shipper_zipcode'),
                'country' => $request->input('shipper_country'),
                'user_id' => $userId,
                'status' => 1,
                'be_value' => $beValue,
                'org_id' => $orgId
            ];

            if ($chkAddress) {
                OrderpartyAddress::where('id', $chkAddress->id)->update($addressData);
            } else {
                $addressData['created_at'] = $cdate;
                OrderpartyAddress::create($addressData);
            }

            // Update orders table
            $orderUpdate = array_filter([
                'pickup_company' => $request->input('shipper_name'),
                'pickup_country' => $request->input('shipper_country'),
                'pickup_city' => $request->input('shipper_city'),
                'pickup_pincode' => $request->input('shipper_zipcode'),
                'pickup_address2' => $request->input('shipper_state'),
                'pickup_address1' => $request->input('shipper_street')
            ]);

            if (!empty($orderUpdate)) {
                Order::where('id', $orderId)->update($orderUpdate);
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Shipper saved successfully',
            'data' => [
                'masterId' => $masterId,
                'status' => $status,
                'shipperId' => $shipperId
            ]
        ], 200);
    }

    public function ajaxListing(Request $request)
    {
        $draw = $request->input('draw', 1);
        $start = $request->input('start', 0);
        $orderId = $request->input('data.order_id');
        $actionType = $request->input('data.actionType', 'View');
        $orgId = $request->input('data.org_id', Auth::user()->org_id ?? 0);

        $indexColumn = $orgId === 'AUKN' ? 'orders.id' : 'cargo_details.id';

        $selectColumns = [
            'cargo_details.id',
            'cargo_details.cargo_type',
            'cargo_details.length_unit',
            'cargo_details.width_unit',
            'cargo_details.height_unit',
            'cargo_details.weight_unit',
            'cargo_details.volume_unit',
            'cargo_details.secondweight_uom',
            'cargo_details.secondvolume_uom',
            'cargo_details.goods_description',
            'cargo_details.stackable',
            'cargo_details.grounded',
            'cargo_details.splittable',
            'cargo_details.dg_goods',
            'order_cargodetails.id as order_cargo_id',
            'order_cargodetails.length',
            'order_cargodetails.width',
            'order_cargodetails.height',
            'order_cargodetails.weight',
            'order_cargodetails.second_weight',
            'order_cargodetails.volume',
            'order_cargodetails.second_volume',
            'order_cargodetails.volumetric_weight',
            'order_cargodetails.volweight_uom',
            'cargo_details.ldm',
            'order_cargodetails.quantity',
            'order_cargodetails.scanned_quantity',
            'order_cargodetails.qr_code',
            'order_cargodetails.reference_order_num',
            'order_cargodetails.marks_numbers'
        ];

        $dataTableSortOrdering = $actionType === 'Edit' ? [
            'cargo_details.id',
            'cargo_details.cargo_type',
            'cargo_details.goods_description',
            'order_cargodetails.marks_numbers',
            'order_cargodetails.quantity',
            'order_cargodetails.scanned_quantity',
            'order_cargodetails.length',
            'order_cargodetails.width',
            'order_cargodetails.height',
            'order_cargodetails.weight',
            'order_cargodetails.second_weight',
            'order_cargodetails.volumetric_weight',
            'order_cargodetails.volume',
            'order_cargodetails.second_volume',
            'cargo_details.ldm',
            'cargo_details.stackable',
            'cargo_details.grounded',
            'cargo_details.splittable',
            'cargo_details.dg_goods',
            'cargo_details.id'
        ] : [
            'cargo_details.cargo_type',
            'cargo_details.goods_description',
            'order_cargodetails.marks_numbers',
            'order_cargodetails.quantity',
            'order_cargodetails.scanned_quantity',
            'order_cargodetails.length',
            'order_cargodetails.width',
            'order_cargodetails.height',
            'order_cargodetails.weight',
            'order_cargodetails.second_weight',
            'order_cargodetails.volumetric_weight',
            'order_cargodetails.volume',
            'order_cargodetails.second_volume',
            'cargo_details.ldm',
            'cargo_details.stackable',
            'cargo_details.grounded',
            'cargo_details.splittable',
            'cargo_details.dg_goods',
            'cargo_details.id'
        ];

        $whereCondition = ['order_cargodetails.order_id' => $orderId, 'order_cargodetails.status' => 1];
        // $groupBy = 'cargo_details.id';
        $groupBy = ['cargo_details.id', 'order_cargodetails.id'];


        $getRecordListing = $this->orderProcessor->datatablesQuery($selectColumns, $dataTableSortOrdering, $whereCondition, $indexColumn, $groupBy, $request);

        $totalRecords = $getRecordListing['recordsTotal'];
        $recordsFiltered = $getRecordListing['recordsFiltered'];
        $recordListing = [];

        if (!empty($getRecordListing['data'])) {
            $totalQty = count($getRecordListing['data']);
            foreach ($getRecordListing['data'] as $i => $res) {
                $stackable = $res->stackable ? 'On' : 'Off';
                $grounded = $res->grounded ? 'On' : 'Off';
                $splittable = $res->splittable ? 'On' : 'Off';
                $dgGoods = $res->dg_goods ? 'Yes' : 'No';
                $scannedQuantity = $res->scanned_quantity ?? 0;
                $secondWeight = $res->second_weight ?? 0;
                $secondVolume = $res->second_volume ?? 0;
                $volumetricWeight = $res->volumetric_weight ?? 0;
                $ldm = $res->ldm ?? 0;
                $qrCode = $res->qr_code;

                // Placeholder for uom_string function (to be implemented in frontend)
                $uomString = fn($value, $unit, $decimals = null) => $value ? number_format($value, $decimals ?? 2) . ' ' . $unit : '0 ' . $unit;

                if ($scannedQuantity == 0) {
                    $label = '<a href="#" onclick="alert(\'There is no Labels Scanned for this cargo..!\')"><span class="icon tru-icon-pdf"></span></a>';
                } else {
                    $label = '<a href="' . route('labels.print', ['order_car_id' => $res->order_cargo_id, 'tot' => $totalQty, 'cargo_row_id' => $i]) . '" target="_blank" title="Print Label"><span class="icon tru-icon-pdf"></span></a>';
                }

                $row = [
                    'cargoType' => $res->cargo_type,
                    'goodsDescription' => $res->goods_description,
                    'marksNumbers' => $res->marks_numbers,
                    'quantity' => $res->quantity,
                    'qrCode' => $qrCode,
                    'scannedQuantity' => $scannedQuantity,
                    'length' => $uomString($res->length, $res->length_unit),
                    'width' => $uomString($res->width, $res->width_unit),
                    'height' => $uomString($res->height, $res->height_unit),
                    'weight' => $uomString($res->weight, $res->weight_unit, 3),
                    'secondWeight' => $uomString($secondWeight, $res->secondweight_uom),
                    'volumetricWeight' => $uomString($volumetricWeight, $res->volweight_uom),
                    'volume' => $uomString($res->volume, $res->volume_unit),
                    'secondVolume' => $uomString($secondVolume, $res->secondvolume_uom),
                    'ldm' => $ldm,
                    'stackable' => $stackable,
                    'grounded' => $grounded,
                    'splittable' => $splittable,
                    'dgGoods' => "<span title='Click To Get Dgoods' onclick='getDangerousGoodsDetails({$res->dg_goods}, {$res->order_cargo_id})' style='cursor:pointer;'>{$dgGoods}</span>",
                    'id' => $res->id
                ];

                if ($actionType === 'Edit') {
                    $action = "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'><a class='dropdown-toggle' data-toggle='dropdown' href='#'><span class='icon tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" .
                        "<li><a data-ordertype='editOrder' id='bEdit' type='button' class='btn btn-sm btn-default' onclick='rowcargoEdit(this, {$res->id}, \"{$res->cargo_type}\", \"{$res->goods_description}\", 0, {$res->quantity}, {$res->length}, {$res->width}, {$res->height}, {$res->weight}, {$res->volume}, {$volumetricWeight}, {$res->stackable}, {$res->grounded}, {$res->splittable}, {$res->dg_goods}, \"{$res->length_unit}\", \"{$res->width_unit}\", \"{$res->height_unit}\", \"{$res->weight_unit}\", \"{$res->volume_unit}\", \"{$res->volweight_uom}\", {$ldm}, {$scannedQuantity}, {$secondWeight}, \"{$res->secondweight_uom}\", {$secondVolume}, \"{$res->secondvolume_uom}\", " . ($qrCode ?: 0) . ", {$orderId})'><span class='glyphicon glyphicon-pencil'></span>Edit</a></li>" .
                        "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deleteordercargodetails({$res->id})'><span class='glyphicon glyphicon-trash'></span>Remove</a></li>" .
                        "<li><a id='bAcep' type='button' class='btn btn-sm btn-default' style='display:none;' onclick='rowAcep(this);'><span class='glyphicon glyphicon-ok'></span>Update</a></li>" .
                        "<li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='rowAdd(this);'><span class='glyphicon glyphicon-plus'></span>Add Cargo Details</a></li>" .
                        "<li><a id='innerpacking' type='button' class='btn btn-sm btn-default' onclick='getinnercargo(this, {$res->id})'><span class='fa fa-archive'></span>Get Inner Cargos</a></li></ul></ul>";
                    $row = ['action' => $action] + $row;
                }

                $recordListing[] = $row;
            }
        }

        return response()->json([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $recordsFiltered,
            'data' => $recordListing
        ], 200);
    }

    public function saveCargo(Request $request, $id = null, $orderId = null)
    {
        $user = Auth::user();
        $userId = $user->id ?? $request->input('user_id', 0);
        $beValue = $user->be_value ?? $request->input('be_value', 0);
        $cdate = Carbon::now()->format('Y-m-d H:i:s');

        // Input sanitization
        $stackable = $request->input('stackable', '0') === '1' ? 1 : 0;
        $grounded = $request->input('grounded', '0') === '1' ? 1 : 0;
        $splittable = $request->input('splittable', '0') === '1' ? 1 : 0;
        $dgGoods = $request->input('dg_goods', '0') === '1' ? 1 : 0;
        $length = is_numeric($request->input('length')) ? (float) $request->input('length') : 0.00;
        $width = is_numeric($request->input('width')) ? (float) $request->input('width') : 0.00;
        $height = is_numeric($request->input('height')) ? (float) $request->input('height') : 0.00;
        $weight = is_numeric($request->input('weight')) ? (float) $request->input('weight') : 0.00;
        $volume = is_numeric($request->input('volume')) ? (float) $request->input('volume') : 0.00;
        $ldm = is_numeric($request->input('ldm')) ? (float) $request->input('ldm') : 0.00;
        $itemId = $request->input('item_num_id', 0);
        $secondWeight = is_numeric($request->input('secondweight')) ? (float) $request->input('secondweight') : 0.00;
        $secondVolume = is_numeric($request->input('secondvolume')) ? (float) $request->input('secondvolume') : 0.00;
        $volumetricWeight = is_numeric($request->input('volumetric_weight')) ? (float) $request->input('volumetric_weight') : 0.00;
        $marksNumbers = $request->input('marks_numbers', '');
        $orderForCargo = $request->input('order_forcargo', '0');

        $cargoData = [
            'cargo_type' => $request->input('cargo_type'),
            'goods_description' => $request->input('goods_desc'),
            'quantity' => $request->input('quantity', 0),
            'length' => $length,
            'length_unit' => $request->input('length_uom'),
            'width' => $width,
            'width_unit' => $request->input('width_uom'),
            'height' => $height,
            'height_unit' => $request->input('height_uom'),
            'weight' => $weight,
            'weight_unit' => $request->input('weight_uom'),
            'volume' => $volume,
            'volume_unit' => $request->input('volume_uom'),
            'volumetric_weight' => $volumetricWeight,
            'volweight_uom' => $request->input('volweight_uom'),
            'stackable' => $stackable,
            'grounded' => $grounded,
            'splittable' => $splittable,
            'dg_goods' => $dgGoods,
            'createdby' => $userId,
            'created_at' => $cdate,
            'ldm' => $ldm,
            'second_weight' => $secondWeight,
            'second_volume' => $secondVolume,
            'secondvolume_uom' => $request->input('secondvolume_uom'),
            'secondweight_uom' => $request->input('secondweight_uom'),
            'marks_numbers' => $marksNumbers
        ];

        $cargoId = 0;
        $innerCargoId = [];

        DB::beginTransaction();
        try {
            if (!$id || $id === '0') {
                // Check item
                if (!$itemId) {
                    $item = Item::where('item_number', $request->input('cargo_type'))->select('id')->first();
                    $itemId = $item ? $item->id : 0;
                }
                if ($itemId) {
                    $cargoData['item_id'] = $itemId;
                }
                $cargo = CargoDetail::create($cargoData);
                $cargoId = $cargo->id;
            } else {
                $cargoId = $id;
                $qrCode = '';
                if (!$itemId) {
                    $cargoDetail = CargoDetail::where('id', $cargoId)->select('item_id')->first();
                    $itemId = $cargoDetail ? $cargoDetail->item_id : 0;
                }
                if ($itemId) {
                    $cargoData['item_id'] = $itemId;
                    $item = Item::where('id', $itemId)->select('item_number', 'hsn_code', 'color_code', 'color_code_name', 'size_code', 'size_code_name', 'unit_price')->first();
                    if ($item) {
                        $qrCode = $item->item_number;
                    }
                }
                CargoDetail::where('id', $cargoId)->update($cargoData);

                $orderCargoData = [
                    'length' => $length,
                    'width' => $width,
                    'height' => $height,
                    'weight' => $weight,
                    'volume' => $volume,
                    'quantity' => $request->input('quantity', 0),
                    'quantity_type' => $request->input('cargo_type'),
                    'cargo_content' => $request->input('goods_desc'),
                    'volumetric_weight' => $volumetricWeight,
                    'volweight_uom' => $request->input('volweight_uom'),
                    'ldm' => $ldm,
                    'second_weight' => $secondWeight,
                    'second_volume' => $secondVolume,
                    'marks_numbers' => $marksNumbers
                ];
                if ($qrCode) {
                    $orderCargoData['qr_code'] = $qrCode;
                }
                OrderCargodetail::where('cargo_id', $cargoId)->update($orderCargoData);
            }

            if ($orderForCargo > 0) {
                $qrCode = $hsnCode = $colorCode = $colorCodeName = $sizeCode = $sizeCodeName = '';
                $unitPrice = 0.00;
                if ($itemId) {
                    $item = Item::where('id', $itemId)->select('item_number', 'hsn_code', 'color_code', 'color_code_name', 'size_code', 'size_code_name', 'unit_price')->first();
                    if ($item) {
                        $qrCode = $item->item_number;
                        $hsnCode = $item->hsn_code;
                        $colorCode = $item->color_code;
                        $colorCodeName = $item->color_code_name;
                        $sizeCode = $item->size_code;
                        $sizeCodeName = $item->size_code_name;
                        $unitPrice = $item->unit_price ?? 0.00;
                    }
                }

                $handlingUnit = ShipunitType::where('unit_name', $request->input('cargo_type'))->select('id')->first();
                if (!$handlingUnit) {
                    $handlingUnit = ShipunitType::create([
                        'unit_name' => $request->input('cargo_type'),
                        'description' => $request->input('cargo_type'),
                        'user_id' => $userId,
                        'created_at' => $cdate,
                        'status' => 1,
                        'order_id' => $orderForCargo,
                        'be_value' => $beValue,
                        'unit_code' => '',
                    ]);
                }

                $orderCargo = OrderCargodetail::where(['order_id' => $orderForCargo, 'cargo_id' => $cargoId, 'status' => 1])->first();
                $orderCargoData = [
                    'order_id' => $orderForCargo,
                    'cargo_id' => $cargoId,
                    'status' => 1,
                    'created_at' => $cdate,
                    'length' => $length,
                    'width' => $width,
                    'height' => $height,
                    'weight' => $weight,
                    'volume' => $volume,
                    'quantity' => $request->input('quantity', 0),
                    'quantity_type' => $request->input('cargo_type'),
                    'cargo_content' => $request->input('goods_desc'),
                    'handling_unit' => $handlingUnit->id,
                    'volumetric_weight' => $volumetricWeight,
                    'volweight_uom' => $request->input('volweight_uom'),
                    'ldm' => $ldm,
                    'second_weight' => $secondWeight,
                    'second_volume' => $secondVolume,
                    'qr_code' => $qrCode,
                    'marks_numbers' => $marksNumbers,
                    'user_id' => $userId
                ];

                if (!$orderCargo) {
                    OrderCargodetail::create($orderCargoData);
                } else {
                    OrderCargodetail::where(['order_id' => $orderForCargo, 'cargo_id' => $cargoId])->update($orderCargoData);
                }

                if ($qrCode && $colorCode && $sizeCode) {
                    $innerCargoData = [
                        'cargo_id' => $cargoId,
                        'cargo_type' => $request->input('cargo_type'),
                        'goods_description' => $request->input('goods_desc'),
                        'quantity' => $request->input('quantity', 0),
                        'scanned_quantity' => 0,
                        'length' => $length,
                        'length_unit' => $request->input('length_uom'),
                        'width' => $width,
                        'width_unit' => $request->input('width_uom'),
                        'height' => $height,
                        'height_unit' => $request->input('height_uom'),
                        'weight' => $weight,
                        'weight_unit' => $request->input('weight_uom'),
                        'volume' => $volume,
                        'volume_unit' => $request->input('volume_uom'),
                        'stackable' => 0,
                        'qr_code' => $qrCode,
                        'status' => 1,
                        'createdby' => $userId,
                        'created_at' => $cdate,
                        'updated_at' => $cdate,
                        'color_code' => $colorCode,
                        'color_code_name' => $colorCodeName,
                        'size_code' => $sizeCode,
                        'size_name' => $sizeCodeName,
                        'unit_price' => $unitPrice,
                        'd_hsn_code' => $hsnCode
                    ];

                    $innerCargo = InnerCargo::where(['cargo_id' => $cargoId, 'cargo_type' => $request->input('cargo_type')])->first();
                    if ($innerCargo) {
                        InnerCargo::where(['cargo_id' => $cargoId, 'cargo_type' => $request->input('cargo_type')])->update($innerCargoData);
                    } else {
                        $innerCargo = InnerCargo::create($innerCargoData);
                        $innerCargoId[] = $innerCargo->id;
                    }
                }
            }

            // Placeholder for carrierweightupdate->updateCarrierWeight($orderId);
            // Implement if needed

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Cargo saved successfully',
                'data' => [
                    'cargoId' => $cargoId,
                    'innerCargoId' => $innerCargoId
                ]
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to save cargo: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function viewItemsList(Request $request)
    {
        try {
            $type = $request->input('ctype', '');
            $popup = $request->input('popup', '');
            $custId = $request->input('cust_id', Auth::user()->cust_id ?? '');
            $orgId = $request->input('org_id', Auth::user()->org_id ?? '0');

            $query = Item::where('status', 1);

            if ($type) {
                $query->where('item_id', 'not like', "%{$type}%");
            }

            $ironMountainIds = ['415', '187', '188', '192', '226', '428', '431', '430', '426', '427', '418', '417', '429', '419', '423', '416', '422', '425', '420', '424', '489', '421'];
            if ($custId && $custId !== '0' && in_array($custId, $ironMountainIds)) {
                $query->whereIn('item_id', ['Docs', 'Non-Docs']);
            }

            $whereCondition = $this->orderProcessor->getPackagesWhereCondition();
            $query->where(function ($q) use ($whereCondition) {
                $q->where('quickbook_status', '0')
                    ->orWhereIn('item_id', $whereCondition);
            });

            // Group by ID, order by ID descending, limit to 1000
            $items = $query->groupBy('id')
                ->orderBy('id', 'desc')
                ->take(1000)
                ->get();

            $result = [];
            foreach ($items as $res) {
                $secondWeight = $res->second_weight ?? 0;
                $secondVolume = $res->second_volume ?? 0;

                $check = "<input type='radio' name='listitem' id='listitem_{$res->id}' class='listitem' onchange='selectitem({$res->id})' value='{$res->id}'>";

                $result[] = [
                    'check' => $check,
                    'itemName' => $res->item_name,
                    'itemId' => $res->item_id,
                    'description' => $res->description,
                    'length' => $res->length . ($res->length_unit ?? ''),
                    'width' => $res->width . ($res->width_unit ?? ''),
                    'height' => $res->height . ($res->height_unit ?? ''),
                    'weight' => $res->weight . ($res->weight_unit ?? ''),
                    'volume' => $res->volume . ($res->volume_unit ?? ''),
                    'volumetricWeight' => $res->volumetric_weight . ($res->volweight_uom ?? ''),
                    'secondWeight' => $secondWeight . ($res->secondweight_uom ?? ''),
                    'secondVolume' => $secondVolume . ($res->secondvolume_uom ?? '')
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Items retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve items: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function getItemDetailsList(Request $request)
    {
        try {
            $itemId = $request->input('item_id', '');
            $items = [];

            if (!$itemId) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Item ID is required',
                    'data' => []
                ], 400);
            }

            $records = Item::where('status', 1)
                ->where('id', $itemId)
                ->orderBy('created_at', 'desc')
                ->get();

            if ($records->isEmpty()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No item found for the provided ID',
                    'data' => []
                ], 404);
            }

            foreach ($records as $res) {
                $secondWeight = $res->second_weight ?? 0;
                $secondVolume = $res->second_volume ?? 0;

                $items[] = [
                    'itemName' => $res->item_name,
                    'itemId' => $res->item_id,
                    'description' => $res->description,
                    'length' => $res->length,
                    'lengthUom' => ucfirst($res->length_unit ?? ''),
                    'width' => $res->width,
                    'widthUom' => ucfirst($res->width_unit ?? ''),
                    'height' => $res->height,
                    'heightUom' => ucfirst($res->height_unit ?? ''),
                    'weight' => $res->weight,
                    'weightUom' => ucfirst($res->weight_unit ?? ''),
                    'volume' => $res->volume,
                    'volumeUom' => ucfirst($res->volume_unit ?? ''),
                    'volumetricWeight' => $res->volumetric_weight,
                    'volweightUom' => ucfirst($res->volweight_uom ?? ''),
                    'secondVolume' => $secondVolume,
                    'secondWeight' => $secondWeight,
                    'secondweightUom' => ucfirst($res->secondweight_uom ?? ''),
                    'secondvolumeUom' => ucfirst($res->secondvolume_uom ?? ''),
                    'itemNumId' => $res->id,
                    'itemNumber' => $res->item_number,
                    'hsnCode' => $res->hsn_code,
                    'colorCode' => $res->color_code,
                    'colorCodeName' => $res->color_code_name,
                    'sizeCode' => $res->size_code,
                    'sizeCodeName' => $res->size_code_name,
                    'unitPrice' => $res->unit_price
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Item details retrieved successfully',
                'data' => $items
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve item details: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function getInnerCargo(Request $request)
    {
        try {
            $cargoId = $request->input('cargo_id', '');

            if (!$cargoId || $cargoId === '0') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cargo ID is required and must not be zero',
                    'data' => []
                ], 400);
            }

            $records = InnerCargo::where('cargo_id', $cargoId)
                ->where('status', 1)
                ->groupBy('id')
                ->orderBy('id', 'desc')
                ->get();

            if ($records->isEmpty()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No inner cargo found for the provided cargo ID',
                    'data' => []
                ], 404);
            }

            $uomString = fn($value, $unit) => $value ? number_format($value, $unit === 'cbm' ? 3 : 2) . ($unit ? ' ' . $unit : '') : '0' . ($unit ? ' ' . $unit : '');

            $cargos = [];
            foreach ($records as $inner) {
                $stackable = $inner->stackable ? 'On' : 'Off';
                $refOrderNum = $inner->ref_order_num ?? '';

                $cargos[] = [
                    'innerId' => $inner->id,
                    'mainCargoId' => $cargoId,
                    'refOrderNum' => $refOrderNum,
                    'innerCargo' => $inner->cargo_type,
                    'innerGd' => $inner->goods_description,
                    'innerQuantity' => $inner->quantity,
                    'innerWidth' => $uomString($inner->width, $inner->width_unit),
                    'innerHeight' => $uomString($inner->height, $inner->height_unit),
                    'innerLength' => $uomString($inner->length, $inner->length_unit),
                    'innerWeight' => $uomString($inner->weight, $inner->weight_unit),
                    'innerVolume' => $uomString($inner->volume, $inner->volume_unit),
                    'innerStackable' => $stackable,
                    'action' => ''
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Inner cargo retrieved successfully',
                'data' => $cargos
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve inner cargo: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function addItem(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'item_id' => 'required|string|max:100',
                'item_name' => 'required|string|max:100',
                'description' => 'nullable|string',
                'length' => 'nullable|numeric|min:0',
                'length_uom' => 'nullable|string|max:20',
                'width' => 'nullable|numeric|min:0',
                'width_uom' => 'nullable|string|max:20',
                'height' => 'nullable|numeric|min:0',
                'height_uom' => 'nullable|string|max:20',
                'weight' => 'nullable|numeric|min:0',
                'weight_uom' => 'nullable|string|max:20',
                'volume' => 'nullable|numeric|min:0',
                'volume_uom' => 'nullable|string|max:20',
                'volumetric_weight' => 'nullable|numeric|min:0',
                'volumetricweight_uom' => 'nullable|string|max:20',
                'second_volume' => 'nullable|numeric|min:0',
                'secondvolume_uom' => 'nullable|string|max:20',
                'second_weight' => 'nullable|numeric|min:0',
                'secondweight_uom' => 'nullable|string|max:20',
                'item_number' => 'nullable|string|max:100',
                'hsn_code' => 'nullable|string|max:50',
                'color_code' => 'nullable|string|max:50',
                'color_code_name' => 'nullable|string|max:100',
                'size_code' => 'nullable|string|max:50',
                'size_code_name' => 'nullable|string|max:100',
                'unit_price' => 'nullable|numeric|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $data = [
                'item_id' => $request->input('item_id'),
                'item_name' => $request->input('item_name'),
                'description' => $request->input('description', ''),
                'status' => 1,
                'createdby' => $request->input('user_id', Auth::user()->id ?? 0),
                'length' => $request->input('length', 0),
                'length_unit' => $request->input('length_uom', ''),
                'width' => $request->input('width', 0),
                'width_unit' => $request->input('width_uom', ''),
                'height' => $request->input('height', 0),
                'height_unit' => $request->input('height_uom', ''),
                'weight' => $request->input('weight', 0),
                'weight_unit' => $request->input('weight_uom', ''),
                'volume' => $request->input('volume', 0),
                'volume_unit' => $request->input('volume_uom', ''),
                'volumetric_weight' => $request->input('volumetric_weight', 0),
                'volweight_uom' => $request->input('volumetricweight_uom', ''),
                'second_volume' => $request->input('second_volume', 0),
                'secondvolume_uom' => $request->input('secondvolume_uom', 'cbm'),
                'second_weight' => $request->input('second_weight', 0),
                'secondweight_uom' => $request->input('secondweight_uom', 'kg'),
                'item_number' => $request->input('item_number', ''),
                'hsn_code' => $request->input('hsn_code', ''),
                'color_code' => $request->input('color_code', ''),
                'color_code_name' => $request->input('color_code_name', ''),
                'size_code' => $request->input('size_code', ''),
                'size_code_name' => $request->input('size_code_name', ''),
                'unit_price' => $request->input('unit_price', 0.00),
            ];

            // Check for existing item
            $existingItem = Item::where($data)->first();

            if ($existingItem) {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Item already exists',
                    'data' => [
                        'id' => $existingItem->id,
                        'ins' => 'upd'
                    ]
                ], 200);
            }

            // Insert new item
            $item = Item::create($data);

            return response()->json([
                'status' => 'success',
                'message' => 'Item added successfully',
                'data' => [
                    'id' => $item->id,
                    'ins' => 'ins'
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add item: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function saveInnerCargo(Request $request, $id = null)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'main_cargo_id' => 'required|integer|min:1',
                'cargo_type' => 'required|string|max:255',
                'goods_desc' => 'nullable|string|max:255',
                'quantity' => 'required|integer|min:1',
                'length' => 'nullable|numeric|min:0',
                'length_uom' => 'nullable|string|max:60',
                'width' => 'nullable|numeric|min:0',
                'width_uom' => 'nullable|string|max:60',
                'height' => 'nullable|numeric|min:0',
                'height_uom' => 'nullable|string|max:60',
                'weight' => 'nullable|numeric|min:0',
                'weight_uom' => 'nullable|string|max:60',
                'volume' => 'nullable|numeric|min:0',
                'volume_uom' => 'nullable|string|max:60',
                'stackable' => 'required|in:0,1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $mainCargoId = $request->input('main_cargo_id');
            $data = [
                'cargo_id' => $mainCargoId,
                'cargo_type' => $request->input('cargo_type'),
                'goods_description' => $request->input('goods_desc', ''),
                'quantity' => $request->input('quantity'),
                'length' => $request->input('length', 0.00),
                'length_unit' => $request->input('length_uom', ''),
                'width' => $request->input('width', 0.00),
                'width_unit' => $request->input('width_uom', ''),
                'height' => $request->input('height', 0.00),
                'height_unit' => $request->input('height_uom', ''),
                'weight' => $request->input('weight', 0.00),
                'weight_unit' => $request->input('weight_uom', ''),
                'volume' => $request->input('volume', 0.00),
                'volume_unit' => $request->input('volume_uom', ''),
                'stackable' => $request->input('stackable', 0),
            ];

            if ($id === null) {
                // Insert new inner cargo
                $data['createdby'] = $request->input('user_id', Auth::user()->id ?? 0);
                $data['created_at'] = now();
                $innerCargo = InnerCargo::create($data);
                $innerCargoId = $innerCargo->id;
                $message = 'Inner cargo added successfully';
                $statusCode = 201;
            } else {
                // Update existing inner cargo
                $innerCargo = InnerCargo::find($id);
                if (!$innerCargo) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Inner cargo not found',
                        'data' => []
                    ], 404);
                }

                // If main_cargo_id is not provided, use the existing one
                if (!$mainCargoId) {
                    $mainCargoId = $innerCargo->cargo_id;
                    $data['cargo_id'] = $mainCargoId;
                }

                $innerCargo->update($data);
                $innerCargoId = $id;
                $message = 'Inner cargo updated successfully';
                $statusCode = 200;
            }

            return response()->json([
                'status' => 'success',
                'message' => $message,
                'data' => [
                    'mainCargoId' => $mainCargoId,
                    'innerCargoId' => $innerCargoId
                ]
            ], $statusCode);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to save inner cargo: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function deleteOrderCargoDetails(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1',
                'cargo_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $orderId = $request->input('order_id');
            $cargoId = $request->input('cargo_id');

            $orderCargoDetail = OrderCargodetail::where('order_id', $orderId)->where('cargo_id', $cargoId)->first();
            if (!$orderCargoDetail) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Order cargo detail not found',
                    'data' => []
                ], 404);
            }

            $orderCargoDetail->update(['status' => 0]);
            $activeCargo = OrderCargodetail::where('order_id', $orderId)->where('status', 1)->first();
            $orderCargoId = $activeCargo ? $activeCargo->cargo_id : '';
            return response()->json([
                'status' => 'success',
                'message' => 'Order cargo detail deleted successfully',
                'data' => [
                    'orderCargoId' => $orderCargoId
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete order cargo detail: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function getOrderInvolvedParties(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $orderId = $request->input('order_id');
            $customerId = $request->input('cust_id', Auth::user()->cust_id ?? 0);

            // Get shift_id from orders
            $order = Order::where('id', $orderId)
                ->where('status', '>', 0)
                ->select('shift_id')
                ->first();

            if (!$order) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Order not found or inactive',
                    'data' => []
                ], 404);
            }

            $shiftId = $order->shift_id ?? 0;

            // Get involved parties
            $parties = OrderParty::where('order_parties.order_id', $orderId)
                ->where('order_parties.status', 1)
                ->join('sx_party_members', 'order_parties.party_id', '=', 'sx_party_members.id')
                ->join('sx_party_types', 'order_parties.party_type', '=', 'sx_party_types.id')
                ->select(
                    'sx_party_members.id as party_master_id',
                    'sx_party_members.name as username',
                    'sx_party_members.email as emailid',
                    'sx_party_members.mobile',
                    'sx_party_members.location_id as city',
                    'sx_party_members.state',
                    'sx_party_members.street',
                    'sx_party_members.pincode as zipcode',
                    'sx_party_members.country',
                    'sx_party_members.partyindetifier',
                    'sx_party_members.code',
                    'sx_party_members.fax',
                    'sx_party_types.type_name as party_name',
                    'sx_party_types.id as party_type_id',
                    'sx_party_types.org_id',
                    'sx_party_types.be_value',
                    'order_parties.id as party_id'
                )
                ->groupBy('order_parties.party_type', 'sx_party_members.id', 'sx_party_types.type_name', 'sx_party_types.id', 'order_parties.id')
                ->orderBy('sx_party_members.id', 'desc')
                ->get();

            $result = [];
            foreach ($parties as $res) {
                $partyName = $res->party_name;
                $action = "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'> <a class='dropdown-toggle' data-toggle='dropdown' href='#'><span class='icon tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'><li><a id='bEdit' type='button' class='btn btn-sm btn-default' onclick='rowPartyEdit(this, \"{$res->party_master_id}\", \"{$res->code}\", \"{$res->username}\", \"{$res->street}\", \"{$res->emailid}\", \"{$res->mobile}\", \"{$res->state}\", \"{$res->country}\", \"{$res->zipcode}\", \"{$res->fax}\", \"{$res->city}\", \"{$partyName}\", \"{$orderId}\")'><span class='glyphicon glyphicon-pencil'></span>Edit</a></li><li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deletepartydetailswithorder(\"{$res->party_master_id}\")'><span class='glyphicon glyphicon-trash'></span>Remove</a></li><li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='rowAddParty(this);'><span class='glyphicon glyphicon-plus'></span>Add Parties</a></li></ul></li></ul>";

                // Filter based on customer_id and CARRIER logic
                $includeParty = true;
                if ($customerId) {
                    if (strtoupper($partyName) === 'CARRIER') {
                        $includeParty = false;
                    }
                } else {
                    if (strtoupper($partyName) === 'CARRIER' && $shiftId == 0) {
                        $includeParty = false;
                    }
                }

                if ($includeParty) {
                    $result[] = [
                        'id' => $res->party_master_id,
                        'partyId' => $res->code,
                        'street' => $res->street,
                        'partyType' => $partyName,
                        'name' => $res->username,
                        'username' => $res->username,
                        'email' => $res->emailid,
                        'mobile' => $res->mobile,
                        'zipcode' => $res->zipcode,
                        'customerIdentifier' => $res->code,
                        'partyIdentifier' => $res->partyindetifier,
                        'fax' => $res->fax,
                        'code' => $res->code,
                        'city' => $res->city,
                        'state' => $res->state,
                        'country' => $res->country,
                        'orgId' => $res->org_id,
                        'beValue' => $res->b_value,
                        'action' => $action
                    ];
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Involved parties retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve involved parties: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function getPartyDetailsListById(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'id' => 'required|string|max:100',
                'type' => 'nullable|in:inv,consignee',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $code = $request->input('id');
            $type = $request->input('type', '');
            $user = Auth::user();
            $orgId = $request->input('org_id', $user->org_id ?? 0);
            $beValue = $request->input('be_value', $user->be_value ?? '');
            $custId = $request->input('cust_id', $user->cust_id ?? 0);
            $subCusts = $request->input('sub_cust', $user->sub_cust ?? []);

            // Prepare sub_custs array
            if ($custId !== 0) {
                if (!empty($subCusts) && is_array($subCusts)) {
                    if (!in_array($custId, $subCusts)) {
                        $subCusts[] = $custId;
                    }
                } else {
                    $subCusts = [$custId];
                }
            } else {
                $subCusts = [];
            }

            // Get customer codes
            $customerCodes = [];
            if (!empty($subCusts)) {
                $customerCodes = $this->orderProcessor->getCustomerCodeByIds($subCusts);
            }

            // Build query
            $query = SxPartyMembers::query()
                ->join('sx_party_types', 'sx_party_members.party_type', '=', 'sx_party_types.id')
                ->select(
                    'sx_party_members.id',
                    'sx_party_members.name',
                    'sx_party_members.email',
                    'sx_party_members.street',
                    'sx_party_members.location_id as city',
                    'sx_party_members.state',
                    'sx_party_members.mobile',
                    'sx_party_members.address',
                    'sx_party_members.country',
                    'sx_party_members.pincode',
                    'sx_party_members.code',
                    'sx_party_members.org_id',
                    'sx_party_members.be_value',
                    'sx_party_members.fax',
                    'sx_party_types.id as partytype_id',
                    'sx_party_types.type_name as role'
                )
                ->where('sx_party_members.code', 'like', "%{$code}%")
                ->whereNotNull('sx_party_members.code')
                ->where('sx_party_members.code', '!=', '0')
                ->where('sx_party_members.code', '!=', '')
                ->where('sx_party_members.org_id', $orgId);

            if (!empty($customerCodes)) {
                $query->whereIn('sx_party_members.customer_code', $customerCodes);
            }

            $parties = $query->groupBy('sx_party_members.id', 'sx_party_types.id')
                ->orderBy('sx_party_members.id', 'desc')
                ->get();

            if ($parties->isEmpty()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No parties found',
                    'data' => []
                ], 404);
            }

            $result = [];
            foreach ($parties as $res) {
                $check = $type === 'inv'
                    ? "<input type='radio' name='partylist' id='partylist_{$res->id}' class='partylist' onchange='selectparty({$res->id})' value='{$res->id}'>"
                    : "<input type='radio' name='consigneelist' id='consigneelist_{$res->id}' class='consigneelist' onchange='selectparty({$res->id})' value='{$res->id}'>";

                $result[] = [
                    'check' => $check,
                    'id' => $res->code,
                    'name' => $res->name,
                    'email' => $res->email,
                    'mobile' => $res->mobile,
                    'partyName' => $res->role,
                    'companyCode' => $res->org_id,
                    'branchCode' => $res->be_value,
                    'city' => $res->city,
                    'country' => $res->country,
                    'street' => $res->street
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Party details retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve party details: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function addInvolvedPartyForOrder(Request $request, $order_id = null)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'party_id' => 'required|string|max:100',
                'party_name' => 'required|string|max:100',
                'street' => 'nullable|string|max:255',
                'zipcode' => 'nullable|string|max:20',
                'country' => 'nullable|string|max:100',
                'state' => 'nullable|string|max:100',
                'mobile' => 'nullable|string|max:20',
                'fax' => 'nullable|string|max:50',
                'email' => 'nullable|email|max:100',
                'role' => 'required|string|max:100',
                'city' => 'nullable|string|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $user = Auth::user();
            $user_id = $user->id ?? $request->input('user_id', 0);
            $org_id = $request->input('party_org_id', $user->org_id ?? 0);
            $be_value = $request->input('party_be_value_id', $user->be_value ?? 0);

            if (!$user_id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User authentication required',
                    'data' => []
                ], 401);
            }

            // Begin transaction
            DB::beginTransaction();

            // Prepare party data
            $address = implode(',', array_filter([
                $request->input('street', ''),
                $request->input('city', ''),
                $request->input('city', '')
            ]));

            $partyData = [
                'customeridentifier' => $request->input('party_id'),
                'code' => $request->input('party_id'),
                'name' => $request->input('party_name'),
                'street' => $request->input('street', ''),
                'pincode' => $request->input('zipcode', ''),
                'country' => $request->input('country', ''),
                'state' => $request->input('state', ''),
                'mobile' => $request->input('mobile', ''),
                'fax' => $request->input('fax', ''),
                'email' => $request->input('email', ''),
                'created_on' => now(),
                'address' => $address,
                'org_id' => $org_id,
                'be_value' => $be_value,
                'location_id' => $request->input('city', ''),
                'party_type' => 0
            ];

            // Get or create role_id
            $role_id = 0;
            $role = $request->input('role');
            if ($role) {
                $partyType = SxPartyTypes::where([
                    'type_name' => $role,
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'status' => 1
                ])->first();

                if (!$partyType) {
                    $partyType = SxPartyTypes::where([
                        'type_name' => $role,
                        'org_id' => $org_id,
                        'status' => 1
                    ])->first();
                }

                if (!$partyType) {
                    $partyType = SxPartyTypes::create([
                        'type_name' => $role,
                        'description' => $role,
                        'org_id' => $org_id,
                        'be_value' => $be_value,
                        'status' => 1,
                        'created_on' => now()
                    ]);
                }

                $role_id = $partyType->id;
            }

            $partyData['party_type_id'] = $role_id;

            // Check for existing party
            $party = SxPartyMembers::where('code', $request->input('party_id'))->first();
            if ($party) {
                $party->update($partyData);
                $party_id = $party->id;
            } else {
                $party = SxPartyMembers::create($partyData);
                $party_id = $party->id;
            }

            $result = [
                'partyId' => $request->input('party_id'),
                'partyType' => SxPartyTypes::where('id', $role_id)->value('type_name') ?? '',
                'name' => $request->input('party_name'),
                'street' => $request->input('street', ''),
                'zipcode' => $request->input('zipcode', ''),
                'city' => $request->input('city', ''),
                'country' => $request->input('country', ''),
                'state' => $request->input('state', ''),
                'mobile' => $request->input('mobile', ''),
                'fax' => $request->input('fax', ''),
                'emailid' => $request->input('email', ''),
                'action' => "<button id='{$request->input('party_id')}' class='btn btn-primary btn-xs editparties' onclick='editpartydetails(\"{$request->input('party_id')}\",event)'><small><i class='glyphicon glyphicon-pencil'></i></small></button> <button id='{$request->input('party_id')}' class='btn btn-primary btn-xs deleteround' onclick='deletepartydetails(\"{$request->input('party_id')}\",event)'><small><i class='glyphicon glyphicon-trash'></i></small></button>"
            ];

            if ($order_id) {
                $order = Order::where('id', $order_id)->first();
                if (!$order) {
                    DB::rollBack();
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Order not found',
                        'data' => []
                    ], 404);
                }

                $orderParty = OrderParty::where([
                    'party_id' => $party_id,
                    'order_id' => $order_id
                ])->first();

                if ($orderParty) {
                    if ($orderParty->status == 1) {
                        DB::rollBack();
                        return response()->json([
                            'status' => 'success',
                            'message' => 'Party already associated with order',
                            'data' => array_merge($result, ['resultCode' => 2])
                        ], 200);
                    } else {
                        $orderParty->update(['status' => 1]);
                        if ($role_id) {
                            $orderParty->update(['party_type' => $role_id]);
                        }
                    }
                } else {
                    $party_type = $role_id ?: ($party->party_type ?? 1);
                    OrderParty::create([
                        'party_id' => $party_id,
                        'order_id' => $order_id,
                        'created_at' => now(),
                        'status' => 1,
                        'party_type' => $party_type,
                        'order_number' => $order->order_number,
                        'user_id' => $user_id
                    ]);
                }

                $result['resultCode'] = 1;
            } else {
                $result['resultCode'] = 0;
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => $order_id ? 'Party associated with order successfully' : 'Party added successfully',
                'data' => $result
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add involved party: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function deleteOrderPartyDetails(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1',
                'party_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => ['resultCode' => 0]
                ], 422);
            }

            $order_id = $request->input('order_id');
            $party_id = $request->input('party_id');

            // Check for existing order party
            $orderParty = OrderParty::where([
                'order_id' => $order_id,
                'party_id' => $party_id,
                'status' => 1
            ])->first();

            if (!$orderParty) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Order party not found or already inactive',
                    'data' => ['resultCode' => 0]
                ], 404);
            }

            // Soft delete by updating status to 0
            $orderParty->update(['status' => 0]);

            return response()->json([
                'status' => 'success',
                'message' => 'Order party deleted successfully',
                'data' => ['resultCode' => 1]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete order party: ' . $e->getMessage(),
                'data' => ['resultCode' => 0]
            ], 500);
        }
    }

    public function getOtherReferenceDetails(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'nullable|integer|min:1|exists:orders,id',
                'type' => 'nullable|string|in:popup,other',
                'ref_ids' => 'nullable|array',
                'ref_ids.*' => 'integer|min:1|exists:reference_master,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $user = Auth::user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized: User not authenticated',
            //         'data' => null
            //     ], 401);
            // }

            $order_id = $request->input('order_id');
            $type = $request->input('type', '');
            $ref_ids = $request->input('ref_ids', []);

            $references = [];

            if ($order_id) {
                $query = ReferenceMaster::select([
                    'reference_master.id',
                    'reference_master.name',
                    'reference_master.description',
                    'order_references.order_shortNo',
                    'order_references.id as order_ref_id',
                    'order_references.ref_value as order_value'
                ])
                    ->join('order_references', 'reference_master.name', '=', 'order_references.reference_id')
                    ->where('order_references.order_id', $order_id)
                    ->where('reference_master.status', 1)
                    ->where('order_references.status', 1)
                    ->groupBy('order_references.id', 'reference_master.id')
                    ->orderBy('reference_master.created_at', 'desc');
            } elseif (!empty($ref_ids)) {
                $query = ReferenceMaster::select([
                    'reference_master.id',
                    'reference_master.name',
                    'reference_master.description',
                    'order_references.order_shortNo',
                    'order_references.id as order_ref_id',
                    'order_references.ref_value as order_value'
                ])
                    ->join('order_references', 'reference_master.name', '=', 'order_references.reference_id')
                    ->whereIn('reference_master.id', $ref_ids)
                    ->where('reference_master.status', 1)
                    ->where('order_references.status', 1)
                    ->groupBy('reference_master.id', 'reference_master.id')
                    ->orderBy('reference_master.created_at', 'desc');
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'No references found',
                    'data' => []
                ], 200);
            }

            $results = $query->get();

            foreach ($results as $res) {
                $res_val = $res->order_value;
                if ($res_val) {
                    try {
                        $parsed_date = Carbon::createFromFormat('Y-m-d H:i:s', $res_val, 'UTC');
                        if ($parsed_date !== false) {
                            $timezone = $user->timezone ?? config('app.timezone', 'UTC');
                            $res_val = $parsed_date->timezone($timezone)->toDateTimeString();
                        }
                    } catch (\Exception $e) {
                        // If parsing fails, keep the original value
                    }
                }

                $references[] = [
                    'id' => $res->name,
                    'name' => $res->description,
                    'value' => $res_val,
                    'orderShortNo' => $res->order_shortNo,
                    'orderRefId' => $res->order_ref_id
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Reference details retrieved successfully',
                'data' => $references
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve reference details: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function triporderintoshipment(Request $request)
    {
        $user = Auth::user();
        $userId = $user->id ?? $request->input('user_id') ?? 0;
        $orgId = $user->org_id ?? $request->input('org_id') ?? 0;
        $beValue = $user->be_value ?? $request->input('be_value') ?? '';
        $curtz = $user->usr_tzone['timezone'] ?? $request->input('curtz') ?? 'Asia/Singapore';
        $curdt = now()->setTimezone($curtz)->format('Y-m-d H:i:s');

        $validator = Validator::make($request->all(), [
            'user_id' => 'integer',
            'org_id' => 'integer',
            'be_value' => 'integer',
            'ordid' => 'required|string',
            'carrierid' => 'required|integer',
            'veh_route_plan_flag' => 'nullable|in:0,1',
            'no_of_vehicles' => 'nullable|integer',
            'dimension_type' => 'nullable|string',
            'trip_type' => 'nullable|string|in:single,multi',
            'veh_typenum' => 'nullable|string',
            'tdrivernum' => 'nullable|string',
            'truck_start_time' => 'nullable|date_format:Y-m-d H:i:s',
            'max_distance' => 'nullable|numeric',
            'truck_cost_per_hour' => 'nullable|numeric',
            'truck_cost_per_kilometer' => 'nullable|numeric',
            'vehicle_type' => 'nullable|string',
            'carrier_instructions' => 'nullable|string',
            'weight_capacity' => 'nullable|string',
            'volume_capacity' => 'nullable|string',
            'additional_conditions' => 'nullable|string',
            'temperature_regime' => 'nullable|string',
            'traffic_checkbox' => 'nullable|in:0,1',
            'time_for_loading_penality_rate' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => ['validationErrors' => $validator->errors()]
            ], 422);
        }

        $input = $request->all();
        $input['user_id'] = $userId;
        $input['org_id'] = $orgId;
        $input['be_value'] = $beValue;
        $input['curtz'] = $curtz;
        $input['curdt'] = $curdt;

        $orderIds = explode(',', $input['ordid']);
        Log::error('orderIds::' . json_encode($orderIds));

        $response = 0;
        if (count($orderIds) > 1) {
            $response = $this->tripCreateFromOrders->tripcreatemultiorder($input);
        } else {
            $response = $this->tripCreateFromOrders->eachordercreatetrip($input);
        }

        $this->rateManagement->addrecodfortripinsertion($input);

        $data = ['vroId' => $response];

        if ($response > 0) {
            if (
                !empty($input['no_of_vehicles']) &&
                !empty($input['dimension_type']) &&
                isset($input['veh_route_plan_flag']) &&
                $input['veh_route_plan_flag'] == 1 &&
                $input['trip_type'] === 'multi'
            ) {
                $vroId = $response;
                if ($vroId == 0) {
                    $vroResult = DB::table('vro_orders')
                        ->select('id')
                        ->where('status', 1)
                        ->whereRaw('FIND_IN_SET(?, order_ids)', [$input['ordid']])
                        ->first();
                    if ($vroResult) {
                        $vroId = $vroResult->id;
                        $data['vroId'] = $vroId;
                    }
                }
                if ($vroId > 0) {
                    $data['redirectUrl'] = url('vehiclerouteoptimize/newTrip/' . $vroId);
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Trip created successfully, redirect URL provided for vehicle route optimization',
                        'data' => $data
                    ], 200);
                }
            }
            return response()->json([
                'status' => 'success',
                'message' => 'Trip created successfully',
                'data' => $data
            ], 200);
        } elseif ($response == 2) {
            return response()->json([
                'status' => 'error',
                'message' => 'Selected orders are not in the same route (e.g., Ref. ID (ROT))',
                'data' => []
            ], 422);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Failed to create trip',
            'data' => []
        ], 500);
    }

    public function deleteOrder(int $id)
    {
        try {
            if ($id <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid order ID',
                    'data' => []
                ], 422);
            }

            $deleteOrder = $this->orderProcessor->deleteOrderById($id);
            if ($deleteOrder) {
                $this->orderProcessor->deleteForOrder($id, ['DQ']);
                return response()->json([
                    'status' => 'success',
                    'message' => 'Order deleted successfully',
                    'data' => ['orderId' => $id]
                ], 200);
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete order',
                'data' => []
            ], 500);
        } catch (\Exception $e) {
            Log::error('DeleteOrder Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while deleting the order',
                'data' => []
            ], 500);
        }
    }

    public function getProductMasterData(Request $request)
    {
        try {
            $validated = $request->validate([
                'order_date' => 'nullable|date_format:Y-m-d',
                'less_date' => 'nullable|date_format:Y-m-d',
                'org_id' => 'integer|min:1',
                'be_value' => 'integer|min:1',
                'user_id' => 'integer|min:1',
            ]);
            $user = Auth::user();
            $orgId = $user->org_id ?? $validated['org_id'];
            $userId = $user->user_id ?? $validated['user_id'];

            $products = $this->orderProcessor->getProductMasterData(
                $validated['order_date'] ?? '',
                $validated['less_date'] ?? '',
                $orgId,
                $validated['be_value']
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Product master data retrieved successfully',
                'data' => [
                    'productData' => $products
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetProductMasterData Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetProductMasterData Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving product master data',
                'data' => []
            ], 500);
        }
    }

    public function getOrderStatus(Request $request)
    {
        try {
            $validated = $request->validate([
                'org_id' => 'integer|min:1',
                'be_value' => 'integer|min:1',
                'user_id' => 'integer|min:1',
            ]);

            $user = Auth::user();
            $orgId = $user->org_id ?? $validated['org_id'];
            $userId = $user->user_id ?? $validated['user_id'];

            $orderStatuses = $this->orderProcessor->getOrderStatus();

            return response()->json([
                'status' => 'success',
                'message' => 'Order statuses retrieved successfully',
                'data' => [
                    'orderStatusData' => $orderStatuses
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetOrderStatus Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetOrderStatus Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving order statuses',
                'data' => []
            ], 500);
        }
    }

    public function getServiceMasterData(Request $request)
    {
        try {
            $validated = $request->validate([
                'order_date' => 'nullable|date_format:Y-m-d',
                'less_date' => 'nullable|date_format:Y-m-d',
                'org_id' => 'integer|min:1',
                'be_value' => 'integer|min:1',
                'user_id' => 'integer|min:1',
            ]);

            $user = Auth::user();
            $orgId = $user->org_id ?? $validated['org_id'];
            $userId = $user->user_id ?? $validated['user_id'];

            $services = $this->orderProcessor->getServiceMasterData(
                $validated['order_date'] ?? '',
                $validated['less_date'] ?? '',
                $orgId,
                $validated['be_value']
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Service master data retrieved successfully',
                'data' => [
                    'serviceData' => $services
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetServiceMasterData Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetServiceMasterData Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving service master data',
                'data' => []
            ], 500);
        }
    }

    public function getTransportMode(Request $request)
    {
        try {
            $validated = $request->validate([
                'order_date' => 'nullable|date_format:Y-m-d',
                'less_date' => 'nullable|date_format:Y-m-d',
                'org_id' => 'integer|min:1',
                'be_value' => 'integer|min:1',
                'user_id' => 'integer|min:1',
            ]);

            $user = Auth::user();
            $orgId = $user->org_id ?? $validated['org_id'];
            $beValue = $user->be_value ?? $validated['be_value'];
            $userId = $user->id ?? $validated['user_id'];

            $transportMode = new TransportMode();
            $transportModes = $transportMode->getTransportMode($validated['order_date'] ?? '', $validated['less_date'] ?? '', $orgId, $beValue);

            return response()->json([
                'status' => 'success',
                'message' => 'Transport mode data retrieved successfully',
                'data' => [
                    'transportModeData' => $transportModes
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetTransportMode Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetTransportMode Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving transport mode data',
                'data' => []
            ], 500);
        }
    }

    public function getShipmentTypes(Request $request)
    {
        try {
            $validated = $request->validate([
                'org_id' => 'integer|min:1',
                'be_value' => 'integer|min:1',
                'user_id' => 'integer|min:1',
            ]);

            $user = Auth::user();
            $orgId = $user->org_id ?? $validated['org_id'] ?? 0;
            $beValue = $validated['be_value'] ?? 0;

            $shipmentTypes = $this->orderProcessor->getShipmentTypes($orgId, $beValue);

            return response()->json([
                'status' => 'success',
                'message' => 'Shipment types retrieved successfully',
                'data' => [
                    'shipmentTypeData' => $shipmentTypes
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetShipmentTypes Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetShipmentTypes Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving shipment types',
                'data' => []
            ], 500);
        }
    }

    public function getRegions(Request $request)
    {
        try {
            $validated = $request->validate([
                'org_id' => 'integer|min:1',
                'be_value' => 'integer|min:1',
                'user_id' => 'integer|min:1',
            ]);

            $user = Auth::user();
            $orgId = $user->org_id ?? $validated['org_id'] ?? 0;
            $beValue = $validated['be_value'] ?? 0;

            $regions = $this->orderProcessor->getRegions($orgId, $beValue);

            return response()->json([
                'status' => 'success',
                'message' => 'Regions retrieved successfully',
                'data' => [
                    'regionData' => $regions
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetRegions Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetRegions Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving regions',
                'data' => []
            ], 500);
        }
    }

    public function viewOrder(Request $request, $id)
    {
        try {
            $validated = $request->validate([
                'id' => 'required|integer|min:1',
                'urlString' => 'nullable|string',
            ]);

            $user = Auth::user();
            $orgId = $user->org_id ?? 0;
            $userId = $user->id ?? 0;
            $currency = $user->timezone['currency'] ?? 'USD';

            $data = $this->orderProcessor->viewOrder($id, $request->query('urlString'), $orgId, $userId, $currency);

            return response()->json([
                'status' => 'success',
                'message' => 'Order details retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('ViewOrder Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('ViewOrder Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving order details',
                'data' => []
            ], 500);
        }
    }

    public function copyOrder(Request $request, $id)
    {
        try {
            $validated = $request->validate([
                'id' => 'required|integer|min:1',
            ]);

            $user = Auth::user();
            $orgId = $user->org_id ?? 0;
            $userId = $user->id ?? 0;
            $currency = $user->timezone['currency'] ?? 'USD';

            $data = $this->orderProcessor->copyOrder($id, $orgId, $userId, $currency);

            return response()->json([
                'status' => 'success',
                'message' => 'Order details for copying retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('CopyOrder Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('CopyOrder Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving order details for copying',
                'data' => []
            ], 500);
        }
    }

    public function cancelOrder(Request $request, $id)
    {
        try {
            $validated = $request->validate([
                'id' => 'required|integer|min:1',
            ]);

            $user = Auth::user();
            $orgId = $user->org_id ?? 0;
            $userId = $user->id ?? 0;
            $timezone = $user->timezone['timezone'] ?? 'UTC';
            $countryHours = $user->timezone['hrs'] ?? '+00:00';

            $this->orderProcessor->cancelOrder($id, $orgId, $userId, $timezone, $countryHours);

            return response()->json([
                'status' => 'success',
                'message' => 'Order cancelled successfully',
                'data' => []
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('CancelOrder Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('CancelOrder Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error cancelling order',
                'data' => []
            ], 500);
        }
    }

    public function checkTrip(Request $request)
    {
        try {
            // $validated = $request->validate([
            //     'order_id' => 'required|integer|min:1|array',
            //     'order_id.*' => 'integer|min:1',
            // ]);

            $orderId = $request->input('order_id');
            $user = Auth::user();
            $userId = $user->id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;

            $result = $this->orderProcessor->checkTrip($orderId, $userId, $orgId, $beValue);

            return response()->json([
                'status' => 'success',
                'message' => 'Trip check completed',
                'data' => $result
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('CheckTrip Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('CheckTrip Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error checking trip',
                'data' => []
            ], 500);
        }
    }

    public function allocationRulePriority(Request $request)
    {
        try {
            $user = Auth::user();
            $userId = $user->id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;

            $allocationRules = [
                'Cost Efficiency',
                'Performance Rating',
                'Nearby Locations',
                'Formula',
            ];

            return response()->json([
                'status' => 'success',
                'message' => 'Allocation rule priorities retrieved',
                'data' => [
                    'allocationRules' => $allocationRules,
                ]
            ], 200);
        } catch (\Exception $e) {
            Log::error('AllocationRulePriority Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving allocation rule priorities',
                'data' => []
            ], 500);
        }
    }

    public function getDriverId(Request $request, $id)
    {
        try {
            $validated = $request->validate([
                'id' => 'required|integer|min:1',
            ]);

            $user = Auth::user();
            $userId = $user->id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;

            $contactNumber = $this->orderProcessor->getDriverId($id);

            return response()->json([
                'status' => 'success',
                'message' => 'Driver contact number retrieved successfully',
                'data' => [
                    'contactNumber' => $contactNumber
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetDriverId Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetDriverId Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving driver contact number',
                'data' => []
            ], 500);
        }
    }

    public function addReferenceDetails(Request $request)
    {
        try {
            $validated = $request->validate([
                'reference_id' => 'required|string',
                'reference_name' => 'required|string',
                'reference_value' => 'required|string',
                'ref_row_id' => 'nullable|string',
                'order_id' => 'nullable|string',
                'order_ref_id' => 'nullable|string',
            ]);

            $referenceId = $request->input('reference_id');
            $referenceName = $request->input('reference_name');
            $referenceValue = $request->input('reference_value');
            $refRowId = $request->input('ref_row_id', '');
            $orderId = $request->input('order_id', '0');
            $orderRefId = $request->input('order_ref_id', '0');

            $user = Auth::user();
            $userId = $user->id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;
            $timezone = $user->timezone['timezone'] ?? 'UTC';

            $result = $this->orderProcessor->addReferenceDetails($referenceId, $referenceName, $referenceValue, $refRowId, $orderId, $orgId, $beValue, $timezone);

            return response()->json([
                'status' => 'success',
                'message' => 'Reference details added successfully',
                'data' => $result
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('AddReferenceDetails Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('AddReferenceDetails Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error adding reference details',
                'data' => []
            ], 500);
        }
    }

    public function deleteOrderReferenceDetails(Request $request)
    {
        try {
            $validated = $request->validate([
                'order_id' => 'required|string',
                'ref_id' => 'required|string',
            ]);

            $orderId = $request->input('order_id', '0');
            $refId = $request->input('ref_id', '0');

            $user = Auth::user();
            $userId = $user->id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;

            $success = $this->orderProcessor->deleteOrderReferenceDetails($orderId, $refId);

            return response()->json([
                'status' => 'success',
                'message' => $success ? 'Order reference deleted successfully' : 'No matching order reference found',
                'data' => [
                    'success' => $success
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('DeleteOrderReferenceDetails Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('DeleteOrderReferenceDetails Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error deleting order reference',
                'data' => []
            ], 500);
        }
    }
}
