<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\AccountMaster;
use App\Models\Transaction;
use App\Models\TrucksData;
use App\Models\Trip;
use App\Models\Shipment;
use App\Models\TruckDriver;
use App\Models\TripExpenseDoc;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class TripExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $user_id = $user->id ?? 1;
        $org_id = $user->org_id ?? 1; // Replaced company_code with org_id

        // Base query
        $query = Transaction::select(
            'transactions.id',
            'shipment.shipmentid',
            'trucks_data.register_number',
            'account_master.act_name',
            'truck_drivers.name',
            'transactions.halting_days',
            'transactions.halting_revenue',
            'transactions.amount',
            'transactions.txn_date',
            'transactions.payable_to',
            'trucks_data.register_number as vehicle_ref'
        )
            ->join('account_master', 'account_master.id', '=', 'transactions.act_id')
            ->join('trucks_data', 'transactions.vehicle_id', '=', 'trucks_data.id')
            ->join('trips', 'transactions.trip_id', '=', 'trips.id')
            ->join('shipment', 'trips.shift_id', '=', 'shipment.id')
            ->leftJoin('truck_drivers', 'transactions.driver_id', '=', 'truck_drivers.id')
            ->where('trucks_data.user_id', $user_id)
            ->where('transactions.status', 1)
            ->where('transactions.org_id', $org_id);

        // Search filters
        if ($request->has('searchsubmit')) {
            if ($shipmentid = $request->input('shipmentid')) {
                $query->where('shipment.shipmentid', 'like', '%' . $shipmentid . '%');
            }
            if ($register_number = $request->input('register_number')) {
                $query->where('trucks_data.register_number', 'like', '%' . $register_number . '%');
            }
            if ($driver_name = $request->input('driver_name')) {
                $query->where('truck_drivers.name', 'like', '%' . $driver_name . '%');
            }
        }

        if ($request->has('searchsubmitadv')) {
            if ($expense_type = $request->input('expense_type')) {
                $query->where('account_master.act_name', 'like', '%' . $expense_type . '%');
            }
            if ($halting_days = $request->input('halting_days')) {
                $query->where('transactions.halting_days', 'like', '%' . $halting_days . '%');
            }
            if ($halting_revenue = $request->input('halting_revenue')) {
                $query->where('transactions.halting_revenue', 'like', '%' . $halting_revenue . '%');
            }
            if ($amount = $request->input('amount')) {
                $query->where('transactions.amount', 'like', '%' . $amount . '%');
            }
        }

        // Order by latest transactions
        $transactions = $query->orderBy('transactions.id', 'desc')->get();

        // Datatable headers (for frontend compatibility)
        $datatable_headers = [
            'Trip ID',
            'Vehicle No.',
            'Expense Type',
            'Driver',
            'Halting Days',
            'Halting Revenue',
            'Amount',
            'Date'
        ];

        // Fetch datatable settings
        $settings = DB::table('datatable_settings')
            ->where('controller_name', 'TripExpenseController')
            ->where('method_name', 'index')
            ->where('org_id', $org_id)
            ->first();

        $datatable_header_sequence = [];
        $datatable_header_toggle = [];
        $datatable_header_sequence_index = [0, 1, 2, 3, 4, 5, 6, 7];
        $column_visibility = array_fill(0, count($datatable_headers), true);

        if ($settings) {
            if ($settings->sequence_data) {
                $datatable_header_sequence = unserialize($settings->sequence_data);
                $datatable_header_sequence_index = [0, 1];
                foreach ($datatable_header_sequence as $value) {
                    $index = array_search($value, $datatable_headers);
                    if ($index !== false) {
                        $datatable_header_sequence_index[] = $index + 2; // Offset for checkbox and actions
                    }
                }
                $datatable_header_sequence_index = array_filter($datatable_header_sequence_index);
                $datatable_header_sequence_index = array_values($datatable_header_sequence_index);
                foreach ($datatable_headers as $key => $header) {
                    $index = $key + 2;
                    if (!in_array($index, $datatable_header_sequence_index)) {
                        $datatable_header_sequence_index[] = $index;
                    }
                }
            }

            if ($settings->toggle_data) {
                $datatable_header_toggle = unserialize($settings->toggle_data);
                $column_visibility = array_map(function ($item) use ($datatable_header_toggle) {
                    return in_array($item, $datatable_header_toggle);
                }, $datatable_headers);
            }
        }

        return response()->json([
            'transactions' => $transactions,
            'datatable_headers' => $datatable_headers,
            'datatable_header_sequence' => $datatable_header_sequence,
            'datatable_header_toggle' => $datatable_header_toggle,
            'datatable_header_sequence_index' => $datatable_header_sequence_index,
            'column_visibility' => $column_visibility,
        ]);
    }

    /**
     * Get list of active trucks for the logged-in user.
     */
    public function getTrucks()
    {
        $user = Auth::user();
        $user_id = $user->id;

        $trucks = TrucksData::where('user_id', $user_id)
            ->where('status', 1)
            ->orderBy('id', 'desc')
            ->limit(200)
            ->get(['id', 'register_number']);

        return response()->json([
            'trucks' => $trucks,
        ]);
    }

    /**
     * Get list of active expense accounts for the user's organization.
     */
    public function getExpenses()
    {
        $user = Auth::user();
        $org_id = $user->org_id;

        $expenses = AccountMaster::where('org_id', $org_id)
            ->where('status', 1)
            ->get(['id', 'act_name']);

        return response()->json([
            'expenses' => $expenses,
        ]);
    }



    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'vehicle_id' => 'required|exists:trucks_data,id',
            'trip_id' => 'required|exists:trips,id',
            'expense_type.*' => 'required|exists:account_master,id',
            'expense_price.*' => 'required|numeric|min:0',
            'txn_date.*' => 'nullable|date'/* ,
            'expense_doc.*' => 'nullable|file|mimes:png,jpg,jpeg|max:2048', */
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $user_id = $user->id ?? 1;
        $org_id = $user->org_id ?? 1;
        $currentDate = now()->toDateTimeString();

        $input = $request->all();
        $expense_types = $input['expense_type'] ?? [];
        // $files = $request->file('expense_doc') ?? [];

        if (empty($expense_types)) {
            return response()->json(['error' => 'No expense types provided!'], 422);
        }

        DB::beginTransaction();
        try {
            foreach ($expense_types as $i => $expense_type) {
                if (empty($input['expense_price'][$i]) || $input['expense_price'][$i] <= 0) {
                    continue;
                }

                $transaction = Transaction::create([
                    'trip_id' => $input['trip_id'],
                    'driver_id' => $this->getDriverByTrip($input['trip_id']),
                    'vehicle_id' => $input['vehicle_id'],
                    'act_id' => $expense_type,
                    'amount' => $input['expense_price'][$i],
                    'txn_date' => !empty($input['txn_date'][$i])
                        ? date('Y-m-d', strtotime($input['txn_date'][$i]))
                        : now()->toDateString(),
                    'description' => $input['description'][$i] ?? '',
                    'txn_id' => (string) time(),
                    'receipt_no' => (string) time(),
                    'approval' => 'Yes',
                    'status' => 1,
                    'created_at' => $currentDate,
                    'payment_method' => 'Offline',
                    'paid_by' => 'Driver',
                    'org_id' => $org_id,
                    'user_id' => $user_id,
                    'halting_days' => $input['halting_days'] ?? '',
                    'halting_revenue' => $input['halting_revenue'] ?? '',
                ]);

                /* if (isset($files[$i]) && $files[$i]->isValid()) {
                    $filename = date('YmdHis') . '-subacnt' . $i . $expense_type . '.' . $files[$i]->getClientOriginalExtension();
                    $path = $files[$i]->storeAs('poduploads', $filename, 'public');

                    TripExpenseDoc::create([
                        'file_name' => $filename,
                        'txn_id' => $transaction->id,
                        'pickup_date' => $input['pickup_date'] ?? '',
                        'delivery_date' => $input['delivery_date'] ?? '',
                        'drivername' => $input['driver'] ?? '',
                        'status' => 1,
                        'created_at' => $currentDate,
                    ]);
                } */
            }

            DB::commit();
            return response()->json(['status' => 'success', 'message' => 'Expense payment added successfully!'], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Expense Storage Error: ' . $e->getMessage());
            return response()->json(['error' => 'Something went wrong!'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = Auth::user();
        $org_id = $user->org_id ?? 1;

        $expense = Transaction::select(
            'account_master.act_name',
            'transactions.id',
            'transactions.amount',
            'transactions.txn_id',
            'transactions.txn_date',
            'transactions.halting_days',
            'transactions.halting_revenue',
            'trucks_data.register_number',
            'shipment.shipmentid',
            'truck_drivers.name',
            'transactions.description',
            'tripexpense_docs.pickup_date',
            'tripexpense_docs.delivery_date',
            'tripexpense_docs.file_name'
        )
            ->join('account_master', 'account_master.id', '=', 'transactions.act_id')
            ->join('trucks_data', 'transactions.vehicle_id', '=', 'trucks_data.id')
            ->join('trips', 'transactions.trip_id', '=', 'trips.id')
            ->join('shipment', 'trips.shift_id', '=', 'shipment.id')
            ->leftJoin('truck_drivers', 'transactions.driver_id', '=', 'truck_drivers.id')
            ->leftJoin('tripexpense_docs', 'transactions.id', '=', 'tripexpense_docs.txn_id')
            ->where('transactions.id', $id)
            ->where('transactions.status', 1)
            ->where('transactions.org_id', $org_id)
            ->first();

        if (!$expense) {
            return response()->json(['error' => 'Expense not found!'], 404);
        }

        return response()->json(['status' => 'success', 'expense' => $expense]);
    }

    /**
     * Show the form for editing the specified trip expense.
     */
    public function edit($id)
    {
        $user = Auth::user();
        $user_id = $user->id ?? 1;
        $org_id = $user->org_id ?? 1;

        // Fetch the transaction
        $transaction = Transaction::select(
            'transactions.id',
            'transactions.halting_days',
            'transactions.halting_revenue',
            'transactions.amount as expense_price',
            'transactions.txn_date',
            'transactions.description',
            'transactions.vehicle_id',
            'transactions.trip_id',
            'trucks_data.id as veh_id',
            'trucks_data.register_number as vehicle_ref',
            'trucks_data.register_number',
            'truck_drivers.id as drive_id',
            'truck_drivers.name as driver',
            'tripexpense_docs.pickup_date',
            'tripexpense_docs.delivery_date',
            'tripexpense_docs.file_name',
            'transactions.act_id'
        )
            ->join('trucks_data', 'transactions.vehicle_id', '=', 'trucks_data.id')
            ->join('truck_drivers', 'transactions.driver_id', '=', 'truck_drivers.id')
            ->leftJoin('tripexpense_docs', 'transactions.id', '=', 'tripexpense_docs.txn_id')
            ->where('transactions.id', $id)
            ->where('transactions.org_id', $org_id)
            ->first();

        if (!$transaction) {
            return response()->json(['error' => 'Transaction not found!'], 404);
        }

        // Fetch trips for the vehicle
        $trips = Trip::select(
            'trips.id',
            'shipment.shipmentid',
            'trips.vehicle_id',
            'trips.driver_id',
            'trips.created_on'
        )
            ->join('shipment', 'trips.shift_id', '=', 'shipment.id')
            ->join('trucks_data', 'trips.vehicle_id', '=', 'trucks_data.id')
            ->where('trucks_data.id', $transaction->vehicle_id)
            ->get();

        return response()->json([
            'type' => 'edit',
            'transaction' => $transaction,
            'trips' => $trips,
        ]);
    }

    /**
     * Update the specified trip expense in storage.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'vehicle_id' => 'required|exists:trucks_data,id',
            'trip_id' => 'required|exists:trips,id',
            'expense_type.0' => 'required|exists:account_master,id',
            'expense_price.0' => 'required|numeric|min:0',
            'txn_date.0' => 'nullable|date'/* ,
            'expense_doc.0' => 'nullable|file|mimes:png,jpg,jpeg|max:2048', */
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $user_id = $user->id ?? 1;
        $org_id = $user->org_id ?? 1;
        $currentDate = now()->toDateTimeString();

        $input = $request->all();
        $expense_type = $input['expense_type'][0] ?? null;

        if (!$expense_type) {
            return response()->json(['error' => 'Expense type is required!'], 422);
        }

        DB::beginTransaction();
        try {
            $transaction = Transaction::where('id', $id)->where('org_id', $org_id)->first();
            if (!$transaction) {
                return response()->json(['error' => 'Transaction not found!'], 404);
            }

            $transaction->update([
                'trip_id' => $input['trip_id'],
                'driver_id' => $this->getDriverByTrip($input['trip_id']),
                'vehicle_id' => $input['vehicle_id'],
                'act_id' => $expense_type,
                'amount' => $input['expense_price'][0] ?? 0,
                'txn_date' => !empty($input['txn_date'][0])
                    ? date('Y-m-d', strtotime($input['txn_date'][0]))
                    : now()->toDateString(),
                'description' => $input['description'][0] ?? '',
                'approval' => 'Yes',
                'status' => 1,
                'updated_at' => $currentDate,
                'payment_method' => 'Offline',
                'paid_by' => 'Driver',
                'org_id' => $org_id,
                'user_id' => $user_id,
                'halting_days' => $input['halting_days'] ?? '',
                'halting_revenue' => $input['halting_revenue'] ?? '',
            ]);

            /* $files = $request->file('expense_doc') ?? [];
            if (isset($files[0]) && $files[0]->isValid()) {
                $filename = date('YmdHis') . '-subacnt0' . $expense_type . '.' . $files[0]->getClientOriginalExtension();
                $path = $files[0]->storeAs('poduploads', $filename, 'public');

                $existingDoc = TripExpenseDoc::where('txn_id', $id)->first();
                if ($existingDoc) {
                    // Delete old file if it exists
                    if (Storage::disk('public')->exists('poduploads/' . $existingDoc->file_name)) {
                        Storage::disk('public')->delete('poduploads/' . $existingDoc->file_name);
                    }
                    $existingDoc->update([
                        'file_name' => $filename,
                        'pickup_date' => $input['pickup_date'] ?? '',
                        'delivery_date' => $input['delivery_date'] ?? '',
                        'drivername' => $input['driver'] ?? '',
                        'status' => 1,
                        'updated_at' => $currentDate,
                    ]);
                } else {
                    TripExpenseDoc::create([
                        'file_name' => $filename,
                        'txn_id' => $id,
                        'pickup_date' => $input['pickup_date'] ?? '',
                        'delivery_date' => $input['delivery_date'] ?? '',
                        'drivername' => $input['driver'] ?? '',
                        'status' => 1,
                        'created_at' => $currentDate,
                    ]);
                }
            } */

            DB::commit();
            return response()->json(['status' => 'success', 'message' => 'Expense payment updated successfully!'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Expense Update Error: ' . $e->getMessage());
            return response()->json(['error' => 'Something went wrong!'], 500);
        }
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = Auth::user();
        $org_id = $user->org_id;

        if (empty($id)) {
            return response()->json(['error' => 'Invalid transaction ID'], 422);
        }

        try {
            $transaction = Transaction::where('id', $id)->where('org_id', $org_id)->first();
            if (!$transaction) {
                return response()->json(['error' => 'Transaction not found!'], 404);
            }

            $transaction->update(['status' => 0]);

            return response()->json(['message' => 'Expense payment deleted successfully!'], 200);
        } catch (\Exception $e) {
            Log::error('Expense Deletion Error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete expense payment'], 500);
        }
    }

    /**
     * Add a new expense category.
     */
    public function addcategory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'act_name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->errors()->first()], 422);
        }

        $user = Auth::user();
        $user_id = $user->id;
        $org_id = $user->org_id;
        $act_name = trim($request->input('act_name'));

        $duplicate = AccountMaster::where('act_name', $act_name)
            ->where('status', 1)
            ->where('org_id', $org_id)
            ->first();

        if ($duplicate) {
            return response()->json(['status' => false, 'message' => 'Category already exists. Please check and try again.'], 422);
        }

        try {
            $category = AccountMaster::create([
                'account_type' => 2,
                'act_name' => $act_name,
                'org_id' => $org_id,
                'user_id' => $user_id,
                'status' => 1,
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Category added successfully.',
                'data' => ['id' => $category->id, 'act_name' => $category->act_name],
            ], 201);
        } catch (\Exception $e) {
            Log::error('Category Creation Error: ' . $e->getMessage());
            return response()->json(['status' => false, 'message' => 'Failed to add category.'], 500);
        }
    }

    /**
     * Upload and process expenses from an Excel file.
     */
    /* public function uploadexpences(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'expence_upload_file' => 'required|file|mimes:xls,xlsx|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->errors()->first()], 422);
        }

        $user = Auth::user();
        $user_id = $user->id;
        $org_id = $user->org_id;
        $be_value = $user->be_value ?? null; // Assuming be_value is in the user model
        $currentDate = now()->toDateTimeString();

        try {
            $file = $request->file('expence_upload_file');
            $spreadsheet = IOFactory::load($file->getRealPath());
            $sheet = $spreadsheet->getActiveSheet();

            $exdata = [
                'arr_data' => [],
                'header' => [],
            ];

            foreach ($sheet->getRowIterator() as $row) {
                $rowIndex = $row->getRowIndex();

                foreach ($row->getCellIterator() as $cell) {
                    $column = $cell->getColumn();
                    $dataValue = $cell->getValue();

                    if ($rowIndex === 1) {
                        $exdata['header'][$rowIndex][$column] = mb_strtoupper($dataValue);
                    } elseif ($rowIndex > 1) {
                        $exdata['arr_data'][$rowIndex][$column] = $dataValue;
                    }
                }
            }

            $data = [];
            foreach ($exdata['arr_data'] as $row) {
                if (
                    !empty($row['A']) &&
                    !empty($row['B']) &&
                    !empty($row['C']) &&
                    is_numeric($row['C']) &&
                    $row['C'] > 0
                ) {
                    $bill_date = !empty($row['D']) && trim($row['D']) !== ''
                        ? date('Y-m-d', strtotime($row['D']))
                        : now()->toDateString();

                    $data[] = [
                        'trip_id' => trim($row['A']),
                        'price' => trim($row['C']),
                        'trip_expense' => $row['B'],
                        'bill_date' => $bill_date,
                    ];
                }
            }

            if (empty($data)) {
                return response()->json(['status' => false, 'message' => 'No valid data found in the uploaded file.'], 422);
            }

            DB::beginTransaction();
            try {
                foreach ($data as $row) {
                    $trip = Trip::select('trips.id', 'trips.driver_id', 'trips.vehicle_id')
                        ->join('shipment', 'trips.shift_id', '=', 'shipment.id')
                        ->where('shipment.shipmentid', $row['trip_id'])
                        ->first();

                    if (!$trip) {
                        continue; // Skip if trip not found
                    }

                    $trip_id = $trip->id;
                    $veh_id = $trip->vehicle_id;
                    $driver_id = $trip->driver_id;

                    $trip_expense = $row['trip_expense'];
                    $fromacnt = 0;

                    if (stripos($trip_expense, 'Diesel') !== false) {
                        $exptype = explode(' - ', $trip_expense);
                        if ($exptype[0] === 'Diesel' && isset($exptype[1]) && $exptype[1] === 'By Card or Account') {
                            $fromacnt = 1;
                        }
                        $trip_expense = 'DIESEL';
                    }

                    $subaccount = AccountMaster::where([
                        'org_id' => $org_id,
                        'be_value' => $be_value,
                        'account_type' => 2,
                        'act_name' => $trip_expense,
                        'status' => 'Active',
                    ])->first();

                    $subacc_id = $subaccount ? $subaccount->id : null;

                    if (!$subacc_id) {
                        $subaccount = AccountMaster::create([
                            'org_id' => $org_id,
                            'user_id' => $user_id,
                            'be_value' => $be_value,
                            'account_type' => 2,
                            'act_name' => $trip_expense,
                            'description' => 'Trip Expense',
                            'created_at' => $currentDate,
                            'status' => 'Active',
                        ]);
                        $subacc_id = $subaccount->id;
                    }

                    $unique_id = (string) time();
                    Transaction::create([
                        'act_id' => $subacc_id,
                        'amount' => $row['price'],
                        'txn_date' => $row['bill_date'],
                        'txn_id' => $unique_id,
                        'receipt_no' => $unique_id,
                        'halting_days' => '0',
                        'driver_id' => $driver_id,
                        'trip_id' => $trip_id,
                        'vehicle_id' => $veh_id,
                        'payment_method' => $fromacnt ? 'Card' : 'Cash',
                        'approval' => 'Yes',
                        'paid' => 'Yes',
                        'description' => 'Trip Expenses',
                        'paid_by' => $user_id,
                        'status' => 'Active',
                        'payable_to' => 'Driver',
                        'org_id' => $org_id,
                        'be_value' => $be_value,
                        'created_at' => $currentDate,
                    ]);
                }

                DB::commit();
                return response()->json(['status' => true, 'message' => 'Successfully uploaded and updated into system!'], 200);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Expense Upload Error: ' . $e->getMessage());
                return response()->json(['status' => false, 'message' => 'Something went wrong. Please try again later.'], 500);
            }
        } catch (\Exception $e) {
            Log::error('Expense Upload File Error: ' . $e->getMessage());
            return response()->json(['status' => false, 'message' => 'Failed to process the uploaded file.'], 500);
        }
    } */

    protected function getDriverByTrip($trip_id)
    {
        $trip = Trip::find($trip_id);
        return $trip ? $trip->driver_id : null;
    }
}
