<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

use App\Models\Driver;
use App\Models\TruckDriver;
use App\Models\Trip;
use App\Models\CountryMaster;
use App\Models\DriverCheckinCheckout;
use App\Models\DriverLoginLog;
use App\Models\Truck;
use App\Models\TrucksData;
use App\Models\AccountMaster;
use App\Models\Transaction;
use App\Models\VehicleInspection;
use App\Models\TripInspectionData;
use App\Models\TripExpenseDoc;
use App\Models\SxUsers;
use App\Models\Shipment;
use App\Models\VehicleOdometer;

class DriverApiController extends Controller
{
    // public function __construct()
    // {
        // require_once APPPATH . 'libraries/Sendsms.php';
        // require_once APPPATH . 'third_party/fpdf/fpdf.php';
        // if (isset($_SERVER['HTTP_ORIGIN'])) {
        //     header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
        //     header('Access-Control-Allow-Credentials: true');
        //     header('Access-Control-Max-Age: 86400');
        // }
        // if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        //     if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
        //         header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
        //     }
        //     if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
        //         header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
        //     }
        // }
        // $post = json_decode(file_get_contents("php://input"), true);
        // $this->load->helper('date');
        // $this->load->model('cabmodel');
        // $this->load->model('common');
        // $this->load->library('smsnotifier');
        // $this->load->library('sendfirebase');
        // $this->load->library("statusintigration");
    // }

    // parties
    // drivercollection

    public function index()
    {
        return response()->json([
                'status' => true,
                'message' => 'Welcome to RT ShipmentX Android services.',
                'data' => [
                    'user' => ''
                ],
            ], 200);
    }

    public function driverLogin(Request $request)
    {
        $response = array();
        $response = array("status" => 0, "data" => "Not Found");
        if ($request->isMethod('post')) {
            $mobile = $request->input("mobile", '');
            $pwd = $request->input("password", '');
            $gcm_id = $request->input("token", '');
            $imei = $request->input("imei", '');
            $phone = $request->input("mobile_number", '');
            if ($mobile != "") {
                if (!empty($phone)) {
                    if (is_array($phone)) {
                        $phone = implode(",", $phone);
                    }
                }

                $driver = Driver::where('contact_num', $mobile)
                    ->where('status', '1')
                    ->first();
                $driverData = [];
                if ($driver) {
                    $driverData = $driver->toArray();
                }
                if ($driverData) {
                    $encryptedPassword = $driverData['password'];
                    if (Hash::check($pwd, $encryptedPassword)) {
                        $insertData = [
                            'mobile' => $mobile,
                            'password' => $pwd,
                            'imei' => $imei,
                            'simcard_phone' => $phone,
                            'status' => 1
                        ];
                        DB::table("driver_login_logs")->insert($insertData);
                        $data = $driverData;
                        $sqlQuery = SxUsers::select("country_code")
                            ->where(["id" => $driverData['user_id'], "status" => "1"])
                            ->get();
                        $data['country_code'] = ($sqlQuery->isNotEmpty()) ? $sqlQuery->first()->country_code : 'US';
                        $id = $data["id"];
                        $qry = DB::table("assigned_drivers as a")
                            ->join("trucks_data as t", "t.id", "=", "a.vehicle_id")
                            ->join("truck_drivers as d", "d.id", "=", "a.driver_id")
                            ->select("t.id", "t.register_number")
                            ->where("a.driver_id", $id)
                            ->where("a.status", '1')
                            ->where("d.status", '1')
                            ->where("t.status", '1')
                            ->orderBy("a.id", "desc")
                            ->limit(1)
                            ->get();
                        $veh = $veh_id = "";
                        if ($qry->isNotEmpty()) {
                            $veh = $qry->first()->register_number;
                            $veh_id = $qry->first()->id;
                        }
                        $whrarr = array("id" => $id);
                        if ($gcm_id != "" && $imei != "") {
                            $upd = TruckDriver::where($whrarr)->update(array("imei" => $imei, "gcm_id" => $gcm_id));
                        } else if ($imei != "") {
                            $upd = TruckDriver::where($whrarr)->update(array("imei" => $imei));
                        }
                        $countrytimezone = "";
                        $chkcntry = CountryMaster::select("cntry_timezone")->where("country_code", $data['country_code'])->first();
                        if ($chkcntry) {
                            $countrytimezone = $chkcntry->cntry_timezone;
                        }
                        if ($countrytimezone == "") {
                            $countrytimezone = "America/New_York";
                        }
                        $data['imei'] = $imei;
                        $data['vehicle'] = $veh;
                        $data['vehicle_id'] = $veh_id;
                        $data['trip_id'] = "";
                        $data['timezone'] = $countrytimezone;
                        $data['border_type'] = 0;
                        if ($veh_id != "") {
                            $whr = array("vehicle_id" => $veh_id, "driver_id" => $id, "status" => "1");
                            $trip = Trip::select('id,shift_id')->where($whr)->orderBy('id', 'desc')->first();
                            if ($trip) {
                                $data['trip_id'] = $trip->id;
                                $shiptid = $trip->shift_id;
                                $shipid = Shipment::select("border_type")->where(array("id" => $shiptid, "border_type" => 1))->first();
                                if ($shipid) {
                                    $data['border_type'] = 1;
                                }
                            }
                        }
                        $checkDriverLogs = DriverCheckinCheckout::select("id AS check_status")
                            ->where("driver_id", $id)
                            ->whereNotNull("checkin_time")
                            ->whereNull("check_out_time")
                            ->where(function ($query) {
                                $query->whereDate("checkin_time", DB::raw("CURRENT_DATE"))
                                    ->orWhereDate("checkin_time", DB::raw("CURRENT_DATE - INTERVAL '1 DAY'"));
                            })
                            ->orderBy("id", "desc")
                            ->limit(1)
                            ->get();

                        if ($checkDriverLogs) {
                            if ($checkDriverLogs->isNotEmpty()) {
                                $data['check_status'] = 1;  //already clocked in have to clock out
                            } else {
                                $data['check_status'] = 0;
                            }
                        } else {
                            $data['check_status'] = 0; // no clock in  data available
                        }
                        $response = array("status" => 1, "data" => $data);
                        $response["status"] = 1;
                        $response["message"] = "Login successful";
                        // Add any additional logic for a successful login here
                    } else {
                        $response = array("status" => 0, "message" => "Incorrect password");
                    }
                } else {
                    $response = array("status" => 0, "message" => "Driver not found or inactive");

                    $response["message"] = "Driver not found or inactive";
                }
            } else {
                $response = array("status" => 0, "message" => "Not Found");
            }
        }
        return response()->json($response, 200);
    }

    public function crossborderChange(Request $request)
    {
        $response = ["status" => 0, "data" => "Not Found"];
        
        if ($request->isMethod('post')) {
            $input = $request->all();
            
            $prev_admin_area = $request->input('prev_admin_area', '');
            $prev_country_code = $request->input('prev_country_code', '');
            $admin_area = $request->input('admin_area', '');
            $country_code = $request->input('country_code', '');
            $user_id = $request->input('user_id', '');
            $driver_id = $request->input('driver_id', '');
            $trip_id = $request->input('trip_id', '');
            $prev_latitude = $request->input('prev_latitude', '');
            $prev_longitude = $request->input('prev_longitude', '');
            $latitude = $request->input('latitude', '');
            $longitude = $request->input('longitude', '');
            
            if (!empty($user_id) && !empty($driver_id) && !empty($trip_id)) {
                try {
                    DB::beginTransaction();
                    
                    $insertData = [
                        'trip_id' => $trip_id,
                        'driver_id' => $driver_id,
                        'user_id' => $user_id,
                        'prev_admin_area' => $prev_admin_area,
                        'prev_country_code' => $prev_country_code,
                        'prev_latitude' => $prev_latitude,
                        'prev_longitude' => $prev_longitude,
                        'admin_area' => $admin_area,
                        'country_code' => $country_code,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];

                    DB::table('crossed_borders')->insert($insertData);

                    $user = DB::table('sx_users')
                        ->select('employee_name', 'emailid', 'company_code')
                        ->where('id', $user_id)
                        ->where('status', '1')
                        ->first();

                    if ($user) {
                        $cmpcode = $user->company_code;
                        
                        if ($cmpcode == "SGKN" || $cmpcode == "MYKN") {
                            $receivemail = $user->emailid;
                            $receivename = $user->employee_name;
                            if (empty($receivemail)) {
                                $receivemail = $user->emailid;
                            }
                        } else {
                            $receivemail = $user->emailid;
                            $receivename = $user->employee_name;
                        }
                        
                        $orderData = DB::select("
                            SELECT e.customer_id, e.order_id 
                            FROM trips t, shiporder_stop_sequence e 
                            WHERE t.shift_id = e.shift_id 
                            AND t.id = ? 
                            AND t.status = '1' 
                            LIMIT 1
                        ", [$trip_id]);
                        
                        if (!empty($orderData)) {
                            $orderData = $orderData[0];
                            $order_id = $orderData->order_id;
                            $customer_id = $orderData->customer_id;
                            
                            $custname = $custemail = "";
                            
                            $customer = DB::table('sx_party_members')
                                ->select('name', 'email')
                                ->where('id', $customer_id)
                                ->where('email', '!=', '')
                                ->where('status', '1')
                                ->first();
                                
                            if ($customer) {
                                $custname = $customer->name;
                                $custemail = $customer->email;
                            }
                            
                            $order = DB::table('orders')
                                ->where('order_id', $order_id)
                                ->first();
                                
                            if ($order) {
                                $orderRef = DB::table('order_references')
                                    ->select('ref_value')
                                    ->where('order_id', $order->id)
                                    ->where('reference_id', 'DQ')
                                    ->first();
                                    
                                $orderRefValue = $orderRef ? $orderRef->ref_value : "";
                                
                                $emailData = [
                                    'order' => $order,
                                    'order_id' => $order_id,
                                    'orderref' => $orderRefValue,
                                    'exit_area' => $prev_admin_area . ", " . $prev_country_code,
                                    'entry_area' => $admin_area . ", " . $country_code,
                                    'page_title' => "Cross Border Shipment",
                                    'receivemail' => $receivemail,
                                    'receivename' => $receivename,
                                    'subject' => "ShipmentX::Cross Border for Shipment #" . $order_id,
                                    'custname' => $custname,
                                    'custemail' => $custemail
                                ];
                                
                                // Send email using Laravel Mail
                                // Mail::send('emails.crossborder', $emailData, function ($message) use ($receivemail, $receivename, $custemail, $custname, $order_id) {
                                //     $message->from('<EMAIL>', 'ShipmentX')
                                //             ->to($receivemail, $receivename)
                                //             ->subject("ShipmentX::Cross Border for Shipment #" . $order_id);
                                    
                                //     if (!empty($custemail) && !empty($custname)) {
                                //         $message->bcc($custemail, $custname);
                                //     }
                                // });
                                
                                $response = ["status" => 1, "data" => $emailData];
                            }
                        }
                    }
                    
                    DB::commit();
                    
                } catch (\Exception $e) {
                    DB::rollBack();
                    $response = ["status" => 0, "data" => "Error: " . $e->getMessage()];
                }
            }
        }
        
        return response()->json($response);
    }

    public function getDriverData(Request $request)
    {
        $response = ["status" => 0, "data" => "Not Found"];
        
        if ($request->isMethod('post')) {
            $id = $request->input('id', '');
            $gcm_id = $request->input('gcm_id', '');
            $imei = $request->input('imei', '');
            
            if (!empty($id)) {
                $driver = TruckDriver::where('id', $id)
                    ->where('status', '1')
                    ->first();
                
                if ($driver) {
                    $updateData = [];
                    if (!empty($imei)) $updateData['imei'] = $imei;
                    if (!empty($gcm_id)) $updateData['gcm_id'] = $gcm_id;
                    
                    if (!empty($updateData)) {
                        $driver->update($updateData);
                    }
                    
                    $trip = Trip::where('driver_id', $id)
                        ->where('status', '1')
                        ->orderBy('id', 'DESC')
                        ->first();
                    
                    $veh = $veh_id = $tripid = "";
                    
                    if ($trip) {
                        $veh_id = $trip->vehicle_id;
                        $vehicle = TrucksData::where('id', $veh_id)
                            ->where('status', '1')
                            ->first();
                        
                        if ($vehicle) {
                            $veh = $vehicle->register_number;
                        }
                        $tripid = $trip->id;
                    }
                    
                    $driverData = $driver->toArray();
                    $driverData['vehicle'] = $veh;
                    $driverData['vehicle_id'] = $veh_id;
                    $driverData['trip_id'] = $tripid;
                    $driverData['country_code'] = 'US';
                    
                    $response = ["status" => 1, "data" => $driverData];
                }
            }
        }
        
        return response()->json($response);
    }

    public function identifySubOrderType($orderID)
    {
        $response = [
            'parent_id' => 0,
            'order_type' => null
        ];

        $row = DB::table('orders')->select('parent_id')->where('id', $orderID)->first();

        if ($row) {
            $mainOrderId = $row->parent_id > 0 ? $row->parent_id : 0;

            if ($mainOrderId > 0) {
                $firstSubOrder = DB::table('orders')
                    ->select('id', 'parent_id')
                    ->where('parent_id', $mainOrderId)
                    ->orderBy('id', 'asc')
                    ->first();

                if ($firstSubOrder && $firstSubOrder->id == $orderID) {
                    $response['parent_id'] = $mainOrderId;
                    $response['order_type'] = 'pickup';
                    return $response;
                }

                $lastSubOrder = DB::table('orders')
                    ->select('id', 'parent_id')
                    ->where('parent_id', $mainOrderId)
                    ->orderBy('id', 'desc')
                    ->first();

                if ($lastSubOrder && $lastSubOrder->id == $orderID) {
                    $response['parent_id'] = $mainOrderId;
                    $response['order_type'] = 'drop';
                    return $response;
                }
            }
        }

        return $response;
    }

    public function startMultipleShipments(Request $request)
    {
        $response = ["status" => 0, "data" => "Insufficient Input Data"];
        $driver_id = $request->input('driver_id');
        $shipmentsJSON = $request->input('shipments');
        $imei = $request->input('imei');
        $latitude = $request->input('latitude');
        $longitude = $request->input('longitude');
        $curtz = $request->input('timezone', 'America/New_York');
        $shipments = [];
        if(is_string($shipmentsJSON) && !empty($shipmentsJSON)) {
            $shipments = json_decode($shipmentsJSON, true);
        } else if(is_array($shipmentsJSON)) {
            $shipments = $shipmentsJSON;
        }

        if (!empty($shipments) && $driver_id) {
            foreach ($shipments as $shipmentArry) {
                $shipment = (object) $shipmentArry;
                $data = [];
                $data["driver_id"] = $driver_id;
                $data["shift_id"] = isset($shipment->shift_id) ? $shipment->shift_id : '';
                $data["vehicle_id"] = isset($shipment->vehicle_id) ? $shipment->vehicle_id : '';
                $data["trip_type"] = isset($shipment->trip_type) ? $shipment->trip_type : 0;
                if ($data["trip_type"] == '') {
                    $data["trip_type"] = 0;
                }
                $data["splace"] = isset($shipment->splace) ? $shipment->splace : '';
                $data["eplace"] = isset($shipment->eplace) ? $shipment->eplace : '';
                $data["start_reading"] = isset($shipment->odemeter) ? $shipment->odemeter : '0';
                $curdt = now();
                $data["stime"] = $curdt;

                $driver = DB::table('truck_drivers')->where('id', $data["driver_id"])->where('status', '1')->first();
                if ($driver) {
                    $trip = DB::table('trips')
                        ->where([
                            'shift_id' => $data["shift_id"],
                            'driver_id' => $data["driver_id"],
                            'vehicle_id' => $data["vehicle_id"]
                        ])->first();

                    if (!$trip) {
                        $data["start_imei"] = $imei;
                        $trip_id = DB::table('trips')->insertGetId($data);
                        DB::table('trip_drivers')->insert([
                            "driver_id" => $data["driver_id"],
                            "trip_id" => $trip_id,
                            "imei" => $imei,
                            "created_at" => $data["created_on"]
                        ]);
                        $shift = DB::table('shiporder_stop_sequence')
                            ->where([
                                "shift_id" => $data["shift_id"],
                                "ship_type" => "P"
                            ])
                            ->orderBy('id', 'DESC')
                            ->first();
                        $uid = $hrs = $shiftOrderID = 0;
                        if ($shift) {
                            $uid = $shift->user_id;
                            $shiftOrderID = $shift->order_id;
                            $gethrs = DB::table('country_master as c')
                                ->join('sx_users as u', 'c.country_code', '=', 'u.country_code')
                                ->where('u.id', $uid)
                                ->where('c.status', '1')
                                ->select('c.cntry_hrs')
                                ->first();
                            $hrs = $gethrs ? $gethrs->cntry_hrs : '';
                        }
                        $postdata = [
                            "shipment_id" => $data["shift_id"],
                            "trip_id" => $trip_id,
                            "driver_id" => $data["driver_id"],
                            "order_id" => $shiftOrderID,
                            "stop_id" => '',
                            "latitude" => $latitude,
                            "longitude" => $longitude,
                            "curtz" => $curtz,
                            "hrs" => $hrs
                        ];
                        // status update
                        $order = DB::table('orders')->where('shift_id', $data["shift_id"])->first();
                        $orderID = $order ? $order->id : 0;
                        if ($orderID > 0) {
                            $chqry = DB::table('stop_status')
                                ->where([
                                    "shipment_id" => $data["shift_id"],
                                    "order_id" => $orderID,
                                    "stop_id" => 0,
                                    "stop_detail_id" => 0,
                                    "trip_id" => $trip_id,
                                    "status_id" => 10
                                ])->first();
                            if (!$chqry) {
                                $insarry = [
                                    "shipment_id" => $data["shift_id"],
                                    "order_id" => $orderID,
                                    "stop_id" => 0,
                                    "stop_detail_id" => 0,
                                    "stop_type" => '',
                                    "trip_id" => $trip_id,
                                    "status_id" => 10,
                                    "status" => '1',
                                    "latitude" => $latitude,
                                    "longitude" => $longitude,
                                    "status_code" => "0212",
                                    "reason" => "From Mobile",
                                    "vehicle_id" => $data["vehicle_id"],
                                    "driver_id" => $data["driver_id"]
                                ];
                                // Once we have a geographyPoint helper, use it here
                                // $insarry['geolocation'] = geographyPoint($insarry['longitude'], $insarry['latitude']);
                                DB::table('stop_status')->insert($insarry);
                                DB::table('orders')
                                    ->where(["shift_id" => $data["shift_id"], "id" => $order->id])
                                    ->update(["trip_id" => $trip_id]);
                                $orderInfo = $this->identifySubOrderType($orderID);
                                $parentOrderId = $orderInfo['parent_id'];
                                $orderType = $orderInfo['order_type'];
                                if ($parentOrderId > 0 && $orderType === 'pickup') {
                                    $arr = [
                                        'stop_type' => '',
                                        'vehicle_id' => $data["vehicle_id"],
                                        'driver_id' => $data["driver_id"],
                                        'contact_num' => '',
                                        'company_code' => $order->company_code,
                                        'curdt' => $curdt,
                                        'crossborder' => 0,
                                        'stopid' => 0,
                                        'stopdetailid' => 0,
                                    ];
                                    $this->updateMainOrderStatus($parentOrderId, 10, "0212", $latitude, $longitude, $curdt, $orderType, "From Mobile", $arr);
                                }
                            }
                        }
                        if ($trip_id > 0) {
                            $empdata = ["trip_id" => $trip_id];
                            $response = ["status" => 1, "message" => "Trip created", "data" => [$empdata]];
                        }
                    } else {
                        $empdata = ["trip_id" => $trip->id];
                        $response = ["status" => 1, "message" => "Trip found", "data" => [$empdata]];
                    }
                } else {
                    $response = ["status" => 3, "message" => "Invalid Request"];
                }
            }
        }
        return response()->json($response);
    }

    public function updateMainOrderStatus($parentOrderId, $statusId, $statusCode, $latitude, $longitude, $createdOn, $orderType, $reason = '', $arr = [])
    {
        $isPickupStatus = isset($arr['stop_type']) && $arr['stop_type'] == "P" && $orderType === 'pickup' && (
            ($statusId == "2" && $statusCode == "0420") || // Gate In
            ($statusId == "1" && in_array($statusCode, ["0500", "0502", "0501"])) || // Pickup
            ($statusId == "3" && $statusCode == "0191") || // Gate Out
            ($statusId == "4" && $statusCode == "1550")    // In Transit
        );
        $isDeliveryStatus = isset($arr['stop_type']) && $arr['stop_type'] == "D" && $orderType === 'drop' && (
            ($statusId == "2" && $statusCode == "0192") || // Delivery Gate IN
            ($statusId == "1" && in_array($statusCode, ["2300", "0502", "0504", "0503"])) || // Delivery
            ($statusId == "3" && $statusCode == "3000")    // Delivery Gate OUT
        );
        $isTripCompleteStatus = isset($arr['stop_type']) && $arr['stop_type'] == "D" && $statusId == "11" && $statusCode == "0218";
        $isTripStartStatus = $statusId == "10" && $statusCode == "0212"; // Trip Start

        if ($isPickupStatus || $isDeliveryStatus || $isTripCompleteStatus || $isTripStartStatus) {
            $mainOrderData = DB::table('orders')->where('id', $parentOrderId)->first(['shift_id', 'trip_id']);
            if (!$mainOrderData) {
                \Log::error("No main order data found for parentOrderId: {$parentOrderId}");
                return false;
            }
            $shiftId = $mainOrderData->shift_id;
            $tripId = $mainOrderData->trip_id;

            if ($isTripStartStatus) {
                $tripQuery = DB::table('trips')
                    ->where([
                        'shift_id' => $shiftId,
                        'vehicle_id' => $arr['vehicle_id'] ?? 0,
                        'driver_id' => $arr['driver_id'] ?? 0
                    ])->first();

                if (!$tripQuery) {
                    if (empty($arr['contact_num'])) {
                        $newimei = DB::table('assigned_drivers')
                            ->where([
                                'vehicle_id' => $arr['vehicle_id'] ?? 0,
                                'driver_id' => $arr['driver_id'] ?? 0,
                                'status' => 1
                            ])->value('imei');
                        if ($newimei) {
                            $arr['contact_num'] = $newimei;
                        }
                    }
                    $tripLatitude = $latitude;
                    $tripLongitude = $longitude;
                    if (isset($arr['company_code']) && $arr['company_code'] == 'PLKN') {
                        $getveh = DB::table('trucks_data')->where('id', $arr['vehicle_id'] ?? 0)->first(['latitude', 'longitude']);
                        if ($getveh) {
                            $tripLatitude = $getveh->latitude;
                            $tripLongitude = $getveh->longitude;
                        }
                    }
                    // Create new trip
                    $triparr = [
                        'shift_id' => $shiftId,
                        'vehicle_id' => $arr['vehicle_id'] ?? 0,
                        'driver_id' => $arr['driver_id'] ?? 0,
                        'stime' => $createdOn,
                        'start_imei' => $arr['contact_num'] ?? '',
                        'splace' => "",
                        'eplace' => "",
                        'start_reading' => 0,
                        'end_reading' => 0,
                        'created_at' => $createdOn,
                        'updated_at' => $arr['curdt'] ?? $createdOn,
                        'status' => 1,
                        'trip_type' => 0,
                        'transit_status' => 0,
                        "plat" => $tripLatitude,
                        "plng" => $tripLongitude
                    ];
                    $tripId = DB::table('trips')->insertGetId($triparr);

                    // Insert stop status
                    $insertData = [
                        "order_id" => $parentOrderId,
                        "shipment_id" => $shiftId,
                        "stop_id" => 0,
                        "stop_detail_id" => 0,
                        "stop_type" => "",
                        "trip_id" => $tripId,
                        "status_id" => $statusId,
                        "latitude" => $tripLatitude,
                        "longitude" => $tripLongitude,
                        "status" => 1,
                        "reason" => $reason,
                        "vehicle_id" => $arr['vehicle_id'] ?? 0,
                        "driver_id" => $arr['driver_id'] ?? 0,
                        "status_code" => $statusCode
                    ];
                    $checkExistData = [
                        "order_id" => $parentOrderId,
                        "shipment_id" => $shiftId,
                        "stop_type" => $arr['stop_type'] ?? '',
                        "status_id" => $statusId,
                        "status_code" => $statusCode
                    ];
                    $existingStopStatus = DB::table('stop_status')->where($checkExistData)->first();
                    if ($existingStopStatus) {
                        DB::table('stop_status')->where('id', $existingStopStatus->id)->update($insertData);
                    } else {
                        DB::table('stop_status')->insert($insertData);
                    }
                } else {
                    $tripId = $tripQuery->id;
                    $statusQuery = DB::table('stop_status')
                        ->where([
                            "order_id" => $parentOrderId,
                            "shipment_id" => $shiftId,
                            "status_id" => $statusId
                        ])->first();
                    if (!$statusQuery) {
                        $insertData = [
                            "order_id" => $parentOrderId,
                            "shipment_id" => $shiftId,
                            "stop_id" => 0,
                            "stop_detail_id" => 0,
                            "stop_type" => "",
                            "trip_id" => $tripId,
                            "status_id" => $statusId,
                            "latitude" => $latitude ?? 0,
                            "longitude" => $longitude ?? 0,
                            "status" => 1,
                            "reason" => $reason,
                            "vehicle_id" => $arr['vehicle_id'] ?? 0,
                            "driver_id" => $arr['driver_id'] ?? 0,
                            "status_code" => $statusCode,
                            "createdon" => $createdOn
                        ];
                        DB::table('stop_status')->insert($insertData);
                    }
                }
                // Update orders or shiporder_stop_sequence with trip_id
                if ($tripId > 0) {
                    if (isset($arr['crossborder']) && $arr['crossborder'] > 0) {
                        DB::table('shiporder_stop_sequence')->where('shift_id', $shiftId)->update(['trip_id' => $tripId]);
                    } else {
                        DB::table('orders')->where('shift_id', $shiftId)->update(['trip_id' => $tripId]);
                    }
                }
            } else {
                $tripQuery = DB::table('trips')->where(['shift_id' => $shiftId, 'id' => $tripId])->first();
                if (!$tripQuery) {
                    \Log::error("No trip found for parentOrderId - 1702: {$parentOrderId} with tripId: {$tripId} with shiftId: {$shiftId}");
                    return false;
                }
                // Validate Delivery Gate IN (statusId = 3, statusCode = 0191)
                if ($statusId == "2" && $statusCode == "0192") {
                    $checkPickupStatus = DB::table('stop_status')
                        ->where([
                            "order_id" => $parentOrderId,
                            "shipment_id" => $shiftId,
                            "stop_type" => 'P',
                            "trip_id" => $tripId,
                            "status_id" => 3,
                            "status_code" => '0191'
                        ])->first();
                    if (!$checkPickupStatus) {
                        \Log::error("Delivery Gate IN rejected for parentOrderId: {$parentOrderId}. No Pickup In Transit status found.");
                        return false;
                    }
                }
                $statusData = [
                    "order_id" => $parentOrderId,
                    "shipment_id" => $shiftId,
                    "stop_id" => $arr['stopid'] ?? 0,
                    "stop_detail_id" => $arr['stopdetailid'] ?? 0,
                    "stop_type" => $arr['stop_type'] ?? '',
                    "trip_id" => $tripId,
                    "status_id" => $statusId
                ];
                $statusQuery = DB::table('stop_status')->where($statusData)->first();
                if (!$statusQuery) {
                    if ($statusId == "2" && ($arr['stop_type'] ?? '') == "P") {
                        DB::table('trips')->where('id', $tripId)->update([
                            "updated_on" => $createdOn,
                            "transit_status" => '1'
                        ]);
                    }
                    $insertData = array_merge($statusData, [
                        "latitude" => $latitude,
                        "longitude" => $longitude,
                        "status" => 1,
                        "reason" => $reason,
                        "vehicle_id" => $arr['vehicle_id'] ?? 0,
                        "driver_id" => $arr['driver_id'] ?? 0,
                        "status_code" => $statusCode,
                        "createdon" => $createdOn
                    ]);
                    if (!DB::table('stop_status')->insert($insertData)) {
                        \Log::error("Failed to insert stop status for parentOrderId: {$parentOrderId}");
                        return false;
                    }
                    if (isset($arr['stopdetailid']) && $arr['stopdetailid'] > 0) {
                        $employeeQuery = DB::table('trip_employee')
                            ->where([
                                "employee_id" => $arr['stopdetailid'],
                                "stop_id" => $arr['stopid'] ?? 0,
                                "trip_id" => $tripId,
                                "status" => 1
                            ])->first();
                        if (!$employeeQuery) {
                            $employeeData = [
                                "employee_id" => $arr['stopdetailid'],
                                "stop_id" => $arr['stopid'] ?? 0,
                                "trip_id" => $tripId,
                                "status" => 1,
                                'driver_late' => 0,
                                'emp_late' => 0,
                                'stime' => $createdOn,
                                'check_in' => $createdOn,
                                'absent_reason' => 'Closed',
                                'created_on' => $createdOn,
                                'updated_on' => $createdOn,
                                'pd_status' => 1
                            ];
                            DB::table('trip_employee')->insert($employeeData);
                        }
                    }
                    // Trip Complete logic
                    if ($isTripCompleteStatus) {
                        // Add your KPI/return trip logic here if needed
                        if (isset($arr['crossborder']) && $arr['crossborder'] > 0) {
                            $tripStatusCheck = 0;
                            $shiftIds = [];
                            $checkShiftLegs = DB::table('shipment')->where('id', $shiftId)->where('shift_leg_id', '>', 0)->first(['shift_leg_id']);
                            if ($checkShiftLegs) {
                                $shiftLegId = $checkShiftLegs->shift_leg_id;
                                $getAllLegIds = DB::table('shipment')->where('shift_leg_id', $shiftLegId)->where('status', '1')->pluck('id')->toArray();
                                if (count($getAllLegIds) > 1) {
                                    $shiftIds = $getAllLegIds;
                                    DB::table('shipment')->where('id', $shiftId)->update(['status' => '0', 'updated_on' => $createdOn]);
                                    DB::table('trips')->where('id', $tripId)->update([
                                        'end_imei' => $arr['contact_num'] ?? '',
                                        'end_reading' => '0',
                                        'etime' => $createdOn,
                                        'updated_on' => $createdOn,
                                        'status' => '0',
                                        'transit_status' => '1'
                                    ]);
                                    $checkPendingLegIds = DB::table('shipment')->whereIn('id', $shiftIds)->where('status', '1')->first();
                                    if (!$checkPendingLegIds) {
                                        DB::table('shipment')->where('id', $shiftLegId)->where('status', '1')->update(['status' => '0']);
                                        $tripStatusCheck = 1;
                                    }
                                } else {
                                    $shiftIds = [$shiftLegId, $shiftId];
                                    DB::table('shipment')->whereIn('id', $shiftIds)->where('status', '1')->update(['status' => '0']);
                                    DB::table('trips')->where('id', $tripId)->update([
                                        'end_imei' => $arr['contact_num'] ?? '',
                                        'end_reading' => '0',
                                        'etime' => $createdOn,
                                        'updated_on' => $createdOn,
                                        'status' => '0',
                                        'transit_status' => '1'
                                    ]);
                                    $tripStatusCheck = 1;
                                }
                                if ($tripStatusCheck && !empty($shiftLegId)) {
                                    DB::table('orders')->where('shift_id', $shiftLegId)->update(['trip_sts' => '1']);
                                }
                            }
                        } else {
                            $chkmuliti = DB::table('orders')->where('shift_id', $shiftId)->where('trip_sts', '0')->get();
                            // Add your return trip/generate summary logic here if needed
                            if ($chkmuliti->count() > 1) {
                                DB::table('orders')->where('id', $parentOrderId)->where('trip_id', '!=', 0)->update(['trip_sts' => '1']);
                            } else {
                                DB::table('shipment')->where('id', $shiftId)->update(['status' => '0', 'updated_on' => $createdOn]);
                                DB::table('trips')->where('id', $tripId)->update([
                                    "end_imei" => $arr['contact_num'] ?? '',
                                    "end_reading" => '0',
                                    "etime" => $createdOn,
                                    "updated_on" => $createdOn,
                                    "status" => '0',
                                    "transit_status" => '1'
                                ]);
                                if ($shiftId > 0) {
                                    DB::table('orders')->where('shift_id', $shiftId)->update(['trip_sts' => '1']);
                                }
                            }
                        }
                    }
                }
                if ($mainOrderData->trip_id != $tripId) {
                    DB::table('orders')->where('id', $parentOrderId)->update(['trip_id' => $tripId]);
                }
            }
            return true;
        }
        return true;
    }

    public function getshipments(Request $request)
    {
        $response = ["status" => 0, "data" => "Insufficient Input Data"];
        
        if ($request->isMethod('post')) {
            $driver = $request->input('driver_id', '');
            $imei = $request->input('imei', '');
            $type = $request->input('type', '');
            $search = $request->input('search', '');
            
            if ($driver != "" && $imei != "") {
                $curtz = $request->input('timezone', 'America/New_York');
                if ($curtz == "") {
                    $curtz = "America/New_York";
                }
                
                $whr = ["id" => $driver, "status" => "1"];

                $res = DB::table('truck_drivers')
                    ->select('id', 'user_id')
                    ->where($whr)
                    ->first();

                if ($res) {
                    if (!$request->has('pman') || $request->input('pman') != '2') {
                        DB::table('truck_drivers')
                            ->where('id', $driver)
                            ->update(['imei' => $imei]);
                            
                        $chkdri = DB::table('assigned_drivers')
                            ->select('id')
                            ->where('driver_id', $driver)
                            ->first();
                            
                        if ($chkdri) {
                            DB::table('assigned_drivers')
                                ->where('driver_id', $driver)
                                ->update(['imei' => $imei]);
                        }
                    }
                    
                    $searchwhr = "";
                    $searchres = [];
                    
                    if ($search != "") {
                        $searchwhr = " AND (r.ref_value LIKE '%" . $search . "%' OR o.order_id LIKE '%" . $search . "%' OR o.shipmentid LIKE '%" . $search . "%') ";
                        
                        $qry = DB::select("
                            SELECT e.shift_id 
                            FROM orders o, order_references r, shiporder_stop_sequence e 
                            WHERE o.id = r.order_id AND o.order_id = e.order_id 
                            $searchwhr AND o.status = 2 AND r.status = '1' 
                            GROUP BY e.shift_id
                        ");
                        
                        if (!empty($qry)) {
                            foreach ($qry as $res1) {
                                $searchres[] = $res1->shift_id;
                            }
                        } else {
                            $searchwhr = " AND (o.order_id LIKE '%" . $search . "%' OR o.shipmentid LIKE '%" . $search . "%') ";
                            
                            $qry = DB::select("
                                SELECT e.shift_id 
                                FROM orders o, shiporder_stop_sequence e 
                                WHERE o.order_id = e.order_id 
                                $searchwhr AND o.status = 2 
                                GROUP BY e.shift_id
                            ");
                            
                            if (!empty($qry)) {
                                foreach ($qry as $res1) {
                                    $searchres[] = $res1->shift_id;
                                }
                            }
                        }
                    }
                    
                    $reswhr = "";
                    if (!empty($searchres)) {
                        $reswhr = " AND s.id IN(" . implode(',', $searchres) . ") ";
                    }
                    
                    $uid = $res->user_id;

                    if (($type == "") || ($type == 0) || ($type == 1)) {
                        $sql = "
                            SELECT 
                                COALESCE(t.id, NULL) AS id, 
                                s.id AS shift_id, 
                                s.user_id, 
                                d.id AS driver_id, 
                                s.stime, 
                                s.etime, 
                                s.trip_type, 
                                s.startdate, 
                                s.enddate, 
                                s.splace, 
                                s.eplace, 
                                s.scity, 
                                s.dcity, 
                                s.slat, 
                                s.slng, 
                                s.sgeolocation, 
                                s.elat, 
                                s.elng, 
                                s.egeolocation, 
                                s.shipmentid, 
                                s.border_type, 
                                v.id AS shift_veh_id, 
                                a.vehicle_id, 
                                d.imei, 
                                COALESCE((SELECT SUM(e.shipment_weight) 
                                        FROM shipment_vehicle_stopsleg h 
                                        JOIN shiporder_stop_sequence e ON h.id = e.id 
                                        WHERE h.shipment_veh_id = v.id AND h.status = '1' AND e.status = '1'), '0') AS shipweight, 
                                COALESCE(t.transit_status, '-1') AS astatus 
                            FROM 
                                shipment s 
                                JOIN shft_veh v ON s.id = v.shft_id 
                                JOIN assigned_drivers a ON v.vehicle_id = a.vehicle_id 
                                JOIN truck_drivers d ON d.id = a.driver_id 
                                LEFT JOIN trips t ON t.shift_id = s.id AND t.driver_id = d.id AND t.status = '1' 
                            WHERE 
                                s.status = '1' 
                                AND v.status = '1' 
                                AND a.status = '1' 
                                AND a.driver_id = ? 
                                AND d.id = ? 
                                $reswhr 
                            GROUP BY 
                                s.id, t.id, d.id, v.id, a.vehicle_id 
                            ORDER BY 
                                astatus DESC
                        ";
                        
                        $params = [$driver, $driver];
                    } else {
                        $sql = "
                            SELECT 
                                s.id AS shift_id,
                                t.id,
                                s.user_id,
                                s.stime,
                                s.etime,
                                s.trip_type,
                                s.startdate,
                                s.enddate,
                                s.splace,
                                s.eplace,
                                s.scity,
                                s.dcity,
                                s.slat,
                                s.slng,
                                s.sgeolocation,
                                s.elat,
                                s.elng,
                                s.egeolocation,
                                s.shipmentid,
                                s.border_type,
                                v.id AS shift_veh_id,
                                t.vehicle_id,
                                t.start_imei AS imei,
                                COALESCE((SELECT SUM(e.shipment_weight) 
                                        FROM shipment_vehicle_stopsleg h
                                        JOIN shiporder_stop_sequence e ON e.id = h.id
                                        WHERE h.shipment_veh_id = v.id AND h.status = '1' AND e.status = '1'), '0') AS shipweight,
                                '2' AS astatus 
                            FROM 
                                shipment s
                                JOIN trips t ON s.id = t.shift_id
                                JOIN shft_veh v ON s.id = v.shft_id AND v.vehicle_id = t.vehicle_id
                            WHERE 
                                t.driver_id = ?
                                AND t.status = '0'
                                AND v.status = '1'
                                $reswhr
                            GROUP BY 
                                s.id, t.id, v.id
                        ";
                        $params = [$driver];
                    }
                    
                    $query = DB::select($sql, $params);
                    $res = [];
                    $res['trips'] = [];
                    $res['doc_types'] = [];
                    
                    if (!empty($query)) {
                        $i = 0;
                        $res['doc_types'] = DB::table('document_types')
                            ->select('id', 'type_name')
                            ->where('status', '1')
                            ->get()
                            ->toArray();
                            
                        foreach ($query as $d) {
                            $d = (array)$d;
                            $d['dftsts'] = 0;
                            
                            $chstops = DB::table('shiporder_stops')
                                ->select('address')
                                ->where('shipment_id', $d['shift_id'])
                                ->orderBy('ordernumber', 'ASC')
                                ->get();
                                
                            if ($chstops->isNotEmpty()) {
                                $l = 0;
                                $k = ($chstops->count() - 1);
                                foreach ($chstops as $stp) {
                                    if ($l == 0) {
                                        $d["saddress"] = $stp->address;
                                    }
                                    if ($k == $l) {
                                        $d["daddress"] = $stp->address;
                                    }
                                    $l++;
                                }
                            }
                            
                            $drop = "";
                            $pod = $signature = "";
                            
                            if (strlen($d["dcity"]) > 0) {
                                $drop .= ucfirst(str_replace("_", " ", $d["dcity"])) . " - ";
                            }
                            if (strlen($d["eplace"]) > 0) {
                                $drop .= ucfirst(str_replace("_", " ", $d["eplace"]));
                            }
                            
                            $pickup = "";
                            if (strlen($d["scity"]) > 0) {
                                $pickup .= ucfirst(str_replace("_", " ", $d["scity"])) . " - ";
                            }
                            if (strlen($d["splace"]) > 0) {
                                $pickup .= ucfirst(str_replace("_", " ", $d["splace"]));
                            }
                            
                            $d["splace"] = $pickup;
                            $d["eplace"] = $drop;
                            
                            if (($type == "") || ($type == 0) || ($type == 1)) {
                                if ($d["id"] == "") {
                                    $d['astatus'] = '';
                                    $d['dftsts'] = 0;
                                } else {
                                    $d['dftsts'] = 1;
                                    if ($d["astatus"] == 2) {
                                        $d['dftsts'] = 2;
                                    }
                                    
                                    $trip = DB::select("
                                        SELECT id, transit_status 
                                        FROM trips 
                                        WHERE driver_id = ? AND shift_id = ? AND status = '1' 
                                        ORDER BY id DESC LIMIT 1
                                    ", [$driver, $d["shift_id"]]);
                                    
                                    if (!empty($trip)) {
                                        $d['astatus'] = 0;
                                        if ($trip[0]->transit_status == 1) {
                                            $d['astatus'] = 1;
                                        }
                                    }
                                    
                                    $chkdoc = DB::select("
                                        SELECT id, doc_type 
                                        FROM pod_uploads 
                                        WHERE doc_type IN (1,3) 
                                        AND shipment_id = ? 
                                        AND trip_id = ?
                                    ", [$d['shift_id'], $d['id']]);
                                    
                                    if (!empty($chkdoc)) {
                                        foreach ($chkdoc as $doc) {
                                            if ($doc->doc_type == 1) {
                                                $signature = 1;
                                            }
                                            if ($doc->doc_type == 3) {
                                                $pod = 1;
                                            }
                                        }
                                    }
                                }
                                
                                $d['sno'] = $i;
                                $dist = $this->distancemetrixship($d['slat'], $d['slng'], $d['elat'], $d['elng'], $d['sgeolocation'], $d['egeolocation']);
                                $d['distance'] = $dist['disttext'];
                                $d['duration'] = $dist['duratext'];
                                $d['startdate'] = strtotime($d['startdate']);
                                $d['enddate'] = strtotime($d['enddate']);
                                $d['pod'] = $pod;
                                $d['signature'] = $signature;
                                $res['trips'][] = $d;
                                $i++;
                            } else {
                                if ($d['id'] == "") {
                                    $d['dftsts'] = 0;
                                }
                                if ($d['id'] != "") {
                                    $d['dftsts'] = 1;
                                }
                                if ($d["astatus"] == 2) {
                                    $d['dftsts'] = 2;
                                }
                                
                                $chkdoc = DB::select("
                                    SELECT id, doc_type 
                                    FROM pod_uploads 
                                    WHERE doc_type IN (1,3) 
                                    AND shipment_id = ? 
                                    AND trip_id = ?
                                ", [$d['shift_id'], $d['id']]);
                                
                                if (!empty($chkdoc)) {
                                    foreach ($chkdoc as $doc) {
                                        if ($doc->doc_type == 1) {
                                            $signature = 1;
                                        }
                                        if ($doc->doc_type == 3) {
                                            $pod = 1;
                                        }
                                    }
                                }
                                
                                $d['sno'] = $i;
                                $dist = $this->distancemetrixship($d['slat'], $d['slng'], $d['elat'], $d['elng'], $d['sgeolocation'], $d['egeolocation']);
                                $d['distance'] = $dist['disttext'];
                                $d['duration'] = $dist['duratext'];
                                $d['startdate'] = strtotime($d['startdate']);
                                $d['enddate'] = strtotime($d['enddate']);
                                
                                if ($d['astatus'] == '-1') {
                                    $d['astatus'] = '';
                                }
                                
                                $d['pod'] = $pod;
                                $d['signature'] = $signature;
                                $res['trips'][] = $d;
                                $i++;
                            }
                        }
                        
                        $response = ["status" => 1, "message" => "Shipments retrieved successfully.", "data" => $res];
                    } else {
                        $response = ["status" => 0, "message" => "No Shipments Found" , "data" => $res];
                    }
                } else {
                    $response = ["status" => 2, "message" => "Imei Changed!"];
                }
            } else {
                $response = ["status" => 0, "message" => "Not Found"];
            }
        }
        
        return response()->json($response);
    }

    public function distancemetrixship($slat, $slng, $elat, $elng, $origin = '', $destination = '')
	{
		$res = array();
		$res['distance'] = $res['disttext'] = $res['duration'] = $res['duratext'] = "";
		if($origin != '' && $destination != ''){
            // Haversine formula to calculate distance and duration (deprecated)
            $query = "SELECT 
                    3959 * ACOS(
                        SIN(RADIANS($slat)) * SIN(RADIANS($elat)) +
                        COS(RADIANS($slat)) * COS(RADIANS($elat)) * 
                        COS(RADIANS($elng - $slng))
                    ) AS dist_in_miles,
                    
                    6371 * ACOS(
                        SIN(RADIANS($slat)) * SIN(RADIANS($elat)) +
                        COS(RADIANS($slat)) * COS(RADIANS($elat)) *
                        COS(RADIANS($elng - $slng))
                    ) AS dist_in_kms,
                    
                    (6371 * ACOS(
                        SIN(RADIANS($slat)) * SIN(RADIANS($elat)) +
                        COS(RADIANS($slat)) * COS(RADIANS($elat)) *
                        COS(RADIANS($elng - $slng))
                    )) / 60 * 60 AS duration_in_mins";

            // PostGIS query to calculate distance and duration
			// $query = "SELECT 
            //                 (ST_Distance(origin::geography, destination::geography) / 1609.34) AS dist_in_miles,
            //                 (ST_Distance(origin::geography, destination::geography) / 1000) AS dist_in_kms, 
            //                 (ST_Distance(origin::geography, destination::geography) / (60 * 1000 / 3600)) / 60 AS duration_in_mins
            //             FROM (
            //                 SELECT 
            //                     ST_GeomFromText('" . $origin . ")', 4326) AS origin,
            //                     ST_GeomFromText('" . $destination . ")', 4326) AS destination
            //             ) AS points;
            //             ";
			$results = DB::select($query);
			$res['disttext'] = $results[0]->dist_in_miles;
			$res['distance'] = $results[0]->dist_in_kms;
			$res['duratext'] = $results[0]->duration_in_mins;
			$res['duration'] = $results[0]->duration_in_mins;
		} else if ($slat != "" && $slng != "" && $elat != "" && $elng != "" && $res['distance'] == "" && $res['duration'] == "") {
			$origin = "POINT( " . $slng . " " . $slat . ")";
			$destination = "POINT( " . $elng . " " . $elat . ")";
			return $this->distancemetrixship($slat, $slng, $elat, $elng, $origin, $destination);
		}
		return $res;
	}

    public function getDriverCompanyCode($id)
    {
        $driver = DB::table('truck_drivers')
            ->select('company_code', 'branch_code')
            ->where('id', $id)
            ->where('status', '1')
            ->first();

        if ($driver) {
            return [
                'company_code' => $driver->company_code,
                'branch_code' => $driver->branch_code
            ];
        }

        return ['company_code' => '', 'branch_code' => ''];
    }

    public function getShipmentStops(Request $request)
    {
        $response = ["status" => 0, "message" => "Insufficient Input Data"];

        $shft_veh_id = $request->input("shift_veh_id", "");
        $shft_id = $request->input("shift_id", "");
        $trip = $request->input("trip_id", 0);

        if ($shft_veh_id !== "" && $shft_id !== "") {
            $curtz = $request->input('timezone', 'America/New_York');
            if ($trip === "") {
                $trip = 0;
            }

            // Get transport mode
            $getTransportMode = DB::table('orders')->select('transport_mode')->where('shift_id', $shft_id)->first();
            $transportMode = $getTransportMode ? $getTransportMode->transport_mode : "FTL";

            // Get stops
            $stops = DB::table('shiporder_stops')
                ->select(
                    'id',
                    'stopname',
                    'plat',
                    'plng',
                    'stopcity',
                    'address',
                    'stoptype',
                    'ordernumber',
                    DB::raw("convertToClientTZ(startdate, ?) as startdate", [$curtz]),
                    DB::raw("convertToClientTZ(enddate, ?) as enddate", [$curtz]),
                    'weight',
                    'volume',
                    'ship_units'
                )
                ->where('shipment_id', $shft_id)
                ->orderBy('ordernumber', 'ASC')
                ->get();

            $result = [];
            $i = 0;
            $status = "N";
            $prevsts = "N";

            if ($stops->count() > 0) {
                foreach ($stops as $res) {
                    $stop_id = $res->id;

                    // Try pickup employee
                    $emp = DB::table('shiporder_stop_sequence')
                        ->select('id', 'order_id')
                        ->where('stop_id', $stop_id)
                        ->where('shift_id', $shft_id)
                        ->where('status', '1')
                        ->first();

                    // Get cargo value
                    $cargovalue = DB::table('shipment')->select('shipment_name')->where('id', $shft_id)->first();

                    // Get order_id from stop_status
                    $order_id = DB::table('stop_status')
                        ->where('shipment_id', $shft_id)
                        ->where('stop_id', $stop_id)
                        ->value('order_id');

                    // Get cargo units
                    $order_cargo_details = [
                        "weight_units" => "lbs",
                        "volume_units" => "cbm"
                    ];
                    if ($order_id) {
                        $cargo_details = DB::table('order_cargodetails')
                            ->leftJoin('cargo_details', 'cargo_details.id', '=', 'order_cargodetails.cargo_id')
                            ->select('weight_unit', 'volume_unit')
                            ->where('order_id', $order_id)
                            ->first();
                        if ($cargo_details) {
                            $order_cargo_details = [
                                "weight_units" => $cargo_details->weight_unit,
                                "volume_units" => $cargo_details->volume_unit
                            ];
                        }
                    }

                    if ($emp) {
                        if ($prevsts == "C") {
                            $status = $prevsts = "S";
                        } else {
                            $status = "N";
                        }
                        $detailsid = $emp->id;

                        $statuses = DB::table('stop_status')
                            ->select('id')
                            ->where([
                                "stop_id" => $stop_id,
                                "shipment_id" => $shft_id
                            ])
                            ->get();

                        if ($statuses->count() > 0) {
                            $status = $prevsts = "S";
                            $gateout_details = DB::table('stop_status')
                                ->select('id')
                                ->where([
                                    "stop_id" => $stop_id,
                                    "shipment_id" => $shft_id,
                                    "status_id" => "3",
                                    "status" => "1",
                                    "stop_type" => $res->stoptype,
                                    "stop_detail_id" => $detailsid
                                ])
                                ->get();
                            if ($gateout_details->count() > 0) {
                                $status = $prevsts = "C";
                            }
                        }

                        $stopname = "";
                        if (strlen($res->stopcity) > 0) {
                            $stopname .= ucfirst(str_replace("_", " ", $res->stopcity)) . " - ";
                        }
                        if (strlen($res->stopname) > 0) {
                            $stopname .= ucfirst(str_replace("_", " ", $res->stopname));
                        }
                        if ($status == "N" && $i == 0) {
                            $status = "S";
                        }
                        $result[] = [
                            'id' => $stop_id,
                            'name' => $stopname,
                            "stopname" => $res->stopname,
                            "address" => $res->address,
                            'plat' => $res->plat,
                            'plng' => $res->plng,
                            'ship_type' => $res->stoptype,
                            'shipment_weight' => $res->weight,
                            'shipment_volume' => $res->volume,
                            'shipment_units' => $res->ship_units ? $res->ship_units : null,
                            'startdate' => strtotime($res->startdate),
                            'enddate' => strtotime($res->enddate),
                            'priority' => $res->ordernumber,
                            'status' => $status,
                            'order_id' => $emp->order_id,
                            "cargo" => $cargovalue ? $cargovalue->shipment_name : "",
                            "cargo_units" => $order_cargo_details,
                            'detail_id' => $detailsid,
                            'last_stop' => 0,
                            'transport_mode' => $transportMode
                        ];
                        if ($status == "C" && $i == 0) {
                            $status = "S";
                        }
                    } else {
                        // Try drop employee
                        $emp = DB::table('shiporder_stop_sequence')
                            ->select('id', 'order_id')
                            ->where('drop_stopid', $stop_id)
                            ->where('shift_id', $shft_id)
                            ->where('status', '1')
                            ->first();

                        if ($emp) {
                            if ($prevsts == "C") {
                                $status = $prevsts = "S";
                            } else {
                                $status = "N";
                            }
                            $detailsid = $emp->id;

                            $statuses = DB::table('stop_status')
                                ->select('id')
                                ->where([
                                    "stop_id" => $stop_id,
                                    "shipment_id" => $shft_id
                                ])
                                ->get();

                            $laststop = 0;
                            if ($statuses->count() > 0) {
                                $status = $prevsts = "S";
                                $gateout_details = DB::table('stop_status')
                                    ->select('id')
                                    ->where([
                                        "stop_id" => $stop_id,
                                        "shipment_id" => $shft_id,
                                        "status_id" => "3",
                                        "status" => "1",
                                        "stop_type" => 'D',
                                        "stop_detail_id" => $detailsid
                                    ])
                                    ->get();
                                if ($gateout_details->count() > 0) {
                                    $status = $prevsts = "C";
                                } else {
                                    $gateout_details1 = DB::table('stop_status')
                                        ->select('id')
                                        ->where([
                                            "stop_id" => $stop_id,
                                            "shipment_id" => $shft_id,
                                            "status_id" => "1",
                                            "status" => "1",
                                            "stop_type" => 'D',
                                            "stop_detail_id" => $detailsid
                                        ])
                                        ->get();
                                    if ($gateout_details1->count() > 0) {
                                        $laststop = 1;
                                    }
                                }
                            }

                            $stopname = "";
                            if (strlen($res->stopcity) > 0) {
                                $stopname .= ucfirst(str_replace("_", " ", $res->stopcity)) . " - ";
                            }
                            if (strlen($res->stopname) > 0) {
                                $stopname .= ucfirst(str_replace("_", " ", $res->stopname));
                            }
                            if ($status == "N" && $i == 0) {
                                $status = "S";
                            }
                            $result[] = [
                                'id' => $stop_id,
                                'name' => $stopname,
                                "stopname" => $res->stopname,
                                "address" => $res->address,
                                'plat' => $res->plat,
                                'plng' => $res->plng,
                                'ship_type' => $res->stoptype,
                                'shipment_weight' => $res->weight,
                                'shipment_volume' => $res->volume,
                                'shipment_units' => $res->ship_units ? $res->ship_units : 0,
                                'startdate' => strtotime($res->startdate),
                                'enddate' => strtotime($res->enddate),
                                'priority' => $res->ordernumber,
                                'status' => $status,
                                'order_id' => $emp->order_id,
                                "cargo" => $cargovalue ? $cargovalue->shipment_name : "",
                                "cargo_units" => $order_cargo_details,
                                'detail_id' => $detailsid,
                                'last_stop' => $laststop ?? 0,
                                'transport_mode' => $transportMode
                            ];
                            if ($status == "C" && $i == 0) {
                                $status = "S";
                            }
                        }
                    }
                    $i++;
                }
                $response = ["status" => 1, "data" => $result];
            } else {
                $response = ["status" => 0, "data" => "Not Found"];
            }
        }

        return response()->json($response);
    }

    public function getShipStatuses(Request $request)
    {
        $response = ['status' => 0, 'data' => 'Insufficient Input Data'];
        $data1 = [];
        $status = 0;

        $trip_id = $request->input('trip_id');
        $shipment_id = $request->input('shipment_id');
        $stop_id = $request->input('stop_id');
        $stop_detail_id = $request->input('stop_detail_id');
        $curtz = $request->input('timezone', 'America/New_York');
        $transportMode = '';

        if (!empty($trip_id)) {
            $sq = ['trip_id' => $trip_id];

            // Get shipment_id from trip if not provided
            if (empty($shipment_id)) {
                $trip = DB::table('trips')->select('shift_id')->where('id', $trip_id)->first();
                if ($trip) {
                    $shipment_id = $trip->shift_id;
                }
            }

            if (!empty($stop_id)) {
                $sq["stop_id"] = $stop_id;
            }
            if (!empty($stop_detail_id)) {
                $sq["stop_detail_id"] = $stop_detail_id;
            }

            // Check for signature doc (doc_type = 1)
            $signSign = DB::table('pod_uploads')->select('id')->where($sq)->first();
            if ($signSign) {
                $status = 1;
            }

            // Build where clause for stop_status
            $stopStatusQuery = DB::table('stop_status as s')
                ->join('status_master as m', 's.status_id', '=', 'm.id')
                ->select(
                    's.latitude',
                    's.longitude',
                    DB::raw("CONVERT_TZ(s.createdon, '+00:00', ?) as createdon", [$curtz]),
                    'm.status_name'
                );

            if (!empty($stop_id)) {
                $stopStatusQuery->where('s.stop_id', $stop_id);
            }
            if (!empty($stop_detail_id)) {
                $stopStatusQuery->where('s.stop_detail_id', $stop_detail_id);
            }
            if (!empty($trip_id)) {
                $stopStatusQuery->where('s.trip_id', $trip_id);
            } elseif (!empty($shipment_id)) {
                $stopStatusQuery->where('s.shipment_id', $shipment_id);
            }
            $stopStatusQuery->orderByDesc('s.id');

            // Get transport mode if shipment_id is available
            if (!empty($shipment_id)) {
                $order = DB::table('orders')->select('transport_mode')->where('shift_id', $shipment_id)->first();
                if ($order) {
                    $transportMode = $order->transport_mode;
                }
            }

            $results = $stopStatusQuery->get();

            if ($results->count() > 0) {
                foreach ($results as $res) {
                    // need to implement getLocationName() as a helper or service
                    // $placename = function_exists('getLocationName')
                    //     ? getLocationName($res->latitude, $res->longitude)
                    //     : '';
                    $placename = '';

                    $data1[] = [
                        'status_name' => $res->status_name,
                        'place' => $placename,
                        'latitude' => $res->latitude,
                        'longitude' => $res->longitude,
                        'createdon' => strtotime($res->createdon)
                    ];
                }
                $response = [
                    "status" => 1,
                    "data" => $data1,
                    'docstatus' => $status,
                    'transport_mode' => $transportMode
                ];
            } else {
                $response = [
                    "status" => 1,
                    "data" => $data1,
                    'docstatus' => $status,
                    'transport_mode' => $transportMode
                ];
            }
        }

        return response()->json($response);
    }

    public function closeTrip(Request $request)
    {
        $response = ["status" => 0, "data" => "Insufficient Input Data"];

        if ($request->isMethod('post')) {
            $driver_id = $request->input("driver_id");
            $trip_id = $request->input("trip_id");
            if (!empty($trip_id) && $trip_id > 0) {
                $curtz = $request->input('timezone', 'America/New_York');
                $trip_id = trim($trip_id);

                $gettrip = DB::table("trips")->select("shift_id")->where("id", $trip_id)->first();
                if ($gettrip) {
                    $curdt = now();
                    $shiftid = $gettrip->shift_id;
                    $data1 = [];
                    $data1["end_imei"] = $request->input("imei", ' ');
                    $data1["end_reading"] = $request->input("odometer", '0');
                    $shipment_id = $request->input("shipment_id", $shiftid);
                    $latitude = $request->input("latitude", '');
                    $longitude = $request->input("longitude", '');
                    $data1["etime"] = $data1["updated_on"] = $curdt;
                    $data1["status"] = '0';

                    $checkTrip = DB::table("stop_status")
                        ->where("trip_id", $trip_id)
                        ->where("shipment_id", $shiftid)
                        ->whereIn("status_code", ['0500', '2300'])
                        ->whereIn("stop_type", ['P', 'D'])
                        ->count();

                    if ($checkTrip <= 1) {
                        $response = ["status" => 2, "data" => "Can not close a shipment without delivering the goods"];
                    } else {
                        // Update shipment status
                        DB::table("shipment")->where("id", $shiftid)->update(["status" => '0', "updated_on" => $curdt]);
                        // Update trip
                        $res = DB::table("trips")->where(["id" => $trip_id, "driver_id" => $driver_id])->update($data1);

                        if ($res) {
                            if ($shiftid > 0) {
                                $getOrderId = DB::table("orders")->select("id")->where("shift_id", $shiftid)->first();
                                if ($getOrderId) {
                                    $ordeID = $getOrderId->id;
                                    if ($ordeID) {
                                        $orderInfo = $this->identifySubOrderType($ordeID);
                                        $parentOrderId = $orderInfo['parent_id'];
                                        $orderType = $orderInfo['order_type'];
                                        \Log::error("parentOrderId::" . json_encode($parentOrderId) . ", orderType::" . json_encode($orderType));
                                        if ($parentOrderId > 0 && $orderType === 'drop') {
                                            $parentOrderData = DB::table("orders")->select("shift_id", "trip_id")->where("id", $parentOrderId)->first();
                                            if ($parentOrderData) {
                                                $parentShiftId = $parentOrderData->shift_id;
                                                $parentTripId = $parentOrderData->trip_id;
                                                DB::table("shipment")->where("id", $parentShiftId)->update(["status" => '0']);
                                                DB::table("trips")->where("id", $parentTripId)->update($data1);
                                                DB::table("orders")->where("id", $parentOrderId)->update(["trip_sts" => '1']);
                                            }
                                        }
                                    }
                                }
                            }
                            // Once we have a generatesummary method, call it here
                            // $gensum = $this->generatesummary($trip_id, $curtz);
                            $response = ["status" => 1, "data" => "Closed Sucessfully"];
                        } else {
                            $response = ["status" => 0, "data" => "Failed to Close"];
                        }
                    }
                } else {
                    $response = ["status" => 0, "data" => "Failed to Close"];
                }
            } else {
                $response = ["status" => 0, "data" => "Failed to Close"];
            }
        }

        return response()->json($response);
    }

    public function generatesummary($tripid = null, $curtz = null)
    {
        if (empty($tripid)) {
            return;
        }

        $dist = $trip_type = $totalemp = $uid = 0;
        if (empty($curtz)) {
            $curtz = config('app.timezone', 'America/New_York');
        }
        $curdt = now();
        $reg = "";

        // Get trip info
        $trip = DB::table('trips')
            ->select(
                'shift_id', 'vehicle_id', 'driver_id',
                DB::raw("CONVERT_TZ(stime, '+00:00', '{$curtz}') AS stime"),
                DB::raw("CONVERT_TZ(etime, '+00:00', '{$curtz}') AS etime"),
                'etime AS tetime', 'start_imei', 'end_imei', 'start_reading', 'end_reading',
                'trip_type', 'plat', 'plng', 'dlat', 'dlng'
            )
            ->where('id', $tripid)
            ->where('status', '0')
            ->first();

        if (!$trip) {
            return;
        }

        $data = [];
        $data['driver_name'] = $data['driver_num'] = "";
        $data["tripinfo"] = $trip;
        $shift = $trip->shift_id;

        // Get shift info
        $shiftRow = DB::table('shipment')
            ->select(
                'user_id',
                'stime AS setime',
                DB::raw("CONVERT_TZ(startdate, '+00:00', '{$curtz}') AS startdate"),
                DB::raw("CONVERT_TZ(enddate, '+00:00', '{$curtz}') AS enddate"),
                'splace', 'eplace'
            )
            ->where('id', $shift)
            ->first();

        $data["trip"] = $shiftRow;
        $order_id = "";

        // Get employees for trip
        $emp1 = DB::table('shiporder_stop_sequence as e')
            ->join('trip_employee as te', 'te.employee_id', '=', 'e.id')
            ->leftJoin('cargo_details as m', 'm.id', '=', 'e.cargo_id')
            ->select(
                'e.name',
                DB::raw("CONVERT_TZ(te.stime, @@session.time_zone, '{$curtz}') AS in_time"),
                'te.status AS emp_status',
                'te.driver_late',
                'te.emp_late',
                'te.absent_reason',
                'm.material',
                'e.shipment_volume',
                'e.shipment_weight',
                'e.order_id'
            )
            ->where('te.trip_id', $tripid)
            ->get();

        if ($emp1->count() > 0) {
            $order_id = $emp1[0]->order_id;
        }
        if (empty($order_id)) {
            $order = DB::table('shiporder_stop_sequence')
                ->select('order_id')
                ->where('shift_id', $shift)
                ->first();
            if ($order) {
                $order_id = $order->order_id;
            }
        }

        // Attendance count
        $emp = DB::table('trip_employee')
            ->select(
                DB::raw("COALESCE(SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END), 0) AS attcnt"),
                DB::raw("COUNT(employee_id) AS totcnt")
            )
            ->where('trip_id', $tripid)
            ->first();

        $start = $trip->stime;
        $end = $trip->etime;
        $imei = $trip->start_imei;
        $eimei = $trip->end_imei;
        $driverid = $trip->driver_id;
        $vehicleid = $trip->vehicle_id;
        $stime = $shiftRow ? $shiftRow->startdate : '';
        $etime = $shiftRow ? $shiftRow->enddate : '';
        $uid = $shiftRow ? $shiftRow->user_id : 0;
        $odometer_start = trim($trip->start_reading ?? 0);
        $odometer_end = trim($trip->end_reading ?? 0);
        $trip_type = $trip->trip_type;
        $vendor_id = 0;

        // Truck info
        $trucks_query = DB::table('trucks_data')
            ->select('truck_capacity', 'register_number', 'vendor_id', 'truck_weight', 'truck_volume')
            ->where('id', $vehicleid)
            ->first();

        $cab_capacity = $truck_weight = $truck_volume = 0;
        if ($trucks_query) {
            $cab_capacity = $trucks_query->truck_capacity;
            $reg = $trucks_query->register_number;
            $vendor_id = $trucks_query->vendor_id;
            $truck_weight = $trucks_query->truck_weight;
            $truck_volume = $trucks_query->truck_volume;
        }
        if (empty($cab_capacity)) {
            $cab_capacity = 0;
        }

        // Driver info
        $regg = DB::table('truck_drivers')
            ->select('name', 'contact_num', 'vendor_id')
            ->where('id', $driverid)
            ->first();
        if ($regg) {
            $data['driver_name'] = $regg->name;
            $data['driver_num'] = $regg->contact_num;
            $vnid = $regg->vendor_id;
            if ($vendor_id == 0 && $vnid != 0) {
                $vendor_id = $vnid;
            }
        }

        // Update trip_sts in orders
        DB::table('orders')->where('shift_id', $shift)->update(['trip_sts' => '1']);

        // Get trip locations for distance calculation
        $locations = DB::table('rtdrive_locations')
            ->select(
                'latitude',
                'longitude',
                DB::raw("CONVERT_TZ(timestamp, '+00:00', ?) as timestamp", [$curtz])
            )
            ->where('mobileimei', $imei)
            ->where('timestamp', '<=', $end)
            ->where('timestamp', '>=', $start)
            ->orderBy('timestamp', 'asc')
            ->get();

        $flag = 0;
        $lat = $lng = 0;
        $dist = 0;
        foreach ($locations as $row) {
            if ($flag == 0) {
                $flag = 1;
            } else {
                $a = $this->calculateDistance12($lat, $lng, $row->latitude, $row->longitude);
                $dist += $a;
            }
            $lat = $row->latitude;
            $lng = $row->longitude;
        }

        // Attendance
        $attendemp = 0;
        $totalemp = DB::table('trip_employee')->where('trip_id', $tripid)->count();
        if ($emp) {
            $attendemp = $emp->attcnt;
        }
        $data["noofemp"] = $totalemp;

        // Driver late calculation
        $driver_late = 0.00;
        $setime = $shiftRow ? date("Y-m-d H:i:s", strtotime($shiftRow->setime)) : '';
        $tetime = date("Y-m-d H:i:s", strtotime(date("H:i:s", strtotime($trip->tetime))));
        $to_time = strtotime($setime);
        $from_time = strtotime($tetime);
        if ($from_time > $to_time) {
            $driver_late = round(abs($from_time - $to_time) / 60, 2);
        }

        $arr = [
            'trip_id' => $tripid,
            'vehicle_id' => $vehicleid,
            'driver_id' => $driverid,
            'vendor_id' => $vendor_id,
            'trip_distance' => $dist,
            'no_of_emp' => $totalemp,
            'attended_emp' => $attendemp,
            'start_imei' => $imei,
            'end_imei' => $eimei,
            'start_time' => $stime,
            'end_time' => $etime,
            'user_id' => $uid,
            'cab_capacity' => $cab_capacity,
            'trip_status' => '1',
            'trip_type' => $trip_type,
            'ship_delay' => $driver_late,
            'createdon' => $curdt
        ];

        $chktrip = DB::table('trip_summaries')->where('trip_id', $tripid)->first();
        if (!$chktrip) {
            DB::table('trip_summaries')->insert($arr);
        }

        $data["trip_id"] = $tripid;
        $shifttime = 0;
        if ($trip_type != 2) {
            $shti = $shiftRow ? $shiftRow->startdate : '';
            $shifttime = $shti ? date('H:i A', strtotime($shti)) : '';
        } else {
            $shifttime = "Empty";
        }
        $data["driver_late"] = $driver_late;
        $data["empshifttime"] = $shifttime;
        $data["distance"] = $dist;
        $data["stime"] = $stime;
        $data["etime"] = $etime;
        $data["simei"] = $imei;
        $data["eimei"] = $eimei;
        if ($odometer_end == "") {
            $odometer_end = 0;
        }
        if ($odometer_start == "") {
            $odometer_start = 0;
        }
        $data["odometer"] = ($odometer_end - $odometer_start);
        $data["page_title"] = "Trip Report";
        $data["capacity"] = $cab_capacity;
        $data["employees"] = $emp1;
        $data["trip_type"] = $trip_type;
        $data["register_number"] = $reg;
        $data["truck_weight"] = $truck_weight;
        $data["truck_volume"] = $truck_volume;
        $data["ep"] = "";

        $getusermail = DB::table('sx_users')
            ->select('employee_name', 'emailid')
            ->where('id', $uid)
            ->where('emailid', '!=', '')
            ->first();

        if ($getusermail) {
            $receivemail = $getusermail->emailid;
            $receivename = $getusermail->employee_name;
            $insertdata = [
                'trip_id' => $tripid,
                'shift_id' => $shift,
                'splace' => $data["trip"]->splace ?? '',
                'eplace' => $data["trip"]->eplace ?? '',
                'stime' => $stime,
                'etime' => $etime,
                'capacity' => $cab_capacity,
                'simei' => $data['simei'],
                'eimei' => $data['eimei'],
                'totcnt' => $totalemp,
                'attcnt' => $attendemp,
                'distance' => $data['distance'],
                'odometer' => $data['odometer'],
                'driver_late' => $data['driver_late'],
                'receivemail' => $receivemail,
                'receivename' => $receivename,
                'user_id' => $uid,
                'createdon' => $curdt
            ];
            DB::table('trips_mails')->insert($insertdata);
        }
    }

    // Helper for distance calculation (Haversine formula)
    protected function calculateDistance12($lat1, $lon1, $lat2, $lon2)
    {
        // Returns distance in kilometers
        $earthRadius = 6371;
        $lat1 = floatval($lat1);
        $lon1 = floatval($lon1);
        $lat2 = floatval($lat2);
        $lon2 = floatval($lon2);

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        $distance = $earthRadius * $c;
        return $distance;
    }

    public function setshipAbort(Request $request)
    {
        $response = ["status" => 0, "data" => "Insufficient Input Data"];

        if ($request->isMethod('post')) {
            $curtz = $request->input('timezone', 'America/New_York');
            $curdt = now();
            $latitude = $request->input('latitude', '');
            $longitude = $request->input('longitude', '');
            $shipment_id_input = $request->input('shipment_id', '0');
            $trip_id = $request->input('trip_id', 0);
            $driver_id = $request->input('driver_id', '0');
            $vehicle_id = $request->input('vehicle_id', '0');
            $reason = $request->input('reason', '');
            $fileName = "";
            $ordid = $podinsid = 0;
            $legsorder_id = "";
            $uid = 5;

            $shipmentRow = DB::table('shipment')->where('shipmentid', $shipment_id_input)->first();
            $shipment_id = $shipmentRow ? $shipmentRow->id : 0;

            if ($shipment_id > 0) {
                // Handle file upload
                if ($request->hasFile('photo')) {
                    $file = $request->file('photo');
                    $allowed = ['jpg', 'jpeg', 'gif', 'png', 'pdf'];
                    if (in_array($file->getClientOriginalExtension(), $allowed)) {
                        $fileName = uniqid('abort_') . '.' . $file->getClientOriginalExtension();
                        $file->move(public_path('assets/poduploads'), $fileName);

                        // Get order info
                        $chkord = DB::table('orders as o')
                            ->join('shiporder_stop_sequence as e', 'o.order_id', '=', 'e.order_id')
                            ->where('e.shift_id', $shipment_id)
                            ->where('e.ship_type', 'P')
                            ->select('o.id', 'o.order_id', 'o.user_id')
                            ->first();

                        if ($chkord) {
                            $ordid = $chkord->id ?? 0;
                            $legsorder_id = $chkord->order_id;
                            $uid = $chkord->user_id;
                        }

                        // Insert pod_uploads
                        $filedata = [
                            'trip_id' => $trip_id,
                            'shipment_id' => $shipment_id,
                            'latitude' => $latitude,
                            'longitude' => $longitude,
                            'doc_type' => 3,
                            'imgpath' => $fileName,
                            'order_id' => $ordid,
                            'createdby' => $driver_id,
                            'status' => '1',
                            'createdon' => $curdt,
                        ];
                        // Once we have a geographyPoint helper, use it here
                        // $filedata['geolocation'] = geographyPoint($filedata['longitude'], $filedata['latitude']);
                        $podinsid = DB::table('pod_uploads')->insertGetId($filedata);
                    }
                }

                // If order id not set, try to get it again
                if ($ordid == 0) {
                    $chkord = DB::table('orders as o')
                        ->join('shiporder_stop_sequence as e', 'o.order_id', '=', 'e.order_id')
                        ->where('e.shift_id', $shipment_id)
                        ->where('e.ship_type', 'P')
                        ->select('o.id', 'o.order_id', 'o.user_id')
                        ->first();
                    if ($chkord) {
                        $ordid = $chkord->id;
                        $legsorder_id = $chkord->order_id;
                        $uid = $chkord->user_id;
                        if ($podinsid > 0) {
                            DB::table('pod_uploads')->where('id', $podinsid)->update(['order_id' => $ordid]);
                        }
                    }
                }

                if ($ordid > 0) {
                    // Get country hours
                    $gethrs = DB::table('country_master as c')
                        ->join('sx_users as u', 'c.country_code', '=', 'u.country_code')
                        ->where('u.id', $uid)
                        ->where('c.status', '1')
                        ->select('c.cntry_hrs')
                        ->first();
                    $hrs = $gethrs ? $gethrs->cntry_hrs : '';

                    $postdata = [
                        "shipment_id" => $shipment_id,
                        "trip_id" => $trip_id,
                        "driver_id" => $driver_id,
                        "stop_id" => "",
                        "order_id" => $legsorder_id,
                        "latitude" => $latitude,
                        "longitude" => $longitude,
                        "curtz" => $curtz,
                        "hrs" => $hrs
                    ];

                    $statuscode = "0217";
                    $addstatus = [
                        'shipment_id' => $shipment_id,
                        'stop_id' => 0,
                        'trip_id' => $trip_id,
                        'status_id' => 23,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'status_code' => $statuscode,
                        'reason' => $reason,
                        'vehicle_id' => $vehicle_id,
                        'driver_id' => $driver_id,
                        'status' => '0',
                        'createdon' => $curdt,
                    ];
                    // Once we have a geographyPoint helper, use it here
                    // $addstatus['geolocation'] = geographyPoint($addstatus['longitude'], $addstatus['latitude']);
                    DB::table('stop_status')->insert($addstatus);
                }

                $response = [
                    "status" => '1',
                    "data" => "Updated",
                    "ordid" => $ordid
                ];
            }
        }

        return response()->json($response);
    }

    public function rescheduleShift(Request $request)
    {
        $response = ["status" => "0", "message" => "Invalid input parameters!"];
        $data = $request->input('data');
        $curtz = $request->input('timezone', 'America/New_York');

        if ($data) {
            $post = is_array($data) ? (object)$data : json_decode($data);

            $shift_id = isset($post->shift_id) ? $post->shift_id : "";
            $vehicle_id = isset($post->vehicle_id) ? $post->vehicle_id : "";
            $trip_type = isset($post->trip_type) ? $post->trip_type : "";
            $driver_id = isset($post->driver_id) ? $post->driver_id : "";
            $imei = isset($post->imei) ? $post->imei : "";
            $user_id = isset($post->user_id) ? $post->user_id : "";
            $latitude = isset($post->latitude) ? $post->latitude : "";
            $longitude = isset($post->longitude) ? $post->longitude : "";
            $reschedule_date = isset($post->reschedule_date) ? $post->reschedule_date : "";
            $reason = isset($post->reason) ? $post->reason : "";

            if (
                strlen($reschedule_date) > 0 &&
                strlen($shift_id) > 0 &&
                strlen($vehicle_id) > 0 &&
                strlen($user_id) > 0
            ) {
                // Get vendor email
                $vendor = DB::table('trucks_data as vv')
                    ->leftJoin('sx_party_members as v', 'v.id', '=', 'vv.vendor_id')
                    ->where('vv.status', '1')
                    ->where('vv.id', $vehicle_id)
                    ->where('v.email', '!=', '')
                    ->select('v.email')
                    ->first();

                // Get user info
                $user = DB::table('sx_users')
                    ->select('employee_name', 'emailid')
                    ->where('id', $user_id)
                    ->first();

                // Get driver info
                $driver = DB::table('truck_drivers')
                    ->select('name', 'contact_num')
                    ->where('id', $driver_id)
                    ->first();

                // Get shift info
                $shift = DB::table('shipment')
                    ->select(
                        DB::raw("CONVERT_TZ(startdate, '+00:00', '{$curtz}') AS startdate"),
                        'shipmentid'
                    )
                    ->where('id', $shift_id)
                    ->first();

                $point = [$longitude, $latitude];
                $insert = [
                    "driver_id" => $driver_id,
                    "shift_id" => $shift_id,
                    "reason" => $reason,
                    "reschedule_time" => $reschedule_date,
                    "longitude" => $longitude,
                    "latitude" => $latitude,
                    "location" => $point,
                    "created_at" => now(),
                    "status" => 1
                ];

                DB::table('trip_reschedule')->insert($insert);

                $response = ["status" => "1", "message" => "Status updated sucessfully!"];
            }
        }

        return response()->json($response);
    }

    public function setShipReject(Request $request)
    {
        $response = ["status" => 0, "message" => "Insufficient Input Data"];

        $data = $request->input('data');
        $curtz = $request->input('timezone', 'America/New_York');
        $curdt = now();

        if ($data) {
            $post = is_array($data) ? (object)$data : json_decode($data);

            $trip_id = isset($post->trip_id) ? $post->trip_id : null;
            $latitude = isset($post->latitude) ? $post->latitude : '';
            $longitude = isset($post->longitude) ? $post->longitude : '';
            $reason = isset($post->reason) ? $post->reason : '';
            $type = isset($post->type) ? $post->type : '1';
            $vehicle_id = isset($post->vehicle_id) ? $post->vehicle_id : '';
            $driver_id = isset($post->driver_id) ? $post->driver_id : '';
            $shipment_id = isset($post->shipment_id) ? $post->shipment_id : '';
            $status = 6;
            $statuscode = "0213";
            $order_id = null;

            if ($shipment_id !== "" && $driver_id > 0) {
                if ($type == '1') {
                    $order = DB::table('orders')
                        ->where('shift_id', $shipment_id)
                        ->where('trip_sts', 0)
                        ->first();

                    if ($order) {
                        $order_id = $order->id;
                        DB::table('trips')
                            ->where('shift_id', $shipment_id)
                            ->update(['status' => 2, 'updated_on' => $curdt]);
                    }
                }

                $addstatus = [
                    'order_id' => $order_id,
                    'shipment_id' => $shipment_id,
                    'stop_id' => 0,
                    'trip_id' => $trip_id,
                    'status_id' => $status,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'status_code' => $statuscode,
                    'reason' => $reason,
                    'vehicle_id' => $vehicle_id,
                    'driver_id' => $driver_id,
                    'status' => 0,
                    'createdon' => $curdt,
                ];

                DB::table('shft_veh')
                    ->where('shft_id', $shipment_id)
                    ->update(['status' => '0']);

                DB::table('stop_status')->insert($addstatus);

                $response = ["status" => 1, "data" => "Updated"];
            }
        }

        return response()->json($response);
    }

    public function getShipmentDocuments(Request $request)
    {
        $shipment_id = $request->input('shipment_id', '');
        $stop_id = $request->input('stop_id', '');

        if ($shipment_id && $stop_id) {
            $result = DB::table('pod_uploads as pu')
                ->leftJoin('document_types as dtm', 'pu.doc_type', '=', 'dtm.id')
                ->select('pu.imgpath', 'dtm.type_name as document_type')
                ->where('pu.shipment_id', $shipment_id)
                ->where('pu.stop_id', $stop_id)
                ->get();

            $response = [];
            foreach ($result as $data) {
                $response[] = [
                    "document_type" => $data->document_type,
                    "document_path" => url("assets/poduploads/" . $data->imgpath)
                ];
            }
            return response()->json(["status" => 1, "data" => $response]);
        } else {
            return response()->json(["status" => 0, "data" => "Insufficient Input Data"]);
        }
    }

    public function updatePassword(Request $request)
    {
        $response = ["status" => 0, "data" => "Not Found"];
        $driver_id = $request->input('driver_id', 0);
        $user_id = $request->input('user_id', 0);
        $newpassword = $request->input('newpassword', '');

        if ($driver_id > 0 && $user_id > 0 && $newpassword != "") {
            $driver = DB::table('truck_drivers')->where('id', $driver_id)->first();
            if ($driver) {
                $pwd = md5($newpassword);
                $upd = [
                    "password" => $pwd,
                    "pwd_ref" => $newpassword
                ];
                DB::table('truck_drivers')->where('id', $driver_id)->update($upd);
                $response = ["status" => 1];
            }
        }

        return response()->json($response);
    }

    public function savePickup($emp, $lat, $lng)
    {
        $data = [];
        $data["plat"] = $lat;
        $data["plng"] = $lng;
        // $data['pgeolocation'] = geographyPoint($data['plng'], $data['plat']);
        $loc = '';// getLocationName($lat, $lng);
        if (strlen($loc) > 0) {
            $type = 'P';
            $emptype = $this->db->select('ship_type')->get_where('shiporder_stop_sequence', array('id' => $emp), 1, 0);
            if ($emptype->num_rows() > 0) {
                $type = $emptype->row()->ship_type;
            }
            if ($type == 'P') {
                $data["pickup"] = $data["address"] = $loc;
            } else {
                $data["dlat"] = $lat;
                $data["dlng"] = $lng;
                /* $data['dgeolocation'] = geographyPoint($data['dlng'], $data['dlat']);*/
                $data['drop'] = $data["address"] = $loc;
            }
        }
        $res = $this->db->where("id", $emp)->update("shiporder_stop_sequence", $data);
    }

    public function checkLatLng($emp)
    {
        $res = DB::table('shiporder_stop_sequence')
            ->select('plat', 'plng')
            ->where('id', $emp)
            ->first();

        if ($res) {
            if ($res->plat == "" || $res->plng == "") {
                return "true";
            } else {
                return "false";
            }
        } else {
            return "false";
        }
    }

    public function getStopStatusId($stop_type, $status_type)
    {
        $stop_type = strtolower($stop_type);
        if (($stop_type == 'p' || $stop_type == 'd') && $status_type == 1) {
            return optional($this->getStatusId('Pickup Or Drop'))->id;
        } elseif (($stop_type == 'p' || $stop_type == 'd') && $status_type == 2) {
            return optional($this->getStatusId('Gate In'))->id;
        } elseif (($stop_type == 'p' || $stop_type == 'd') && $status_type == 3) {
            return optional($this->getStatusId('Gate Out'))->id;
        } elseif (($stop_type == 'p' || $stop_type == 'd') && $status_type == 4) {
            return optional($this->getStatusId('In-Transit'))->id;
        }
        return null;
    }

    public function getStatusId($status_name)
    {
        return DB::table('status_master')
            ->select('id')
            ->where('status_name', $status_name)
            ->orderBy('id', 'asc')
            ->first();
    }

    public function setShipStopStatus(Request $request)
    {
        $response = ["status" => "0", "data" => "Insufficient Input Data"];

        $data = $request->all();
        $curtz = $request->input('timezone', 'America/New_York');
        $latitude = $request->input('latitude', '');
        $longitude = $request->input('longitude', '');
        $trip_id = $request->input('trip_id', '');
        $shipment_id = $request->input('shipment_id', null);
        $stop_id = $request->input('stop_id', '0');
        $employee_id = $request->input('employee_id', '');
        $stop_type = $request->input('stop_type', '');
        $driver_id = $request->input('driver_id', '');
        $stop_detail_type = $request->input('stop_detail_type', '');
        $status_type = $request->input('status_type', '');
        $image_orientation = intval($request->input('image_orientation', 0));
        $curdt = now();
        $file_upload_flag = false;
        $legsorder_id = "";
        $ordid = null;
        $insrt = false;
        $getOrderId = $codes = [];
        $fileName = "";

        if ($ordid == null && !empty($shipment_id)) {
            $getOrderId = DB::table('orders')
                ->select('id', 'company_code', 'branch_code')
                ->where('shift_id', $shipment_id)
                ->first();
            if ($getOrderId) {
                $ordid = $getOrderId->id;
                $codes['companyCode'] = $getOrderId->company_code;
                $codes['branchCode'] = $getOrderId->branch_code;
            }
        }

        // Handle file upload
        if ($request->hasFile('file_name')) {
            $file = $request->file('file_name');
            $allowed = ['jpg', 'jpeg', 'gif', 'png', 'pdf'];
            $file_ext = strtolower($file->getClientOriginalExtension());
            if (in_array($file_ext, $allowed)) {
                if ($file_ext == "pdf") {
                    $fileName = uniqid('pod_') . '.' . $file_ext;
                    $file->move(public_path('assets/poduploads'), $fileName);
                } else {
                    $fileName = date('dmyhis') . '_' . $file->getClientOriginalName();
                    $filePath = public_path("assets/poduploads/" . $fileName);
                    $tempFilePath = $file->getRealPath();
                    $quality = 20;

                    if ($file_ext == "png") {
                        $image = imagecreatefrompng($tempFilePath);
                        imagealphablending($image, false);
                        imagesavealpha($image, true);
                        imagepng($image, $filePath, 9);
                    } else {
                        $image = imagecreatefromjpeg($tempFilePath);
                        if ($image && ($file_ext == "jpg" || $file_ext == "jpeg")) {
                            $exif = @exif_read_data($tempFilePath);
                            if (!empty($exif['Orientation'])) {
                                $image_orientation = $exif['Orientation'];
                            }
                        }
                        // Rotate if needed
                        $angle = 0;
                        switch ($image_orientation) {
                            case 3: $angle = 180; break;
                            case 6: $angle = -90; break;
                            case 8: $angle = 90; break;
                            default: $angle = 0;
                        }
                        if ($angle != 0) {
                            $image = imagerotate($image, $angle, 0);
                        }
                        imagejpeg($image, $filePath, $quality);
                    }
                    if (isset($image)) {
                        imagedestroy($image);
                    }
                }

                // Determine stop_type if not set
                if ($stop_type == "") {
                    $stop_type = "P";
                }

                // Get order and user id for this stop
                if ($stop_id != "") {
                    // Check if stoptype is 'D' for this stop_id
                    $chkstype = DB::table('shiporder_stops')
                        ->select('stoptype')
                        ->where('id', $stop_id)
                        ->where('stoptype', 'D')
                        ->first();
                    if ($chkstype) {
                        $stop_type = $chkstype->stoptype;
                    }
                    $chkord = DB::table('orders as o')
                        ->join('shiporder_stop_sequence as e', 'o.order_id', '=', 'e.order_id')
                        ->where('e.shift_id', $shipment_id)
                        ->where(function ($q) use ($stop_id) {
                            $q->where('e.stop_id', $stop_id)
                              ->orWhere('e.drop_stopid', $stop_id);
                        })
                        ->select('o.id', 'o.order_id', 'o.user_id')
                        ->first();
                    if ($chkord) {
                        $ordid = $chkord->id;
                        $legsorder_id = $chkord->order_id;
                        $uid = $chkord->user_id;
                    }
                }

                // Insert pod_uploads
                $filedata = [
                    'shipment_id' => $shipment_id,
                    'stop_id' => $stop_id,
                    'stop_detail_id' => $employee_id,
                    'stop_type' => $stop_type,
                    'trip_id' => $trip_id,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'doc_type' => $status_type,
                    'imgpath' => $fileName,
                    'order_id' => $ordid,
                    'status' => '1',
                    'createdon' => $curdt,
                    'createdby' => $driver_id
                ];
                DB::table('pod_uploads')->insert($filedata);
                $file_upload_flag = true;
            }
        }

        // Get transport mode
        $transportMode = "LTL";
        if (!empty($shipment_id)) {
            $getTransportMode = DB::table('orders')->select('transport_mode')->where('shift_id', $shipment_id)->first();
            if ($getTransportMode) {
                $transportMode = $getTransportMode->transport_mode;
            }
        }

        // Main status logic
        if (!empty($employee_id) && !empty($trip_id)) {
            $vehicle = $driver = 0;
            $checkintrans = DB::table('trips')
                ->select('id', 'vehicle_id', 'driver_id')
                ->where('id', $trip_id)
                ->where('transit_status', 1)
                ->first();
            if (!$checkintrans) {
                DB::table('trips')->where('id', $trip_id)->update(['transit_status' => 1]);
                $checktrip = DB::table('trips')->select('id', 'vehicle_id', 'driver_id')->where('id', $trip_id)->first();
                if ($checktrip) {
                    $vehicle = $checktrip->vehicle_id;
                    $driver = $checktrip->driver_id;
                }
            } else {
                $vehicle = $checkintrans->vehicle_id;
                $driver = $checkintrans->driver_id;
            }

            $tdata = [
                'employee_id' => $employee_id,
                'trip_id' => $trip_id,
                'stop_id' => $stop_id
            ];

            $status = $request->input('status', '');
            $stsreason = $status;
            $pd_status = $status;
            $stime = $created_on = $check_in = $curdt;
            $notests = 0;
            $stscode = "0420";
            // Status code logic
            if ($stsreason == 2 && $stop_type == "P") { $stscode = "0420"; $notests = 2; }
            if ($stsreason == 2 && $stop_type == "D") { $stscode = "0192"; $notests = 2; }
            if ($stsreason == 3 && $stop_type == "P") { $stscode = "0191"; $notests = 3; }
            if ($stsreason == 3 && $stop_type == "D") { $stscode = "3000"; $notests = 3; }
            if ($stsreason == 1 && $stop_type == "P") { $stscode = "0500"; $notests = 1; }
            if ($stsreason == 1 && $stop_type == "D") { $stscode = "2300"; $notests = 5; }
            if ($stsreason == 4) { $stscode = "1550"; $notests = 4; }

            // Parent order logic
            $parentOrderId = 0;
            $orderType = null;
            $arr = [];
            if ($ordid > 0) {
                $orderInfo = $this->identifySubOrderType($ordid);
                $parentOrderId = $orderInfo['parent_id'];
                $orderType = $orderInfo['order_type'];
                $arr = [
                    'stop_type' => $stop_type,
                    'vehicle_id' => $vehicle,
                    'driver_id' => $driver,
                    'contact_num' => '',
                    'company_code' => $getOrderId->company_code ?? '',
                    'curdt' => $curdt,
                    'crossborder' => 0,
                    'stopid' => $stop_id,
                    'stopdetailid' => $employee_id,
                ];
                if (in_array($stsreason, [12, 13, 14, 15])) {
                    $stsreason = 1;
                }
            }

            // Check if stop_status already exists
            $chksts = [
                'shipment_id' => $shipment_id,
                'order_id' => $ordid,
                'stop_id' => $stop_id,
                'stop_detail_id' => $employee_id,
                'stop_type' => $stop_type,
                'trip_id' => $trip_id,
                'status_id' => $stsreason,
                'status' => $status
            ];
            $checksts = DB::table('stop_status')->where($chksts)->first();

            if (!$checksts) {
                // Insert stop_status
                $data1 = [
                    'shipment_id' => $shipment_id,
                    'order_id' => $ordid,
                    'stop_id' => $stop_id,
                    'stop_detail_id' => $employee_id,
                    'stop_type' => $stop_type,
                    'trip_id' => $trip_id,
                    'status_id' => $this->getStopStatusId($stop_type, $stsreason),
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'status' => $status,
                    'status_code' => $stscode,
                    'reason' => "From Mobile",
                    'vehicle_id' => $vehicle,
                    'driver_id' => $driver,
                    'createdon' => $curdt
                ];
                $insrt = DB::table('stop_status')->insert($data1);

                // Main order status update
                if ($parentOrderId > 0 && $orderType === 'pickup') {
                    $stsreason = $this->getStopStatusId($stop_type, $stsreason) ?? $stsreason;
                    $this->updateMainOrderStatus($parentOrderId, $stsreason, $stscode, $latitude, $longitude, $curdt, $orderType, "From Mobile", $arr);
                } elseif ($parentOrderId > 0 && $orderType === 'drop') {
                    $stsreason = $this->getStopStatusId($stop_type, $stsreason) ?? $stsreason;
                    $this->updateMainOrderStatus($parentOrderId, $stsreason, $stscode, $latitude, $longitude, $curdt, $orderType, "From Mobile", $arr);
                }

                // Pickup Gate Out and In Transit
                if ($insrt && $stscode == "0500" && $stsreason == 1 && $stop_type == "P") {
                    $dataForPickupGateOut = [
                        'shipment_id' => $shipment_id,
                        'order_id' => $ordid,
                        'stop_id' => $stop_id,
                        'stop_detail_id' => $employee_id,
                        'stop_type' => "P",
                        'trip_id' => $trip_id,
                        'status_id' => 3,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'status' => $status,
                        'status_code' => "0191",
                        'reason' => "From Mobile",
                        'vehicle_id' => $vehicle,
                        'driver_id' => $driver,
                        'createdon' => $curdt
                    ];
                    $pickupGateOutInsert = DB::table('stop_status')->insert($dataForPickupGateOut);
                    if ($pickupGateOutInsert && $parentOrderId > 0 && $orderType === 'pickup') {
                        $this->updateMainOrderStatus($parentOrderId, 3, "0191", $latitude, $longitude, $curdt, $orderType, "From Mobile", $arr);
                    }
                    $dataForInTransit = [
                        'shipment_id' => $shipment_id,
                        'order_id' => $ordid,
                        'stop_id' => $stop_id,
                        'stop_detail_id' => $employee_id,
                        'stop_type' => "P",
                        'trip_id' => $trip_id,
                        'status_id' => 4,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'status' => $status,
                        'status_code' => "1550",
                        'reason' => "From Mobile",
                        'vehicle_id' => $vehicle,
                        'driver_id' => $driver,
                        'createdon' => $curdt
                    ];
                    $inTransitInsert = DB::table('stop_status')->insert($dataForInTransit);
                    if ($inTransitInsert && $parentOrderId > 0 && $orderType === 'pickup') {
                        $this->updateMainOrderStatus($parentOrderId, 4, "1550", $latitude, $longitude, $curdt, $orderType, "From Mobile", $arr);
                    }
                }

                // Delivery Gate Out
                if ($insrt && $stscode == "2300" && $stsreason == 1 && $stop_type == "D") {
                    $dataForDeliveryGateOut = [
                        'shipment_id' => $shipment_id,
                        'order_id' => $ordid,
                        'stop_id' => $stop_id,
                        'stop_detail_id' => $employee_id,
                        'stop_type' => "D",
                        'trip_id' => $trip_id,
                        'status_id' => 3,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'status' => $status,
                        'status_code' => "3000",
                        'reason' => "From Mobile",
                        'vehicle_id' => $vehicle,
                        'driver_id' => $driver,
                        'createdon' => $curdt
                    ];
                    $deliveryGateOutInsert = DB::table('stop_status')->insert($dataForDeliveryGateOut);
                    if ($deliveryGateOutInsert && $parentOrderId > 0 && $orderType === 'drop') {
                        $this->updateMainOrderStatus($parentOrderId, 3, "3000", $latitude, $longitude, $curdt, $orderType, "From Mobile", $arr);
                    }
                }

                if ($transportMode != "FTL") {
                    $data1 = [
                        'shipment_id' => $shipment_id,
                        'order_id' => $ordid,
                        'stop_id' => $stop_id,
                        'stop_detail_id' => $employee_id,
                        'stop_type' => $stop_type,
                        'trip_id' => $trip_id,
                        'status_id' => $this->getStopStatusId($stop_type, $stsreason),
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'status' => $status,
                        'status_code' => $stscode,
                        'reason' => "From Mobile",
                        'vehicle_id' => $vehicle,
                        'driver_id' => $driver,
                        'createdon' => $curdt
                    ];
                    $insrt = DB::table('stop_status')->insert($data1);
                    if ($parentOrderId > 0 && $orderType === 'pickup') {
                        $stsreason = $this->getStopStatusId($stop_type, $stsreason) ?? $stsreason;
                        $this->updateMainOrderStatus($parentOrderId, $stsreason, $stscode, $latitude, $longitude, $curdt, $orderType, "From Mobile", $arr);
                    } elseif ($parentOrderId > 0 && $orderType === 'drop') {
                        $stsreason = $this->getStopStatusId($stop_type, $stsreason) ?? $stsreason;
                        $this->updateMainOrderStatus($parentOrderId, $stsreason, $stscode, $latitude, $longitude, $curdt, $orderType, "From Mobile", $arr);
                    }
                }
            }

            // Save driver location
            if (!empty($latitude) && !empty($longitude)) {
                $driverData = DB::table('truck_drivers')->select('imei')->where('id', $driver_id)->first();
                if ($driverData && $driverData->imei != "") {
                    $exists = DB::table('rtdrive_locations')
                        ->where([
                            'driver_id' => $driver_id,
                            'trip_id' => $trip_id,
                            'vehicle_id' => $vehicle,
                            'latitude' => $latitude,
                            'longitude' => $longitude,
                            'mobileimei' => $driverData->imei
                        ])->first();
                    if (!$exists) {
                        DB::table('rtdrive_locations')->insert([
                            'driver_id' => $driver_id,
                            'trip_id' => $trip_id,
                            'vehicle_id' => $vehicle,
                            'latitude' => $latitude,
                            'longitude' => $longitude,
                            'speed' => 50,
                            'mobileimei' => $driverData->imei,
                            'battery' => 60,
                            'fuel' => 70,
                            'timestamp' => $curdt
                        ]);
                    }
                }
            }

            // If legsorder_id is empty, try to get it from shiporder_stop_sequence
            if ($legsorder_id == "") {
                $shift = DB::table('shiporder_stop_sequence')
                    ->select('order_id', 'user_id')
                    ->where(function ($q) use ($stop_id) {
                        $q->where('stop_id', $stop_id)
                          ->orWhere('drop_stopid', $stop_id);
                    })
                    ->limit(1)
                    ->first();
                if ($shift) {
                    $legsorder_id = $shift->order_id;
                    $uid = $shift->user_id;
                }
            }

            // Count pod_uploads for this trip
            $cnt = DB::table('pod_uploads')->where('trip_id', $trip_id)->count();
            if ($cnt == 0) {
                $cntstr = "001";
            } else {
                $cntstr = "00" . $cnt;
                if ($cnt > 9) {
                    $cntstr = "0" . $cnt;
                }
            }

            // Get country hours
            $gethrs = DB::table('country_master as c')
                ->join('sx_users as u', 'c.country_code', '=', 'u.country_code')
                ->where('u.id', $uid)
                ->where('c.status', '1')
                ->select('c.cntry_hrs')
                ->limit(1)
                ->first();
            if ($gethrs) {
                $hrs = $gethrs->cntry_hrs;
            } else {
                $hrs = null;
            }

            $postdata = [
                "shipment_id" => $shipment_id,
                "trip_id" => $trip_id,
                "driver_id" => $driver_id,
                "stop_id" => $stop_id,
                "order_id" => $legsorder_id,
                "inc_id" => $cntstr,
                "pod_type" => $status_type,
                "latitude" => $latitude,
                "longitude" => $longitude,
                "stop_type" => $stop_type,
                "vehicle_id" => $vehicle,
                "curtz" => $curtz,
                "hrs" => $hrs
            ];

            // Check if trip_employee exists
            $tripEmployee = DB::table('trip_employee')->where($tdata)->first();
            if ($tripEmployee) {
                $id = $tripEmployee->id;
                if ($data["status"] == 1 && $stsreason == 1) {
                    $empdt = [
                        'status' => '1',
                        'absent_reason' => 'Closed',
                        'pd_status' => 'Closed',
                        'updated_on' => $curdt
                    ];
                    DB::table('trip_employee')->where('id', $id)->update($empdt);
                }
                $response = ["status" => "1", "data" => ["message" => "Updated Status!"]];
            } else {
                if ($data['status'] == '1') {
                    $chkltlng = $this->checkLatLng($data["employee_id"]);
                    if ($chkltlng == "true") {
                        $lat = $latitude;
                        $lng = $longitude;
                        $svpk = $this->savePickup($data["employee_id"], $lat, $lng);
                    }
                }
                if (($stsreason == 1 && $data["status"] == 1) || ($data["status"] == 0)) {
                    if ($stsreason == 1) {
                        $data['absent_reason'] = 'Closed';
                    } else if ($stsreason == 0) {
                        $data['absent_reason'] = 'Cancelled';
                    }
                    $data["stop_id"] = $stop_id;
                    $res = DB::table('trip_employee')->insert($data);
                    $id = DB::getPdo()->lastInsertId();
                    $smsdata = [];
                    if ($id > 0) {
                        if ($data["status"] == 1) {
                            $result = [];
                            // If sendTripAlert is implemented, call it here
                            // $result = $this->sendTripAlert($data["trip_id"], $curtz);
                            // if (!empty($result)) {
                            //     if (isset($result['sms_data'])) {
                            //         $smsdata = $result['sms_data'];
                            //     }
                            //     if (isset($result['pickup_data'])) {
                            //         $pickdata = $result['pickup_data'];
                            //     }
                            // }
                        }
                        $response = ["status" => "1", "data" => ["message" => "Updated Status!!"]];
                    } else {
                        $response = ["status" => "0", "data" => ["message" => "Failed to Set Shipment Status"]];
                    }
                } else {
                    $response = ["status" => "1", "data" => ["message" => "Updated Status!!!"]];
                }
            }
        }

        return response()->json($response);
    }

    public function getExpenseTypes(Request $request)
    {
        if ($request->isMethod('post')) {
            $actName = strtolower($request->input('name', ''));
            $accountMaster = new AccountMaster();
            $rows = $accountMaster->where('status', '1')
                ->whereRaw('lower(act_name) LIKE ?', ["%{$actName}%"])
                ->get(['id', 'act_name']);

            $response = [];
            foreach ($rows as $res) {
                $response[] = [
                    'id' => $res->id,
                    'name' => $res->act_name,
                ];
            }

            return response()->json(['status' => 1, 'data' => $response], 200);
        } else {
            return response()->json(['status' => 0, 'data' => 'its not a get request'], 200);
        }
    }

    public function getDriverExpenseData(Request $request)
    {
        $response = [];

        // Accept POST data similar to original behavior
        if ($request->isMethod('post')) {
            $driver_id = $request->input('driver_id', '');
            $vehicle_id = $request->input('vehicle_id', '');

            if (!empty($vehicle_id)) {
                $transactions = new Transaction();
                $getExpenses = $transactions
                    ->where('status', '1')
                    ->where('driver_id', $driver_id)
                    ->where('vehicle_id', $vehicle_id)
                    ->get();

                foreach ($getExpenses as $res) {
                    // approval/status handling
                    $sts = $res->approval;
                    if (isset($res->status) && $res->status === 'Inactive') {
                        $sts = "Rejected";
                    }

                    // document path
                    $doc = "";
                    if (!empty($res->bill_doc)) {
                        $doc = url('tripexpenses/' . $res->bill_doc);
                    }

                    // expense type name lookup
                    $expType = $res->act_id;
                    $accountMaster = new AccountMaster();
                    $expenseType = $accountMaster->where('id', $res->act_id)->first();
                    if ($expenseType && !empty($expenseType->act_name)) {
                        $expType = $expenseType->act_name;
                    }

                    // vehicle number lookup
                    $vehNumber = $res->vehicle_id;
                    $trucksData = new TrucksData();
                    $vehqry = $trucksData->where('id', $res->vehicle_id)->select('register_number')->first();
                    if ($vehqry && !empty($vehqry->register_number)) {
                        $vehNumber = $vehqry->register_number;
                    }

                    // shipment id / order id lookup when trip_id present
                    $shipmentId = "";
                    if (isset($res->trip_id) && $res->trip_id > 0) {
                        $order = new Order();
                        $getShipmentId = $order->where('trip_id', $res->trip_id)
                            ->where('shipmentid', '<>', '')
                            ->select('shipmentid')
                            ->first();

                        if ($getShipmentId) {
                            $shipmentId = $getShipmentId->shipmentid;
                        } else {
                            // fallback: try to get order_id for the trip (original logic used else shipmentId = getShipmentId['order_id'])
                            $getShipmentIdFallback = $order->where('trip_id', $res->trip_id)
                                ->select('order_id')
                                ->first();
                            if ($getShipmentIdFallback) {
                                $shipmentId = $getShipmentIdFallback->order_id;
                            }
                        }
                    }

                    $response[] = [
                        "expense_id" => $res->id,
                        "vehicle_number" => $vehNumber,
                        "vehicle_id" => $res->vehicle_id,
                        "expense_type" => $expType,
                        "trip_id" => $res->trip_id,
                        "amount" => $res->amount,
                        "activity_date" => $res->txn_date,
                        "receipt_no" => $res->receipt_no,
                        "status" => $sts,
                        "shipmentid" => $shipmentId,
                        "bills" => [
                            'document_type' => 'Expense',
                            'document' => $doc
                        ]
                    ];
                }
            }
        }

        return response()->json(["status" => 1, "data" => $response], 200);
    }

    public function getVehicleInspectionsDataByTripId(Request $request)
    {
        $shiftId = $request->input('shift_id', null);

        if (empty($shiftId)) {
            return response()->json(["status" => 0, "data" => "Insufficient Input Data"]);
        }

        // Fetch vehicle inspection for the given trip_id
        $vehicleInspection = VehicleInspection::where('trip_id', $shiftId)
            ->select(
                'id',
                'inspection_time',
                'vehicle_no',
                'odometer_reading',
                'trailer_no',
                'remarks',
                'satisfactory_condition',
                'driver_name',
                'defects_safedrive',
                'mechanic_name',
                'driver_date',
                'driver_id',
                'vehicle_id',
                'trip_id'
            )->first();

        if (! $vehicleInspection) {
            return response()->json(["status" => 0, "data" => "No data for that trip id " . $shiftId]);
        }

        $result = (array) $vehicleInspection;
        $id = $result['id'];

        // Fetch trip inspections data joined with inspections master and truck types
        $tripInspectionQuery = DB::table('trip_inspections_data as ti')
            ->leftJoin('inspections_master as im', function ($join) {
                $join->on('ti.inspection_id', '=', 'im.id')
                     ->where('ti.status', '=', 1)
                     ->where('im.status', '=', 1);
            })
            ->leftJoin('trucktypes as tt', 'im.truck_type', '=', 'tt.id')
            ->where('ti.trip_inspection_id', $id)
            ->select('ti.trip_inspection_id', 'ti.inspection_id', 'im.inspection_name', 'im.truck_type')
            ->get();

        $data = $tripInspectionQuery->toArray();
        $trucktype = null;
        if (count($data) > 0) {
            $trucktype = $data[0]->truck_type ?? null;
        }

        $truckInspections = [];
        $trailerInspections = [];

        if (count($data) > 0) {
            foreach ($data as $res) {
                $inspectionId = $res->inspection_id ?? null;
                $inspectionName = $res->inspection_name ?? null;
                $tt = $trucktype ? strtolower($trucktype) : '';

                if ($tt !== "trailer" && $tt !== "container") {
                    $truckInspections[] = ['id' => $inspectionId, 'name' => $inspectionName];
                } else {
                    $trailerInspections[] = ['id' => $inspectionId, 'name' => $inspectionName];
                }
            }
        }

        $result['truckinspections'] = $truckInspections;
        $result['trailerinspectins'] = $trailerInspections;

        $response = ["status" => 1, "data" => $result];

        // If printable helper exists on this controller, use it to transform output
        if (method_exists($this, 'printable')) {
            return response()->json($this->printable($response));
        }

        return response()->json($response);
    }

    public function updateDriverExpenses(Request $request)
    {
        if (!$request->isMethod('post')) {
            return response()->json(['status' => 0, 'data' => 'It is not a get request'], 200);
        }

        $txn_id = (int) $request->input('id', 0);
        $driver_id = (int) $request->input('driver_id', 0);
        $vehicle_id = (int) $request->input('vehicle_id', 0);
        $expenseName = $request->input('expense_type', '0');
        $amount = $request->input('amount', 0.00);
        $receipt_no = $request->input('receipt_no', '');
        $trip_shipmentid = $request->input('trip_id', 0);
        $txndate = $request->input('activity_date', date('Y-m-d H:i:s'));

        if ($txn_id && $driver_id && $vehicle_id) {
            if (!empty($txndate)) {
                $txndate = date('Y-m-d H:i:s', strtotime($txndate));
            } else {
                $txndate = date('Y-m-d H:i:s');
            }

            if ($expenseName === 0 || $expenseName === "" || $expenseName === '0') {
                $expenseName = "Duplicate Charges";
            }

            $tripId = DB::table('orders')->where('shipmentid', $trip_shipmentid)->value('trip_id');
            $tripId = $tripId ? $tripId : 0;

            $setData = [
                'amount' => $amount,
                'txn_id' => $receipt_no,
                'txn_date' => $txndate,
                'receipt_no' => $receipt_no,
                'trip_id' => $tripId,
                'payment_method' => 'Manual',
                'approval' => 'No',
                'paid' => 'Yes',
                'paid_by' => 'Driver',
                'quantity' => 1,
                'description' => $receipt_no,
                'payable_to' => 'Carrier',
                'status' => '1'
            ];

            $expenseTypeId = AccountMaster::where('act_name', $expenseName)
                ->where('status', '1')
                ->value('id');

            if ($expenseTypeId) {
                $setData['act_id'] = $expenseTypeId;
            }

            if ($request->hasFile('file')) {
                $file = $request->file('file');
                if ($file->isValid()) {
                    $ext = strtolower($file->getClientOriginalExtension());
                    $allowed = ['jpg', 'jpeg', 'gif', 'png', 'pdf', 'svg'];
                    if (in_array($ext, $allowed)) {
                        $filename = uniqid('exp_') . '.' . $ext;
                        $destination = public_path('tripexpenses');
                        // ensure directory exists
                        if (!is_dir($destination)) {
                            @mkdir($destination, 0755, true);
                        }
                        $file->move($destination, $filename);
                        $setData['bill_doc'] = $filename;
                    }
                }
            }

            $transaction = Transaction::find($txn_id);
            if ($transaction) {
                $transaction->update($setData);
            } else {
                return response()->json(['status' => 0, 'data' => 'Transaction not found'], 200);
            }

            return response()->json(['status' => 1, 'data' => ['success']], 200);
        }

        return response()->json(['status' => 0, 'data' => 'Insufficient Input Data'], 200);
    }

    public function addDriverExpenses(Request $request)
    {
        if (! $request->isMethod('post')) {
            return response()->json(['status' => 0, 'data' => 'It is not a get request'], 200);
        }

        $driver_id = (int) $request->input('driver_id', 0);
        $vehicle_id = (int) $request->input('vehicle_id', 0);
        $expenseName = $request->input('expense_type', '0');
        $amount = $request->input('amount', 0.00);
        $receipt_no = $request->input('receipt_no', '');
        $trip_shipmentid = $request->input('trip_id', 1);
        $txndate = $request->input('activity_date', date('Y-m-d H:i:s'));

        if (! $driver_id || ! $vehicle_id) {
            return response()->json(['status' => 0, 'data' => 'Insufficient Input Data'], 200);
        }

        if ($txndate != '') {
            $txndate = date('Y-m-d H:i:s', strtotime($txndate));
        }

        if ($expenseName == 0 || $expenseName === '') {
            $expenseName = 'Other Charges';
        }

        $expense_type = 1;
        $accountMaster = new AccountMaster();
        $expenseTypeId = $accountMaster->where('act_name', $expenseName)
            ->where('status', '1')
            ->value('id');

        if (! empty($expenseTypeId)) {
            $expense_type = $expenseTypeId;
        }

        $bill_doc = '';
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            if ($file->isValid()) {
                $ext = strtolower($file->getClientOriginalExtension());
                $allowed = ['jpg', 'gif', 'png', 'pdf', 'svg', 'jpeg'];
                if (in_array($ext, $allowed)) {
                    $filename = uniqid('exp_') . '.' . $ext;
                    $destination = public_path('tripexpenses');
                    if (! is_dir($destination)) {
                        @mkdir($destination, 0755, true);
                    }
                    $file->move($destination, $filename);
                    $bill_doc = $filename;
                }
            }
        }

        $Order = new Order();
        $tripId = $Order->where('shipmentid', $trip_shipmentid)->value('trip_id');
        $tripId = $tripId ? $tripId : 0;

        $currentDate = date('Y-m-d H:i:s');

        $insData = [
            'act_id' => $expense_type,
            'amount' => $amount,
            'txn_id' => $receipt_no,
            'vehicle_id' => $vehicle_id,
            'driver_id' => $driver_id,
            'txn_date' => $txndate,
            'receipt_no' => $receipt_no,
            'trip_id' => $tripId,
            'payment_method' => 'Manual',
            'approval' => 'No',
            'paid' => 'Yes',
            'paid_by' => 'Driver',
            'quantity' => 1,
            'description' => $receipt_no,
            'bill_doc' => $bill_doc,
            'payable_to' => 'Carrier',
            'status' => '1',
            'created_at' => $currentDate
        ];

        $transaction = new Transaction();
        $transaction->create($insData);

        return response()->json(['status' => 1, 'data' => ['success']], 200);
    }

    public function getInspectionTypes()
    {
        $sql = "SELECT m.id, m.inspection_name, t.trucktype, tic.category_name FROM inspections_master AS m 
        LEFT JOIN trucktypes AS t ON m.truck_type = t.id 
        LEFT JOIN inspection_category AS tic ON m.category_id = tic.id
        WHERE m.status = 1 AND t.status='1' AND tic.status=1";
        $results = DB::select($sql);

        $response = [];
        foreach ($results as $res) {
            $response[] = [
                "id" => $res->id,
                "name" => $res->inspection_name,
                "type" => $res->trucktype,
                "category_name" => $res->category_name
            ];
        }

        return response()->json(["status" => 1, "data" => $response], 200);
    }

    public function setTripVehicleInspections(Request $request)
    {
        if (! $request->isMethod('post')) {
            return response()->json(['status' => 0, 'data' => 'It is not a get request'], 200);
        }

        $inspectionTime = $request->input('inspection_time', date("Y-m-d H:i:s"));
        $vehicleNo = $request->input('vehicle_no', null);
        $odometerReading = $request->input('odometer_reading', 0);
        $truckInspections = $request->input('truck_inspections', 0);
        $trailerNo = $request->input('trailer_no', null);
        $trailerInspections = $request->input('trailer_inspections', null);
        $remarks = $request->input('remarks', '');
        $satisfactoryCondition = $request->input('satisfactory_condition', null);
        $driverName = $request->input('driver_name', '');
        $defectsSafedrive = $request->input('defects_safedrive', null);
        $mechanicName = $request->input('mechanic_name', '');
        $mechanicDate = $request->input('mechanic_date', '');
        $driverDate = $request->input('driver_date', '');
        $driverId = $request->input('driver_id', null);
        $vehicleId = $request->input('vehicle_id', null);
        $shiftId = $request->input('shift_id', null);

        if (empty($shiftId)) {
            return response()->json(['status' => 0, 'data' => 'It is not a get request'], 200);
        }

        $updateResult = false;
        $insertResult = false;

        // Check existing record
        $existing = VehicleInspection::where('driver_id', $driverId)
            ->where('vehicle_id', $vehicleId)
            ->where('trip_id', $shiftId)
            ->first();

        $id = $existing ? $existing->id : null;

        if ($truckInspections != 0) {
            $tripInspections = $truckInspections;
        } elseif ($trailerInspections != 0) {
            $tripInspections = $trailerInspections;
        } else {
            $tripInspections = [];
        }

        // Normalize to array for insertion loop
        if (!is_array($tripInspections)) {
            $tripInspections = $tripInspections !== null ? (array) $tripInspections : [];
        }

        if (!empty($id)) {
            $updateData = [
                "inspection_time" => $inspectionTime,
                "vehicle_no" => $vehicleNo,
                "odometer_reading" => $odometerReading,
                "trailer_no" => $trailerNo,
                "remarks" => $remarks,
                "satisfactory_condition" => $satisfactoryCondition,
                "driver_name" => $driverName,
                "defects_safedrive" => $defectsSafedrive,
                "mechanic_name" => $mechanicName,
                "mechanic_date" => $mechanicDate,
                "driver_date" => $driverDate,
                "driver_id" => $driverId,
                "vehicle_id" => $vehicleId,
                "trip_id" => $shiftId
            ];

            $updateResult = VehicleInspection::where('id', $id)->update($updateData);

            if ($updateResult) {
                TripInspectionData::where('trip_inspection_id', $id)->update(['status' => '0']);

                foreach ($tripInspections as $inspectionId) {
                    TripInspectionData::create([
                        "trip_inspection_id" => $id,
                        "inspection_id" => $inspectionId,
                        "status" => '1',
                        "created_at" => date("Y-m-d H:i:s")
                    ]);
                }
            }
        } else {
            $data = [
                "inspection_time" => $inspectionTime,
                "vehicle_no" => $vehicleNo,
                "odometer_reading" => $odometerReading,
                "trailer_no" => $trailerNo,
                "remarks" => $remarks,
                "satisfactory_condition" => $satisfactoryCondition,
                "driver_name" => $driverName,
                "defects_safedrive" => $defectsSafedrive,
                "mechanic_name" => $mechanicName,
                "mechanic_date" => $mechanicDate,
                "driver_date" => $driverDate,
                "driver_id" => $driverId,
                "vehicle_id" => $vehicleId,
                "trip_id" => $shiftId
            ];

            $vehicleInspection = new VehicleInspection();
            $lastInsertId = $vehicleInspection->create($data)->id;

            if ($lastInsertId > 0) {
                foreach ($tripInspections as $inspectionId) {
                    $tripInspectionData = new TripInspectionData();
                    $tripInspectionData->trip_inspection_id = $lastInsertId;
                    $tripInspectionData->inspection_id = $inspectionId;
                    $tripInspectionData->status = 1;
                    $tripInspectionData->created_at = date("Y-m-d H:i:s");
                    $tripInspectionData->save();
                }
            }
        }

        if ($updateResult || $lastInsertId > 0) {
            return response()->json(["status" => 1, "data" => ["success"]], 200);
        }

        return response()->json(["status" => 0, "data" => "Insufficient Input Data"], 200);
    }

    public function driverCheckIn(Request $request)
    {
        if (! $request->isMethod('post')) {
            return response()->json(['status' => 0, 'data' => 'It is not a get request'], 200);
        }

        $driver_id = $request->input('driver_id', null);
        $check_type = $request->input('check_type', 0);
        $latitude = $request->input('latitude', 0);
        $longitude = $request->input('longitude', 0);
        $checkInTime = $request->input('checkin_time', date('Y-m-d H:i:s'));
        $checkOutTime = $request->input('check_out_time', date('Y-m-d H:i:s'));
        $image_orientation = intval($request->input('image_orientation', 0));
        $fileName = '';

        $result = false;
        $update = false;
        $response = [];

        // Handle uploaded file similarly to original logic
        if ($request->hasFile('file_name')) {
            $file = $request->file('file_name');
            if ($file->isValid()) {
                $file_ext = strtolower($file->getClientOriginalExtension());
                $expensions = ['jpeg', 'jpg', 'png', 'gif', 'pdf'];

                if (in_array($file_ext, $expensions)) {
                    // PDF handling: move file as-is
                    if ($file_ext === 'pdf') {
                        $fileName = uniqid('file_') . '.' . $file_ext;
                        $file->move(public_path('assets/poduploads'), $fileName);
                    } else {
                        // Image handling: create file path, process rotation/quality as original
                        $fileName = date('dmyhis') . $file->getClientOriginalName();
                        $filePath = public_path('assets/poduploads/' . $fileName);
                        $tempPath = $file->getRealPath();

                        $imgsize = @getimagesize($tempPath);
                        $quality = 100;
                        $imgtype = $file_ext;

                        if ($file_ext === 'png') {
                            $top = @imagecreatefrompng($tempPath);
                            $imgtype = 'png';
                        } else {
                            // treat jpg/jpeg via imagecreatefromjpeg
                            $top = @imagecreatefromjpeg($tempPath);
                            if ($file_ext === 'jpg' || $file_ext === 'jpeg') {
                                $imgtype = 'jpeg';
                            } else {
                                $imgtype = $file_ext;
                            }
                        }

                        if (isset($top) && $top !== false) {
                            // Rotate if required (preserve original orientation logic)
                            if ($image_orientation > 1 && in_array($imgtype, ['png', 'jpeg'])) {
                                switch ($image_orientation) {
                                    case 3:
                                    case 4:
                                        $angle = 180;
                                        break;
                                    case 5:
                                    case 6:
                                        $angle = -90;
                                        break;
                                    case 7:
                                    case 8:
                                        $angle = 90;
                                        break;
                                    default:
                                        $angle = 0;
                                        break;
                                }

                                if (!empty($angle)) {
                                    // allocate fully transparent background for PNG rotations like original
                                    $background_color = imageColorAllocateAlpha($top, 0, 0, 0, 127);
                                    $ignore_transparent = false;
                                    $rotated = @imagerotate($top, $angle, $background_color, $ignore_transparent);
                                    if ($rotated !== false) {
                                        imagedestroy($top);
                                        $top = $rotated;
                                    }
                                    imagealphablending($top, false);
                                    imagesavealpha($top, true);
                                }
                            }

                            // Save as JPEG (matching original imagejpeg usage)
                            @imagejpeg($top, $filePath, $quality);
                            @imagedestroy($top);
                        } else {
                            // fallback: move without processing
                            $file->move(public_path('assets/poduploads'), $fileName);
                        }
                    }
                }
            }
        }

        if (! empty($driver_id) && $check_type === 'checkin') {
            $data = [
                'driver_id' => $driver_id,
                'checkin_latitude' => $latitude,
                'checkin_longitude' => $longitude,
                'status' => 2,
                'checkin_time' => $checkInTime,
                'clockin_odometer' => $fileName
            ];
            $result = DB::table('driver_checkin_checkout')->insert($data);
        } elseif (! empty($driver_id) && $check_type === 'checkout') {
            $row = DB::selectOne(
                "SELECT id FROM driver_checkin_checkout WHERE checkin_time IS NOT NULL AND driver_id = ? AND check_out_time IS NULL AND clockin_odometer IS NOT NULL ORDER BY id DESC LIMIT 1",
                [$driver_id]
            );

            if ($row && isset($row->id)) {
                $id = $row->id;
                $updateData = [
                    'checkout_latitude' => $latitude,
                    'checkout_longitude' => $longitude,
                    'check_out_time' => $checkOutTime,
                    'clockout_odometer' => $fileName,
                    'status' => 1
                ];
                $update = DB::table('driver_checkin_checkout')->where('id', $id)->update($updateData);
            }
        }

        if ($result === true) {
            return response()->json(['status' => 2, 'data' => ['success']], 200);
        }

        if ($update === true) {
            return response()->json(['status' => 1, 'data' => $response], 200);
        }

        // If neither insert nor update happened, return a generic response (keeps behaviour explicit)
        return response()->json(['status' => 0, 'data' => 'No action performed'], 200);
    }

    public function getDriverDailyLogs(Request $request)
    {
        $driverId = $request->input('driver_id', null);

        if ($driverId === null) {
            return response()->json(["status" => 0, "data" => "no data for this driver"]);
        }

        // Fetch daily checkin/checkout rows
        $rows = DB::table('driver_checkin_checkout')
            ->select(
                'checkin_time',
                'check_out_time',
                'status',
                'checkin_latitude',
                'checkin_longitude',
                'checkout_latitude',
                'checkout_longitude'
            )
            ->where('driver_id', $driverId)
            ->orderByDesc('id')
            ->get();

        $response = [];
        $checkInDate = null;

        if ($rows->isNotEmpty()) {
            foreach ($rows as $driverData) {
                $checkInTime = $driverData->checkin_time;
                $checkOutTime = $driverData->check_out_time;
                $status = $driverData->status;
                $checkinLatitude = $driverData->checkin_latitude;
                $checkinLongitude = $driverData->checkin_longitude;
                $checkOutLatitude = $driverData->checkout_latitude;
                $checkOutLongitude = $driverData->checkout_longitude;

                // Safely parse datetimes (preserve original formatting logic)
                $checkInDate = null;
                $checkInTimeOnly = null;
                $checkOutTimeOnly = null;
                $diffFormatted = "";

                try {
                    if (!empty($checkInTime)) {
                        $dtIn = new \DateTime($checkInTime);
                        $checkInDate = $dtIn->format('Y-m-d');
                        $checkInTimeOnly = $dtIn->format('H:i:s');
                    }
                    if (!empty($checkOutTime)) {
                        $dtOut = new \DateTime($checkOutTime);
                        $checkOutTimeOnly = $dtOut->format('H:i:s');
                    }

                    if (!empty($checkInTime) && !empty($checkOutTime)) {
                        $interval = $dtIn->diff($dtOut);
                        $hours = $interval->h;
                        $minutes = $interval->i;
                        $diffFormatted = $hours . " hrs " . $minutes . " minutes ";
                    }
                } catch (\Exception $e) {
                    // If parsing fails, keep fields empty to preserve behavior
                }

                // Map status codes to text as original
                if ($status == 1) {
                    $statusText = 'Approved';
                } elseif ($status == 2) {
                    $statusText = 'Pending';
                } elseif ($status == 3) {
                    $statusText = 'Discrepancy';
                } else {
                    $statusText = $status;
                }

                $response[] = [
                    'date' => $checkInDate,
                    'checkin_time' => $checkInTimeOnly,
                    'check_out_time' => $checkOutTimeOnly,
                    'total_work' => $diffFormatted,
                    'status' => $statusText,
                    'checkin_latitude' => $checkinLatitude,
                    'checkin_longitude' => $checkinLongitude,
                    'checkout_latitude' => $checkOutLatitude,
                    'checkout_longitude' => $checkOutLongitude
                ];
            }
        }

        // Try to find a shipment id for the same date using stop status records
        $shipmentId = null;
        if (!empty($checkInDate)) {
            $shiftRow = DB::table('stop_status')
                ->select('shipment_id')
                ->where('driver_id', $driverId)
                ->whereNotIn('status_code', ['0212'])
                ->whereDate('createdon', $checkInDate)
                ->orderByDesc('id')
                ->first();

            if ($shiftRow && isset($shiftRow->shipment_id)) {
                $shiftId = $shiftRow->shipment_id;
                $orderRow = DB::table('orders')->select('shipmentid')->where('shift_id', $shiftId)->first();
                if ($orderRow && isset($orderRow->shipmentid)) {
                    $shipmentId = $orderRow->shipmentid;
                    foreach ($response as &$entry) {
                        $entry['shipmentid'] = $shipmentId;
                    }
                    unset($entry);
                }
            }
        }

        return response()->json(["status" => 1, "data" => $response]);
    }

    public function getAcceptTrips(Request $request)
    {
        $response = ["status" => 0, "data" => "no data for the drivers trips"];

        $driverid = $request->input('driver_id');

        $accepttrips = DB::table('trips as tt')
            ->join('shipment as ts', 'tt.shift_id', '=', 'ts.id')
            ->select('tt.id', DB::raw('ts.shipmentid as name'))
            ->where('tt.transit_status', 0)
            ->where('tt.driver_id', $driverid)
            ->get();

        if ($accepttrips->isNotEmpty()) {
            $res = $accepttrips->map(function ($item) {
                return (array) $item;
            })->toArray();
            $response = ["status" => 1, "data" => $res];
        }

        return response()->json($response);
    }

    public function getCheckInOutStatus(Request $request)
    {
        $response = ["status" => 0, "data" => "No data for the driver's trips"];
        $driverid = $request->input('driver_id', null);

        // Use Eloquent model instead of raw SQL
        $checkDriverLogs = null;
        if (!empty($driverid)) {
            $checkDriverLogs = DriverCheckinCheckout::where('driver_id', $driverid)
            ->whereNotNull('checkin_time')
            ->whereNull('check_out_time')
            ->where(function ($q) {
                $q->whereDate('checkin_time', now()->toDateString())
                  ->orWhereDate('checkin_time', now()->subDay()->toDateString());
            })
            ->orderByDesc('id')
            ->first();
        }

        if (!empty($checkDriverLogs)) {
            $response['status'] = 2;
            $response['data'] = [];
        }

        return response()->json($response);
    }

    public function checkOdometerReadings(Request $request)
    {
        $response = ["status" => 0, "data" => "No data for the driver's trips"];

        if (! $request->isMethod('post')) {
            $response = [
                'status' => 'error',
                'message' => 'Not allowed to use ' . $request->method() . ' in here.'
            ];
            return response()->json($response, 405);
        }

        $post = $request->all();

        if (empty($post)) {
            $response = [
                'status' => 'error',
                'message' => 'POST data should not be empty.'
            ];
            return response()->json($response, 400);
        }

        $tripId = $post['trip_id'] ?? null;
        $vehicleId = $post['vehicle_id'] ?? null;
        $startReading = $post['start_reading'] ?? null;
        $endReading = $post['end_reading'] ?? null;
        $readings = $post['readings'] ?? null;
        $startReadingImg = $post['startreading_img'] ?? null;
        $endReadingImg = $post['endreading_img'] ?? null;
        $latitude = $post['latitude'] ?? null;
        $longitude = $post['longitude'] ?? null;
        $fileName = '';

        if ($request->hasFile('file_name_odo') && $request->file('file_name_odo')->isValid()) {
            $file = $request->file('file_name_odo');
            $ext = strtolower($file->getClientOriginalExtension());
            $allowed = ['jpg', 'jpeg', 'gif', 'png', 'pdf'];

            if (in_array($ext, $allowed)) {
                $fileName = uniqid('odo_') . '.' . $ext;
                $destination = public_path('assets/poduploads');
                if (! is_dir($destination)) {
                    @mkdir($destination, 0755, true);
                }
                $file->move($destination, $fileName);
            }
        }

        if ($tripId === '') {
            $tripId = null;
        }

        $tripOdometerData = [];

        if ($vehicleId !== null && $vehicleId !== "" && $vehicleId !== 'null') {
            $vehicleOdometer = new VehicleOdometer();
            $results = $vehicleOdometer->where('vehicle_id', $vehicleId)
                ->where('status', 1)
                ->whereNotNull('start_reading')
                ->whereNull('end_reading')
                ->get();

            if (count($results) > 0) {
                $tripOdometerData = (array) $results[0];
            }
        }

        if (! empty($tripOdometerData)) {
            $updateData = [
                'end_reading'     => $readings,
                'endreading_img'  => $fileName,
                'latitude'        => $latitude !== null ? $latitude : $tripOdometerData['latitude'],
                'longitude'       => $longitude !== null ? $longitude : $tripOdometerData['longitude'],
                'updated_at'      => now()
            ];

            $set = ['vehicle_id' => $vehicleId, 'status' => 1];

            $updateResult = VehicleOdometer::where($set)->update($updateData);

            if ($updateResult) {
                $response = ["status" => 2, "data" => "Odometer reading Updated Successfully"];
            } else {
                $response = ["status" => 'error', "data" => "Failed to update odometer reading"];
            }
        } else {
            $insertData = [
                'trip_id'          => $tripId,
                'vehicle_id'       => $vehicleId,
                'start_reading'    => $readings,
                'startreading_img' => $fileName,
                'latitude'         => $latitude,
                'longitude'        => $longitude,
                'created_at'       => now(),
                'updated_at'       => now()
            ];

            $result = VehicleOdometer::insert($insertData);

            if ($result) {
                $response = ["status" => 1, "data" => "Odometer reading Inserted Successfully"];
            } else {
                $response = ["status" => 'error', "data" => "Failed to insert odometer reading"];
            }
        }

        return response()->json($response);
    }

    public function getCompanyCode(Request $request)
    {
        $response = ["status" => 0, "data" => "Not Found"];

        if ($request->isMethod('post') && count($request->post()) > 0) {
            $driver_id = $request->input('driver_id', null);

            if (!empty($driver_id)) {
                $row = TruckDriver::where('id', $driver_id)
                    ->where('status', '1')
                    ->first();

                if ($row) {
                    $response = ["status" => 1, "data" => $row->company_code];
                }
            }
        }

        return response()->json($response, 200);
    }

    public function getUserDetailsById(int $userId): array
    {
        try {
            $response = [];
            
            // Get user data - equivalent to $ci->SxUsers->where(['id' => $user_id])->row_array()
            $userData = DB::table('sx_users')
                ->where('id', $userId)
                ->first();
            
            if (!$userData) {
                Log::warning('User not found', ['user_id' => $userId]);
                return [];
            }

            $response['org_id'] = $orgId = $userData->default_org_id;

            // Get user organization data - equivalent to $ci->SxUserOrganizations->where(['user_id' => $data['id'], 'org_id' => $data['default_org_id']])->row_array()
            $userOrganizations = DB::table('sx_user_organizations')
                ->where('user_id', $userData->id)
                ->where('org_id', $userData->default_org_id)
                ->first();

            if (!$userOrganizations) {
                Log::warning('User organization not found', [
                    'user_id' => $userId,
                    'org_id' => $orgId
                ]);
                return $response;
            }

            // Decode roles from JSON - equivalent to json_decode($user_organizations['roles'])
            $defaultOrgPrivilegeIds = json_decode($userOrganizations->roles, true) ?? [];

            // Get structure data - equivalent to $ci->SxStructureMaster->where(['org_id' => $org_id, "id" => $user_organizations['structure_id']])->select('id')->findAll()
            $structures = DB::table('sx_structure_master')
                ->where('org_id', $orgId)
                ->where('id', $userOrganizations->structure_id)
                ->select('id')
                ->get();

            if ($structures->isEmpty()) {
                Log::warning('Structure not found', [
                    'org_id' => $orgId,
                    'structure_id' => $userOrganizations->structure_id
                ]);
                return $response;
            }

            $response['be_value'] = $userOrganizations->entity_value_id;

            // Get structure sequences with business entities - equivalent to the complex join query
            $sequences = DB::table('sx_structure_sequence')
                ->join('sx_business_entity as sbe', 'sbe.id', '=', 'sx_structure_sequence.parent_business_entity')
                ->join('sx_business_entity as sbee', 'sbee.id', '=', 'sx_structure_sequence.child_business_entity')
                ->where('sx_structure_sequence.structure_id', $structures[0]->id)
                ->where('sx_structure_sequence.org_id', $orgId)
                ->select(
                    'sx_structure_sequence.*',
                    'sx_structure_sequence.id as id',
                    'sbe.id as parent_entity_id',
                    'sbe.entity_name as parent_entity_name',
                    'sbee.entity_name as child_entity_name',
                    'sbee.id as child_entity_id'
                )
                ->where('sx_structure_sequence.status', 1) // getActiveRecords equivalent
                ->get();

            // Get privileges - equivalent to $ci->SxPrevilleges->whereIn("id", $default_org_privilege_ids)->getActiveRecords()
            $privileges = DB::table('sx_previlleges')
                ->whereIn('id', $defaultOrgPrivilegeIds)
                ->where('status', 1) // getActiveRecords equivalent
                ->get();

            // Extract business entities from privileges
            $businessEntities = [];
            foreach ($privileges as $privilege) {
                $businessEntities[] = $privilege->business_entity;
            }

            // Build sequence array - exactly like original
            $sequenceArr = [];
            foreach ($sequences as $sequence) {
                $sequenceArr[] = $sequence->parent_business_entity;
                $sequenceArr[] = $sequence->child_business_entity;
            }

            // Find entity IDs based on business entities and sequences - exactly like original
            $entityIds = [];
            foreach ($businessEntities as $businessEntity) {
                $index = array_search($businessEntity, $sequenceArr);
                if ($index !== false) {
                    for ($i = $index; $i < count($sequenceArr); $i++) {
                        $entityIds[] = $sequenceArr[$i];
                    }
                }
            }

            // Get entity values - equivalent to $ci->SxBusinessEntityValue->whereIn('entity_id ', $entity_ids)->select('id')->findAll()
            $entityValues = DB::table('sx_business_entity_value')
                ->whereIn('entity_id', $entityIds)
                ->select('id')
                ->get();

            // Build entities array - exactly like original
            $entities = [];
            foreach ($entityValues as $entity) {
                $entities[] = $entity->id;
            }

            $response['entities'] = $entities;

            Log::info('User details retrieved successfully', [
                'user_id' => $userId,
                'org_id' => $orgId,
                'entities_count' => count($entities)
            ]);

            return $response;

        } catch (\Exception $e) {
            Log::error('Error retrieving user details', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    public function parties(Request $request)
    {
        $response = ["status" => 0, "data" => "Not Found"];

        $user_id = (int) $request->input('user_id', 0);
        $cidname = (string) $request->input('name', '');

        if ($user_id > 0 && $cidname !== '') {
            $user_details = $this->getUserDetailsById($user_id);

            $orgId = $user_details['org_id'] ?? null;
            $entities = $user_details['entities'] ?? [];

            if ($orgId !== null && !empty($entities)) {
                $cidLower = strtolower($cidname);

                $query = DB::table('sx_party_members')
                    ->select('id', 'name', 'mobile')
                    ->where('name', '!=', 'Carrier')
                    ->where('org_id', $orgId)
                    ->where('status', '1')
                    ->whereIn('entity', $entities)
                    ->where(function ($q) use ($cidLower, $cidname) {
                        $q->whereRaw('LOWER(name) LIKE ?', ["%{$cidLower}%"])
                          ->orWhere('mobile', 'like', "%{$cidname}%");
                    });

                $rows = $query->get();

                if ($rows->isNotEmpty()) {
                    $data = [];
                    foreach ($rows as $res) {
                        $data[] = [
                            'id' => $res->id,
                            'name' => $res->name,
                            'cid' => $res->mobile
                        ];
                    }
                    $response = ["status" => 1, "data" => $data];
                }
            }
        }

        $output = method_exists($this, 'printable') ? $this->printable($response) : $response;
        return response()->json($output, 200);
    }

    public function driverCollection(Request $request)
    {
        $response = ["status" => 0, "data" => "Not Found"];

        $driver_id = $request->input('driver_id', null);
        $user_id = $request->input('user_id', null);
        $imei = $request->input('imei', '');

        $user_details = [];
        if (!empty($user_id)) {
            $user_details = $this->getUserDetailsById((int)$user_id);
        }

        $payload = $request->input('data', null);
        if ($payload) {
            $post = is_string($payload) ? json_decode($payload) : (object)$payload;
            $data = (array)$post;

            $cust_id = $data['customer_id'] ?? '';
            $pickup_partyid = $data['pickup_pary_id'] ?? ($cust_id);
            $drop_partyid = $data['delivery_pary_id'] ?? ($cust_id);

            if ($driver_id > 0 && $user_id > 0 && $cust_id != "") {
                $curtz = $request->input('timezone', 'America/New_York');

                $uoms = $data['uoms'] ?? [];
                $references = $data['references'] ?? [];

                // Pickup
                $pickup = $data['pickup'] ?? null;
                $destination = $data['destination'] ?? null;

                $pickupAddressLine1 = $pickup->formatted_address ?? '';
                $pickcity = $pickup->name ?? '';
                $plat = $pickup->geometry->location->lat ?? null;
                $plng = $pickup->geometry->location->lng ?? null;

                // pgeolocation using raw SQL similar to original
                $pgeolocation = null;
                if ($plng !== null && $plat !== null) {
                    $pg = DB::select("SELECT 'POINT({$plng} {$plat})'::geometry as pgeolocation");
                    if (!empty($pg)) {
                        $pgeolocation = $pg[0]->pgeolocation ?? null;
                    }
                }

                // pickup address components
                $addrs = (array)($pickup->address_components ?? []);
                $parea = $pickupCity = $pstate = $pickupCountry = $pickupPin = "";
                foreach ($addrs as $res1) {
                    $types = $res1->types ?? [];
                    $type0 = $types[0] ?? '';
                    if ($type0 === "locality") {
                        $parea = $res1->long_name;
                    }
                    if ($type0 === "administrative_area_level_3") {
                        $parea = trim($parea . ", " . $res1->long_name, " ,");
                    }
                    if ($type0 === "administrative_area_level_2") {
                        $pickupCity = $res1->long_name;
                    }
                    if ($type0 === "administrative_area_level_1") {
                        $pstate = $res1->long_name;
                    }
                    if ($type0 === "country") {
                        $pickupCountry = $res1->long_name;
                    }
                    if ($type0 === "postal_code") {
                        $pickupPin = $res1->long_name;
                    }
                }
                if ($pickupCity == "") {
                    $pickupCity = $parea;
                }

                // Destination
                $dropAddressLine1 = $destination->formatted_address ?? '';
                $dropcity = $destination->name ?? '';
                $dlat = $destination->geometry->location->lat ?? null;
                $dlng = $destination->geometry->location->lng ?? null;

                $dgeolocation = null;
                if ($dlng !== null && $dlat !== null) {
                    $dg = DB::select("SELECT 'POINT({$dlng} {$dlat})'::geometry as dgeolocation");
                    if (!empty($dg)) {
                        $dgeolocation = $dg[0]->dgeolocation ?? null;
                    }
                }

                $daddrs = (array)($destination->address_components ?? []);
                $darea = $dropCity = $dstate = $dropCountry = $dropPin = "";
                foreach ($daddrs as $dres) {
                    $types = $dres->types ?? [];
                    $type0 = $types[0] ?? '';
                    if ($type0 === "locality") {
                        $darea = $dres->long_name;
                    }
                    if ($type0 === "administrative_area_level_3") {
                        $darea = trim($darea . ", " . $dres->long_name, " ,");
                    }
                    if ($type0 === "administrative_area_level_2") {
                        $dropCity = $dres->long_name;
                    }
                    if ($type0 === "administrative_area_level_1") {
                        $dstate = $dres->long_name;
                    }
                    if ($type0 === "country") {
                        $dropCountry = $dres->long_name;
                    }
                    if ($type0 === "postal_code") {
                        $dropPin = $dres->long_name;
                    }
                }
                if ($dropCity == "") {
                    $dropCity = $darea;
                }

                if ($pickupAddressLine1 != "" && $dropAddressLine1 != "") {
                    $curdt = now()->format('Y-m-d H:i:s');
                    $pickupDate = $dropDate = $curdt;

                    // Use existing distancemetrixship method
                    $chkdist = $this->distancemetrixship($plat, $plng, $dlat, $dlng, $pgeolocation, $dgeolocation);
                    if (!empty($chkdist['duration']) && $chkdist['duration'] > 0) {
                        $duration = $chkdist['duration'];
                        $tm = strtotime($curdt);
                        $dropDate = date("Y-m-d H:i:s", $tm + (int)$duration);
                    }

                    // Determine vendor_id and vehicle_id assigned to driver
                    $vendor_id = $vehicle_id = "";
                    $quantity = $valueOfGoods = 0;
                    $shipmentOption = "LTL";

                    foreach ($uoms as $cnt) {
                        $cnt = (array)$cnt;
                        if (!empty($cnt['value']) && $cnt['value'] > 0) {
                            $quantity += $cnt['value'];
                        }
                    }

                    $weight = $volume = 1;

                    // Get customer info (sx_party_members)
                    $custinfo = DB::table('sx_party_members')->select('name', 'mobile', 'code', 'city', 'email', 'company_code', 'branch_code')
                        ->where('id', $cust_id)->first();

                    if ($custinfo) {
                        $cmpname = $custinfo->name;
                        $consigneeName = $custinfo->name;
                        $consigneeCode = $custinfo->code;
                        $customer_area = $custinfo->city;
                        $consigneeEmail = $custinfo->email;
                        $consigneePhone = $custinfo->mobile;
                        $pickupCompany = $dropCompany = $cmpname;
                        $company_code = $custinfo->company_code;
                        $branch_code = $custinfo->branch_code;
                    } else {
                        $cmpname = $consigneeName = $consigneeCode = $customer_area = $consigneeEmail = $consigneePhone = $company_code = $branch_code = "";
                    }

                    // Find vehicle assigned to driver in assigned_drivers or alternative tables
                    $assigned = DB::table('assigned_drivers')->where('driver_id', $driver_id)->where('status', '1')->first();
                    if ($assigned) {
                        $vehicle_id = $assigned->vehicle_id;
                        $tbltrucks = DB::table('vendor_vehicles')->where('vehicle_id', $vehicle_id)->where('status', '1')->first();
                        if ($tbltrucks) {
                            $vendor_id = $tbltrucks->vendor_id;
                        } else {
                            $tbltrucks = DB::table('trucks_data')->where('id', $vehicle_id)->where('vendor_id', '>', 0)->where('status', '1')->first();
                            if ($tbltrucks) {
                                $vendor_id = $tbltrucks->vendor_id;
                            }
                        }
                    } else {
                        // fallback other legacy table name
                        $assigned = DB::table('tb_vehicles_drivers')->where('driver_id', $driver_id)->where('status', '1')->first();
                        if ($assigned) {
                            $vehicle_id = $assigned->vehicle_id;
                            $tbltrucks = DB::table('vendor_vehicles')->where('vehicle_id', $vehicle_id)->where('status', '1')->first();
                            if ($tbltrucks) {
                                $vendor_id = $tbltrucks->vendor_id;
                            } else {
                                $tbltrucks = DB::table('trucks_data')->where('id', $vehicle_id)->where('vendor_id', '>', 0)->where('status', '1')->first();
                                if ($tbltrucks) {
                                    $vendor_id = $tbltrucks->vendor_id;
                                }
                            }
                        }
                    }

                    if ($vendor_id != "" && $vehicle_id != "") {
                        // Determine truck type
                        $vehicle_type = "Truck";
                        $vehinfo = DB::select("SELECT t.trucktype FROM trucktypes t, trucks_data d WHERE t.id = d.truck_type AND d.id = ? LIMIT 1", [$vehicle_id]);
                        if (!empty($vehinfo)) {
                            $vehicle_type = $vehinfo[0]->trucktype ?? $vehicle_type;
                        }

                        // Prepare shipment and order numbers as original logic
                        $year = date('y');
                        $week = date('W');

                        // company_code prefix logic
                        $coun_sess = $company_code ?? '';
                        $count_code = substr($coun_sess, 0, 2);

                        // get last order for user to derive sequence
                        $ordchk = DB::table('orders')->where('user_id', $user_id)->orderByDesc('id')->first();
                        if ($ordchk) {
                            $order_id_next = $ordchk->id + 1;
                            $order_nm = $ordchk->order_id;
                            $seq_num = substr($order_nm, -6);
                            $country_codelength = strlen($count_code);
                            $wcount = $country_codelength + 2;
                            $previous_weeknumber = mb_substr($order_nm, $wcount, 2);
                            $ycount = $wcount + 2;
                            $previous_ordernumber = mb_substr($order_nm, $ycount, 5);
                            $id = ltrim($previous_ordernumber, '0');
                            if ((int)$previous_weeknumber < (int)$week) {
                                $id = '0001';
                            } else {
                                $i_id = (int)$id;
                                $i_id++;
                                $idlength = strlen((string)$i_id);
                                if ($idlength == 1) $id = "000" . $i_id;
                                else if ($idlength == 2) $id = "00" . $i_id;
                                else if ($idlength == 3) $id = "0" . $i_id;
                                else $id = (string)$i_id;
                            }
                            $ord = ($count_code ?: '') . $year . $week . $id;
                        } else {
                            $i_id = 1;
                            $id = '000' . $i_id;
                            $ord = ($count_code ?: '') . $year . $week . $id;
                        }

                        // Shipment transaction id
                        $shiptxn = "T" . ($count_code ?: '') . $week . $year . sprintf('%06d', 0);

                        // Try find last shipment for company to reuse some metadata
                        $rcentship = DB::table('shipments')->where('company_code', $company_code)->orderByDesc('id')->first();
                        $product = $branch_code = $freight_term = $freight_termname = $logicalreceiver = $physicalreceiver = $physicalsender = $logicalsender = "";
                        $modeoftransport = 1;
                        if ($rcentship) {
                            $product = $rcentship->product ?? $product;
                            $branch_code = $rcentship->branch_code ?? $branch_code;
                            $freight_term = $rcentship->freight_term ?? $freight_term;
                            $freight_termname = $rcentship->freight_termname ?? $freight_termname;
                            $logicalreceiver = $rcentship->logicalreceiver ?? $logicalreceiver;
                            $physicalreceiver = $rcentship->physicalreceiver ?? $physicalreceiver;
                            $physicalsender = $rcentship->physicalsender ?? $physicalsender;
                            $logicalsender = $rcentship->logicalsender ?? $logicalsender;
                            $modeoftransport = $rcentship->modeoftransport ?? $modeoftransport;
                        }

                        // If shiptxn doesn't exist create a tb_shipments (or shipments) record
                        $chkshmt = DB::table('shipments')->where('shipid', $shiptxn)->first();
                        if (!$chkshmt) {
                            $shipdatains = [
                                'shipid' => $shiptxn,
                                'txnid' => $shiptxn,
                                'trucktype' => "Full Truck Load",
                                'product' => $product,
                                'pickupcnt' => 1,
                                'dropcnt' => 1,
                                'unitspec' => 1,
                                'insertusr' => $cust_id,
                                'carrier' => $vendor_id,
                                'insertuserdate' => $curdt,
                                'enddate' => $dropDate,
                                'insdate' => $curdt,
                                'upddate' => $dropDate,
                                'reason' => 'SHIPMENT',
                                'purpose' => 'SEND INTEGRATION',
                                'ship_object' => 'SHIPMENT',
                                'logdate' => $curdt,
                                'status' => '1',
                                'createdon' => $curdt,
                                'updatedon' => $curdt,
                                'transport_mode' => $shipmentOption,
                                'txncode' => $ord,
                                'domainname' => $branch_code,
                                'company_code' => $company_code,
                                'branch_code' => $branch_code,
                                'freight_term' => $freight_term,
                                'freight_termname' => $freight_termname,
                                'logicalreceiver' => $logicalreceiver,
                                'physicalreceiver' => $physicalreceiver,
                                'physicalsender' => $physicalsender,
                                'logicalsender' => $logicalsender,
                                'modeoftransport' => $modeoftransport,
                                'org_id' => $user_details['org_id'] ?? null,
                                'entity' => $user_details['be_value'] ?? null
                            ];
                            $sship_id = DB::table('shipments')->insertGetId($shipdatains);
                        } else {
                            $sship_id = $chkshmt->id;
                        }

                        if ($sship_id > 0) {
                            // Insert order into orders table (legacy tb_orders)
                            $orddata = [
                                'order_id' => $ord,
                                'pickup_datetime' => $pickupDate,
                                'pickup_endtime' => $pickupDate,
                                'delivery_datetime' => $dropDate,
                                'drop_endtime' => $dropDate,
                                'pickup_company' => $pickupCompany,
                                'delivery_company' => $dropCompany,
                                'pickup_country' => $pickupCountry,
                                'delivery_country' => $dropCountry,
                                'pickup_city' => $pickupCity,
                                'delivery_city' => $dropCity,
                                'pickup_pincode' => $pickupPin,
                                'delivery_pincode' => $dropPin,
                                'pickup_address1' => $pickupAddressLine1,
                                'delivery_address1' => $dropAddressLine1,
                                'quantity' => $quantity,
                                'weight' => $weight,
                                'volume' => $volume,
                                'goods_value' => $valueOfGoods,
                                'transport_mode' => $shipmentOption,
                                'dedicated_vehicle' => 0,
                                'vehicle_type' => $vehicle_type,
                                'user_id' => $user_id,
                                'customer_id' => $cust_id,
                                'vendor_id' => $vendor_id,
                                'customer_name' => $consigneeName,
                                'customer_code' => $consigneeCode,
                                'customer_area' => $customer_area,
                                'customer_phone' => $consigneePhone,
                                'customer_email' => $consigneeEmail,
                                'vendor_name' => "",
                                'vendor_code' => "",
                                'vendor_area' => "",
                                'vendor_phone' => "",
                                'vendor_email' => "",
                                'plat' => $plat,
                                'plng' => $plng,
                                'dlat' => $dlat,
                                'dlng' => $dlng,
                                'product' => $product,
                                'shipment_id' => $sship_id,
                                'pickup_custid' => $cust_id,
                                'pickup_partyid' => $pickup_partyid,
                                'drop_custid' => $cust_id,
                                'drop_partyid' => $drop_partyid,
                                'status' => 2,
                                'updatedon' => $curdt,
                                'created_source' => 1,
                                'shipmentid' => $shiptxn,
                                'logicalreceiver' => $logicalreceiver,
                                'physicalreceiver' => $physicalreceiver,
                                'physicalsender' => $physicalsender,
                                'logicalsender' => $logicalsender,
                                'modeoftransport' => $modeoftransport,
                                'company_code' => $company_code,
                                'branch_code' => $branch_code,
                                'org_id' => $user_details['org_id'] ?? null,
                                'entity' => $user_details['be_value'] ?? null
                            ];

                            $ord_id = DB::table('orders')->insertGetId($orddata);

                            // Insert cargo details and order cargo types
                            foreach ($uoms as $ct) {
                                $ct = (array)$ct;
                                if (!empty($ct['value'])) {
                                    $cargo_whr = [
                                        'cargo_type' => $ct['name'],
                                        'quantity' => $quantity,
                                        'length' => $weight,
                                        'length_unit' => 'M',
                                        'width' => $weight,
                                        'width_unit' => 'M',
                                        'height' => $weight,
                                        'height_unit' => 'M',
                                        'weight' => $weight,
                                        'weight_unit' => 'Kg',
                                        'volume' => $volume,
                                        'volume_unit' => 'cbm',
                                        'stackable' => 0
                                    ];
                                    $checkparty = DB::table('cargo_details')->where($cargo_whr)->first();
                                    if (!$checkparty) {
                                        $cargo_ins = array_merge($cargo_whr, ['goods_description' => $ct['name'], 'createdby' => $user_id, 'createdon' => $curdt]);
                                        $cargo_id = DB::table('cargo_details')->insertGetId($cargo_ins);
                                    } else {
                                        $cargo_id = $checkparty->id;
                                    }

                                    $ins = ['cargo_type' => $ct['name'], 'order_id' => $ord_id, 'status' => '1', 'createdon' => $curdt];
                                    $checkCargoType = DB::table('order_cargotypes')->where(['cargo_type' => $ct['name'], 'order_id' => $ord_id, 'status' => '1'])->first();
                                    if (!$checkCargoType) {
                                        $cargotype = DB::table('order_cargotypes')->insertGetId($ins);
                                    } else {
                                        $cargotype = $checkCargoType->id;
                                    }

                                    $cargodata = [
                                        'order_id' => $ord_id,
                                        'cargo_id' => $cargo_id,
                                        'handling_unit' => $cargotype,
                                        'length' => $weight,
                                        'width' => $weight,
                                        'height' => $weight,
                                        'weight' => $weight,
                                        'quantity' => $quantity,
                                        'status' => '1',
                                        'createdon' => $curdt
                                    ];
                                    DB::table('order_cargodetails')->insert($cargodata);
                                }
                            }

                            // Insert order references
                            foreach ($references as $ref) {
                                if ($ref !== null) {
                                    $ref = (array)$ref;
                                    if (isset($ref['value']) && $ref['value'] !== "" && !empty($ref['reference_id'])) {
                                        $ins = ['order_id' => $ord_id, 'reference_id' => $ref['reference_id'], 'ref_value' => $ref['value'], 'status' => '1', 'createdon' => $curdt];
                                        DB::table('order_references')->insert($ins);
                                    }
                                }
                            }

                            // order parties: find type ids for Shipper and Consignee
                            $shipperparty = DB::table('party_types')->where('type_name', 'Shipper')->where('status', '1')->select('id')->first();
                            $shipperparty_id = $shipperparty->id ?? 1;
                            $consigneeparty = DB::table('party_types')->where('type_name', 'Consignee')->where('status', '1')->select('id')->first();
                            $consignee_party_id = $consigneeparty->id ?? 3;

                            $checkparty = DB::table('order_parties')->where(['order_id' => $ord_id, 'party_id' => $pickup_partyid, 'party_type' => $shipperparty_id, 'org_id' => $user_details['org_id'] ?? null, 'entity' => $user_details['be_value'] ?? null])->first();
                            if (!$checkparty) {
                                $partyarr = ['order_id' => $ord_id, 'order_number' => $ord, 'party_id' => $pickup_partyid, 'party_type' => $shipperparty_id, 'status' => '1', 'createdon' => $curdt, 'org_id' => $user_details['org_id'] ?? null, 'entity' => $user_details['be_value'] ?? null];
                                DB::table('order_parties')->insert($partyarr);
                            }
                            $checkparty = DB::table('order_parties')->where(['order_id' => $ord_id, 'party_id' => $drop_partyid, 'party_type' => $consignee_party_id, 'org_id' => $user_details['org_id'] ?? null, 'entity' => $user_details['be_value'] ?? null])->first();
                            if (!$checkparty) {
                                $partyarr = ['order_id' => $ord_id, 'order_number' => $ord, 'party_id' => $drop_partyid, 'party_type' => $consignee_party_id, 'status' => '1', 'createdon' => $curdt, 'org_id' => $user_details['org_id'] ?? null, 'entity' => $user_details['be_value'] ?? null];
                                DB::table('order_parties')->insert($partyarr);
                            }

                            // Create shipment (tb_shifts / tb_shifts legacy name -> here: shifts)
                            $checkshipment = DB::table('shifts')->where('shipmentid', $shiptxn)->where('status', '1')->first();
                            if (!$checkshipment) {
                                $shipment = [
                                    'user_id' => $user_id,
                                    'stime' => date("H:i", strtotime($curdt)),
                                    'etime' => date("H:i", strtotime($dropDate)),
                                    'splace' => $pickupAddressLine1,
                                    'slat' => $plat,
                                    'slng' => $plng,
                                    'eplace' => $dropAddressLine1,
                                    'elat' => $dlat,
                                    'elng' => $dlng,
                                    'scity' => $pickupCity,
                                    'dcity' => $dropCity,
                                    'zone_id' => 1,
                                    'empshift_start' => date("H:i", strtotime($curdt)),
                                    'empshift_end' => date("H:i", strtotime($dropDate)),
                                    'trip_type' => 0,
                                    'startdate' => $curdt,
                                    'enddate' => $dropDate,
                                    'shipment_name' => 1,
                                    'shipmentid' => $shiptxn,
                                    'shipment_id' => $sship_id,
                                    'customer_id' => $cust_id,
                                    'transport_mode' => $shipmentOption,
                                    'vendor_id' => $vendor_id,
                                    'carrier_type' => 0,
                                    'txnid' => $ord,
                                    'weight' => $weight,
                                    'volume' => $volume,
                                    'units' => count($uoms),
                                    'domainname' => $branch_code,
                                    'vehicle_type' => $vehicle_type,
                                    'company_code' => $company_code,
                                    'branch_code' => $branch_code,
                                    'status' => '1',
                                    'created_on' => $curdt,
                                    'updated_on' => $curdt,
                                    'org_id' => $user_details['org_id'] ?? null,
                                    'entity' => $user_details['be_value'] ?? null
                                ];

                                // If geographyPoint helper exists, compute fields. Otherwise skip
                                if (function_exists('geographyPoint')) {
                                    $shipment['egeolocation'] = geographyPoint($shipment['elng'], $shipment['elat']);
                                    $shipment['sgeolocation'] = geographyPoint($shipment['slng'], $shipment['slat']);
                                }

                                $ship_id = DB::table('shifts')->insertGetId($shipment);

                                // create shiporder stops
                                $stopleg1_id = $stopleg2_id = null;
                                $chkstoplegs = DB::table('shiporder_stops')->where([
                                    'stopname' => $pickupAddressLine1,
                                    'stopcity' => $pickupCity,
                                    'shipment_id' => $ship_id,
                                    'txncode' => $ord,
                                    'stoptype' => 'P'
                                ])->first();
                                if (!$chkstoplegs) {
                                    $shiporder = [
                                        'stopname' => $pickupAddressLine1,
                                        'plat' => $plat,
                                        'plng' => $plng,
                                        'stopcity' => $pickupCity,
                                        'address' => $pickupAddressLine1,
                                        'stoptype' => 'P',
                                        'stopstatus' => 0,
                                        'shipment_id' => $ship_id,
                                        'ordernumber' => 1,
                                        'startdate' => $curdt,
                                        'enddate' => $curdt,
                                        'weight' => $weight,
                                        'volume' => $volume,
                                        'shipmentstopid' => 0,
                                        'serviceprovider' => "LMV",
                                        'ship_units' => count($uoms),
                                        'txncode' => $ord,
                                        'status' => '1',
                                        'created_on' => $curdt,
                                        'updated_on' => $curdt,
                                        'order_id' => $ord_id
                                    ];
                                    if (function_exists('geographyPoint')) {
                                        $shiporder['pgeolocation'] = geographyPoint($shiporder['plng'], $shiporder['plat']);
                                    }
                                    $stopleg1_id = DB::table('shiporder_stops')->insertGetId($shiporder);
                                } else {
                                    $stopleg1_id = $chkstoplegs->id;
                                }

                                $chkstoplegs1 = DB::table('shiporder_stops')->where([
                                    'stopname' => $dropAddressLine1,
                                    'stopcity' => $dropCity,
                                    'shipment_id' => $ship_id,
                                    'txncode' => $ord,
                                    'stoptype' => 'D'
                                ])->first();
                                if (!$chkstoplegs1) {
                                    $shiporder = [
                                        'stopname' => $dropAddressLine1,
                                        'plat' => $dlat,
                                        'plng' => $dlng,
                                        'stopcity' => $dropCity,
                                        'address' => $dropAddressLine1,
                                        'stoptype' => 'D',
                                        'stopstatus' => 0,
                                        'shipment_id' => $ship_id,
                                        'ordernumber' => 2,
                                        'startdate' => $dropDate,
                                        'enddate' => $dropDate,
                                        'weight' => $weight,
                                        'volume' => $volume,
                                        'shipmentstopid' => 0,
                                        'serviceprovider' => "LMV",
                                        'ship_units' => count($uoms),
                                        'txncode' => $ord,
                                        'status' => '1',
                                        'created_on' => $curdt,
                                        'updated_on' => $curdt,
                                        'order_id' => $ord_id
                                    ];
                                    if (function_exists('geographyPoint')) {
                                        $shiporder['pgeolocation'] = geographyPoint($shiporder['plng'], $shiporder['plat']);
                                    }
                                    $stopleg2_id = DB::table('shiporder_stops')->insertGetId($shiporder);
                                } else {
                                    $stopleg2_id = $chkstoplegs1->id;
                                }

                                if ($stopleg1_id && $stopleg2_id) {
                                    $chkemp = DB::table('employee')->where([
                                        'assoc_id' => $ord,
                                        'order_id' => $ord,
                                        'shift_id' => $ship_id,
                                        'stop_id' => $stopleg1_id,
                                        'drop_stopid' => $stopleg2_id
                                    ])->first();
                                    if (!$chkemp) {
                                        $stops = [
                                            'assoc_id' => $ord,
                                            'pickup' => $pickupAddressLine1,
                                            'plat' => $plat,
                                            'plng' => $plng,
                                            'drop' => $dropAddressLine1,
                                            'dlat' => $dlat,
                                            'dlng' => $dlng,
                                            'pickup_city' => $pickupCity,
                                            'drop_city' => $dropCity,
                                            'pickup_datetime' => $curdt,
                                            'drop_datetime' => $dropDate,
                                            'name' => 'Goods',
                                            'phone' => "",
                                            'address' => $pickupAddressLine1,
                                            'user_id' => $user_id,
                                            'password' => '123456',
                                            'status' => '1',
                                            'createdon' => $curdt,
                                            'updatedon' => $curdt,
                                            'material_id' => 0,
                                            'capacity' => $quantity,
                                            'information' => "",
                                            'shipment_weight' => $weight,
                                            'shipment_volume' => $volume,
                                            'ship_type' => 'P',
                                            'customer_id' => $cust_id,
                                            'vendor_id' => $vendor_id,
                                            'shipment_id' => $sship_id,
                                            'startdate' => $curdt,
                                            'enddate' => $dropDate,
                                            'shift_id' => $ship_id,
                                            'stop_order' => 1,
                                            'drop_order' => 2,
                                            'basic_stop' => 1,
                                            'stop_id' => $stopleg1_id,
                                            'drop_stopid' => $stopleg2_id,
                                            'order_id' => $ord,
                                            'pkgitemid' => 'Goods',
                                            'no_of_pkgs' => count($uoms),
                                            'domainname' => $branch_code,
                                            'accepted' => 1
                                        ];
                                        if (function_exists('geographyPoint')) {
                                            $stops['pgeolocation'] = geographyPoint($stops['plng'], $stops['plat']);
                                            $stops['dgeolocation'] = geographyPoint($stops['dlng'], $stops['dlat']);
                                        }
                                        DB::table('employee')->insert($stops);
                                    }
                                    // assign to carrier: shft_veh
                                    $veharr = ['user_id' => $user_id, 'shft_id' => $ship_id, 'carrier_id' => $vendor_id, 'vehicle_id' => $vehicle_id];
                                    $chkveh = DB::table('shft_veh')->where($veharr)->first();
                                    if (!$chkveh) {
                                        $insveh = array_merge($veharr, ['register_number' => "", 'status' => '1', 'created_on' => $curdt, 'updated_on' => $curdt]);
                                        $shipvehid = DB::table('shft_veh')->insertGetId($insveh);
                                    } else {
                                        $shipvehid = $chkveh->id;
                                        DB::table('shft_veh')->where($veharr)->update(['status' => '1', 'updated_on' => $curdt]);
                                    }

                                    // assign priority stops shft_veh_emp
                                    $veharr2 = ['user_id' => $user_id, 'shft_veh_id' => $shipvehid];
                                    $chkveh2 = DB::table('shft_veh_emp')->where($veharr2)->first();
                                    if (!$chkveh2) {
                                        $getemp = DB::table('employee')->where('shift_id', $ship_id)->get();
                                        if ($getemp->count() > 0) {
                                            $pri = 1;
                                            foreach ($getemp as $gt) {
                                                $insveh1 = ['user_id' => $user_id, 'shft_veh_id' => $shipvehid, 'emp_id' => $gt->id, 'priority' => $pri, 'pickup_time' => $gt->pickup_datetime, 'created_on' => $curdt, 'updated_on' => $curdt, 'status' => '1', 'drop_time' => $gt->drop_datetime];
                                                DB::table('shft_veh_emp')->insert($insveh1);
                                                $pri++;
                                            }
                                        }
                                    } else {
                                        $getemp = DB::table('employee')->where('shift_id', $ship_id)->get();
                                        if ($getemp->count() > 0) {
                                            $pri = 1;
                                            foreach ($getemp as $gt) {
                                                $veharr = ['user_id' => $user_id, 'shft_veh_id' => $shipvehid, 'emp_id' => $gt->id];
                                                $chkveh = DB::table('shft_veh_emp')->where($veharr)->first();
                                                if (!$chkveh) {
                                                    $insveh1 = ['user_id' => $user_id, 'shft_veh_id' => $shipvehid, 'emp_id' => $gt->id, 'priority' => $pri, 'pickup_time' => $gt->pickup_datetime, 'created_on' => $curdt, 'updated_on' => $curdt, 'status' => '1', 'drop_time' => $gt->drop_datetime];
                                                    DB::table('shft_veh_emp')->insert($insveh1);
                                                } else {
                                                    DB::table('shft_veh_emp')->where('id', $chkveh->id)->update(['priority' => $pri, 'pickup_time' => $gt->pickup_datetime, 'updated_on' => $curdt, 'status' => '1', 'drop_time' => $gt->drop_datetime]);
                                                }
                                                $pri++;
                                            }
                                        }
                                    }

                                    // update stop_status to indicate coming from E-Booking (status_id = 9)
                                    $chk = DB::table('stop_status')->where(['shipment_id' => $ship_id, 'status_id' => 9])->first();
                                    if (!$chk) {
                                        $ins = ['order_id' => $ord_id, 'shipment_id' => $ship_id, 'stop_id' => 0, 'stop_detail_id' => 0, 'stop_type' => "", 'trip_id' => 0, 'status_id' => 9, 'status_code' => "0100", 'status' => '1', 'reason' => "Coming from E-Booking", 'createdon' => $curdt];
                                        DB::table('stop_status')->insert($ins);
                                    }

                                    // Accept shipment: create trip record if not exists
                                    $chktrip = DB::table('trips')->where(['shift_id' => $ship_id, 'vehicle_id' => $vehicle_id, 'driver_id' => $driver_id])->first();
                                    if (!$chktrip) {
                                        $tripsdata = ['shift_id' => $ship_id, 'vehicle_id' => $vehicle_id, 'driver_id' => $driver_id, 'stime' => $curdt, 'start_imei' => $imei, 'splace' => $pickupAddressLine1, 'created_on' => $curdt, 'updated_on' => $curdt, 'status' => '1'];
                                        $tid = DB::table('trips')->insertGetId($tripsdata);
                                    } else {
                                        $tid = $chktrip->id;
                                    }

                                    // get country hours (legacy)
                                    $gethrs = DB::select("SELECT c.cntry_hrs FROM country_master c, sx_users u WHERE c.country_code = u.country_code AND u.id = ? AND c.status = '1' LIMIT 1", [$user_id]);
                                    $hrs = $gethrs[0]->cntry_hrs ?? null;

                                    $postdata = [
                                        "shipment_id" => $ship_id,
                                        "trip_id" => $tid,
                                        "driver_id" => $driver_id,
                                        "order_id" => $ord,
                                        "stop_id" => $stopleg1_id,
                                        "latitude" => $plat,
                                        "longitude" => $plng,
                                        "curtz" => $curtz,
                                        "hrs" => $hrs
                                    ];

                                    // status update: insert stop_status with status_id = 10 (trip start)
                                    $chqry = DB::table('stop_status')->where(['shipment_id' => $ship_id, 'stop_id' => 0, 'stop_detail_id' => 0, 'trip_id' => $tid, 'status_id' => 10])->first();
                                    if (!$chqry) {
                                        $insarry = ['order_id' => $ord_id, 'shipment_id' => $ship_id, 'stop_id' => 0, 'stop_detail_id' => 0, 'stop_type' => '', 'trip_id' => $tid, 'status_id' => 10, 'latitude' => $plat, 'longitude' => $plng, 'status' => '1', 'status_code' => "0212", 'reason' => "From Mobile", 'vehicle_id' => $vehicle_id, 'driver_id' => $driver_id, 'createdon' => $curdt];
                                        if (function_exists('geographyPoint')) {
                                            $insarry['geolocation'] = geographyPoint($insarry['longitude'], $insarry['latitude']);
                                        }
                                        DB::table('stop_status')->insert($insarry);
                                    }

                                    // prepare response
                                    $res = $d = [];
                                    $res['doc_types'] = DB::table('document_types')->select('id', 'type_name')->where('status', '1')->get()->toArray();
                                    $d['id'] = $tid;
                                    $d['shift_id'] = $ship_id;
                                    $d['user_id'] = $user_id;
                                    $d['driver_id'] = $driver_id;
                                    $d['vehicle_id'] = $vehicle_id;
                                    $d['shipweight'] = $quantity;
                                    $d['imei'] = $imei;
                                    $d['stime'] = date("H:i", strtotime($curdt));
                                    $d['etime'] = date("H:i", strtotime($dropDate));
                                    $d['trip_type'] = 0;
                                    $d['startdate'] = $curdt;
                                    $d['enddate'] = $dropDate;
                                    $d["saddress"] = $pickupAddressLine1;
                                    $d["daddress"] = $dropAddressLine1;
                                    $pod = $signature = "";
                                    $d["splace"] = $pickupCity;
                                    $d["eplace"] = $dropCity;
                                    $d["scity"] = $pickupCity;
                                    $d["dcity"] = $dropCity;
                                    $d["slat"] = $plat;
                                    $d["slng"] = $plng;
                                    $d["elat"] = $dlat;
                                    $d["elng"] = $dlng;
                                    $d["shipmentid"] = $shiptxn;
                                    $d["shift_veh_id"] = $shipvehid ?? null;
                                    $d['astatus'] = 0;
                                    $d['sno'] = 1;
                                    $d['distance'] = $chkdist['disttext'] ?? null;
                                    $d['duration'] = isset($chkdist['duration']) ? round((($chkdist['duration'] / 40) * 60), 2) . " min" : null;
                                    $d['startdate'] = strtotime($curdt);
                                    $d['enddate'] = strtotime($dropDate);
                                    $d['pod'] = $pod;
                                    $d['signature'] = $signature;
                                    $res['trips'] = $d;
                                    $response = ["status" => 1, "data" => $res];
                                } else {
                                    $response = ["status" => 0, "data" => "Source/Destination Locations Are Wrong"];
                                }
                            } else {
                                $response = ["status" => 0, "data" => "Already exists!"];
                            }
                        } else {
                            $response = ["status" => 0, "data" => "Shipment Already exists!"];
                        }
                    } else {
                        $response = ["status" => 0, "data" => "No vehicle assigned to you"];
                    }
                } else {
                    $response = ["status" => 0, "data" => "Please enter proper locations"];
                }
            } else {
                $response = ["status" => 0, "data" => "Enter Customer Name"];
            }
        }

        // Return printable if available, else plain response
        $output = method_exists($this, 'printable') ? $this->printable($response) : $response;
        return response()->json($output);
    }

}