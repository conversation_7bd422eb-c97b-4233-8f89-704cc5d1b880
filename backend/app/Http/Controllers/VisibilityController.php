<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VisibilityController extends Controller
{
    public function __construct()
    {
        
    }

    /**
     * Display a listing of Shipments.
     */
    public function shipments(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $id = $user->id;
            $org_id = $user->default_org_id;

            $results = DB::table('shipment as s')
                ->leftJoin('shft_veh as sv', 's.id', '=', 'sv.shft_id')
                ->join('trucks_data as t', 'sv.vehicle_id', '=', 't.id')
                ->leftJoin('assigned_drivers as ad', 't.id', '=', 'ad.vehicle_id')
                ->join('truck_drivers as td', 'ad.driver_id', '=', 'td.id')
                ->join('sx_users as u_customer', 's.customer_id', '=', 'u_customer.id')
                ->join('sx_users as u_vendor', 's.vendor_id', '=', 'u_vendor.id')
                ->select(
                    's.id',
                    's.splace',
                    's.eplace',
                    's.startdate',
                    's.enddate',
                    'u_customer.employee_name as customer_name',
                    'u_vendor.employee_name as carrier_name',
                    'td.name as driver_name',
                    't.truck_number as vehicle_number'
                )
                ->where('s.user_id', $id)
                ->where('s.org_id', $org_id)
                ->orderBy('s.id', 'asc')
                ->get();

            $shipmentsData = $results->toArray();
            $shiftDetails = [];
            foreach ($shipmentsData as $eachShipment) {
                $shiftDetails[$eachShipment->id] = $eachShipment;
                $shiftDetails[$eachShipment->id]['stops'] = [];
            }

            $employeeData = $stopIds = $stopsData = [];
            if (!empty($shipmentsData)) {
                $shiftIds = array_column($shipmentsData, 'id');
                $getStopIds = DB::table('shiporder_stops')
                    ->select('id', 'shipment_id', 'plat', 'plng', 'stoptype', 'stopstatus')
                    ->whereIn('shipment_id', $shiftIds)
                    ->where('status', 1)
                    ->orderBy('id', 'asc')
                    ->get();

                foreach ($getStopIds as $stops) {
                    if ($stops->id > 0) {
                        $stopsData[$stops->id] = $stops;
                        $stopIds[] = $stops->id;
                    }
                }
                if (empty($stopIds)) {
                    return response()->json([
                        'status' => 'success',
                        'message' => 'shipments retrieved successfully.',
                        'data' => $shiftDetails,
                        'count' => count($shiftDetails)
                    ], 200);
                }
                $getEmployeeIds = DB::select("SELECT id,stop_id,drop_stopid
                        FROM shiporder_stop_sequence
                        WHERE stop_id IN (" . implode(',', $stopIds) . ") 
                            AND status=1
                    UNION 
                    SELECT id,stop_id,drop_stopid
                        FROM shiporder_stop_sequence
                        WHERE drop_stopid IN  (" . implode(',', $stopIds) . ")
                            AND status=1");
                $employeeData = $getEmployeeIds;
                $stopsData = $this->arrangeEmployeeStopsData($employeeData, $stopsData, $stopIds);
                $employeeStopDetails = [];
                foreach ($employeeData as $i => $iValue) {
                    $employeeStopDetails[$iValue['stop_id']][] = $employeeData[$i]['id'];
                }
                $stopsData = $this->getEmployeeStopsStatus($employeeStopDetails, $stopIds, $shiftIds, $stopsData);
                foreach ($stopsData as $eachStop) {
                    $shipmentId = $eachStop['shipment_id'];
                    $shiftDetails[$shipmentId]['stops'][] = $eachStop;
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'shipments retrieved successfully.',
                'data' => $shiftDetails,
                'count' => count($shiftDetails)
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error getting shipments data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get shipments data.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function arrangeEmployeeStopsData($employeeData, $stopsData, $stopIds)
    {
        foreach ($employeeData as $i => $iValue) {
            $stopId = $iValue['stop_id'];
            if (in_array($stopId, $stopIds)) {
                $stopsData[$stopId]['employee_id'] = $employeeData[$i]['id'];
            }
            $dropStopId = $iValue['drop_stopid'];
            if (in_array($dropStopId, $stopIds)) {
                $stopsData[$dropStopId]['employee_id'] = $employeeData[$i]['id'];
            }
        }
        return $stopsData;
    }

    public function getEmployeeStopsStatus($employeeStopDetails, $stopIds, $shiftIds, $stopsData)
    {
        foreach ($employeeStopDetails as $stopId => $employeeIds) {
            if (in_array($stopId, $stopIds)) {
                $stopStatus = 0;
                foreach ($employeeIds as $employeeId) {
                    if (isset($stopsData[$stopId]['employee_id']) && $stopsData[$stopId]['employee_id'] == $employeeId) {
                        $stopStatus = 1;
                    }
                }
                $stopsData[$stopId]['stopstatus'] = $stopStatus;
            }
        }
        return $stopsData;
    }

    public function openAssignVehicle(Request $request)
    {
        $data = [];
        $data['driver'] = 0;
        $shift_id = $request->input('shift_id');
        $carrier_id = $request->input('carrier_id');
        $data["volume"] = $request->input('volume');
        $data["vehicle_id"] = $request->input('vehicle_id');
        $data["shipment_id"] = $shift_id;
        $data["carrier_id"] = $carrier_id;
        $chklegs = DB::table('shipment')
            ->select('id')
            ->where('shift_leg_id', $shift_id)
            ->limit(1)
            ->get();
        if (count($chklegs) == 0) {
            $schedule_date = DB::table('shipment')->select("*")->where('id', $shift_id)->limit(1)->get();
            $data["schedule_date"] = "";
            if (count($schedule_date) > 0) {
                $data["schedule_date"] = $schedule_date[0]->schedule_date;
            }
            if ($data["schedule_date"] == "") {
                $data["schedule_date"] = date("Y-m-d H:i:s");
            }
            $data["carrier_instructions"] = isset($schedule_date[0]) ? $schedule_date[0]->carrier_instructions : null;
            $data["weight_capacity"] = isset($schedule_date[0]) ? $schedule_date[0]->weight_capacity : null;
            $data["volume_capacity"] = isset($schedule_date[0]) ? $schedule_date[0]->volume_capacity : null;
            $data["additional_conditions"] = isset($schedule_date[0]) ? $schedule_date[0]->additional_conditions : null;
            $data["temperature_regime"] = isset($schedule_date[0]) ? $schedule_date[0]->temperature_regime : null;
            $data["time_for_loading_penality_rate"] = isset($schedule_date[0]) ? $schedule_date[0]->time_for_loading_penality_rate : null;
            $data['vehicle_type'] = isset($schedule_date[0]) ? $schedule_date[0]->vehicle_type : null;
            $category_data = DB::table('shipment_rates')
                ->select('category', 'rate')
                ->where('shift_id', $shift_id)
                ->limit(1)
                ->get();
            $data["category"] = isset($category_data[0]) ? $category_data[0]->category : null;
            $data["rate"] = isset($category_data[0]) ? $category_data[0]->rate : null;
            $getCarrierCode = DB::table('sx_users')
                ->select('employee_name')
                ->where('id', $carrier_id)
                ->limit(1)
                ->get();
            $data["carrier_code"] = isset($getCarrierCode[0]) ? $getCarrierCode[0]->employee_name : null;
            $driver_query = DB::table('shft_veh_histories')
                ->select('driver_id')
                ->where('shft_id', $shift_id)
                ->where('status', 1)
                ->orderBy('id', 'desc')
                ->limit(1)
                ->get();
            if(count($driver_query) > 0){
                $data['driver'] = $driver_query[0]->driver_id;
            }else{
                $fallback_query = DB::table('shft_veh')
                    ->join('assigned_drivers', 'shft_veh.vehicle_id', '=', 'assigned_drivers.vehicle_id')
                    ->select('assigned_drivers.driver_id')
                    ->where('shft_veh.shft_id', $shift_id)
                    ->where('shft_veh.status', 1)
                    ->where('assigned_drivers.status', 1)
                    ->limit(1)
                    ->get();
                if(count($fallback_query) > 0){
                    $data['driver'] = $fallback_query[0]->driver_id;
                }
            }
            $data['rate_categories'] = DB::table('shipment_rate_categories')
                ->select('id', 'name')
                ->where('status', 1)
                ->get();
        }
        return response()->json([
            'status' => 'success',
            'message' => 'Shipment details retrieved for Assign Vehicle successfully.',
            'data' => $data
        ], 200);
    }

    public function getHistoryAndDocuments(Request $request)
    {
        $validated = $request->validate([
            'shift_id' => 'required|integer|exists:trip_shifts,id',
            'order_id' => 'nullable|integer',
            'stop_id' => 'nullable|integer',
            'pickup_id' => 'nullable|integer',
            'report_id' => 'nullable|boolean'
        ]);

        try {
            $user = Auth::guard('api')->user();
            $timezone = $user->usr_tzone['timezone'] ?? 'UTC';
            
            $data = $this->tripHistoryService->getTripHistory(
                $validated['shift_id'],
                $validated['order_id'],
                $validated['stop_id'], 
                $validated['pickup_id'],
                $validated['report_id'],
                $timezone
            );

            return new TripHistoryResource($data);
        } catch (\Exception $e) {
            Log::error('Trip history error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve trip history',
            $dlng = $shift->row()->elng;
            $slat = $shift->row()->slat;
            $slng = $shift->row()->slng;
        }
        $trip = $this->db->query(
            "SELECT id,vehicle_id,status,dlat as latitude,dlng as longitude from tb_trips where shift_id = '$shift_id' $whr ORDER BY id DESC"
        );
        if ($trip->num_rows() > 0) {
            $trip_id = $trip->row()->id;
            $vehicle_id = $trip->row()->vehicle_id;
            if ($trip->row()->status == 1) {
                $rec = $this->db->query(
                    "SELECT latitude,longitude from tb_trucks_data WHERE id = '$vehicle_id' LIMIT 1"
                );
                if ($rec->num_rows() > 0) {
                    $slat = $rec->row()->latitude;
                    $slng = $rec->row()->longitude;
                }
            } else {
                $slat = $trip->row()->latitude;
                $slng = $trip->row()->longitude;
            }
            $data["drivers"] = [];
            $vehicles = $this->db->query(
                "SELECT vehicle_id FROM tb_shft_veh where shft_id=$shift_id AND status=1 GROUP BY vehicle_id"
            );
            $vs = [];
            $chk = array();
            $primary_driver = "";
            if ($vehicles->num_rows() > 0) {
                foreach ($vehicles->result() as $veh) {
                    $vs[] = $veh->vehicle_id;
                }
                $vs = "(" . implode(",", $vs) . ")";
                $arr = $this->db->query(
                    "SELECT d.id,d.name,d.contact_num,td.created_on as createdon from tbl_assigned_drivers td LEFT JOIN tb_truck_drivers d ON d.id = td.driver_id WHERE td.vehicle_id in $vs AND td.status ='1' GROUP BY d.id ORDER BY td.created_on ASC"
                )->result_array();
                foreach ($arr as $ar1) {
                    $primary_driver = $ar1["id"];
                    if (!in_array($ar1["id"], $chk)) {
                        $chk[] = $ar1["id"];
                        $data["drivers"][] = $ar1;
                    }
                }
                $arr = $this->db->query(
                    "SELECT d.id,d.name,d.contact_num,td.createdon as createdon from tb_trip_drivers td LEFT JOIN tb_truck_drivers d ON d.id = td.driver_id WHERE td.trip_id = $trip_id AND td.status ='1' AND td.driver_id != '$primary_driver' GROUP BY d.id ORDER BY td.createdon ASC"
                )->result_array();
                foreach ($arr as $ar) {
                    if (!in_array($ar["id"], $chk)) {
                        $chk[] = $ar["id"];
                        $data["drivers"][] = $ar;
                    }
                }
            }
            $data["primary_driver"] = $primary_driver;
        } else {
            $trip_id = 0;
            $data["drivers"] = [];
            $vehicles = $this->db->query(
                "SELECT vehicle_id FROM tb_shft_veh where shft_id=$shift_id AND status=1 GROUP BY vehicle_id"
            );
            $vs = [];
            $primary_driver = "";
            $chk = array();
            if ($vehicles->num_rows() > 0) {
                foreach ($vehicles->result() as $veh) {
                    $vs[] = $veh->vehicle_id;
                }
                $vs = "(" . implode(",", $vs) . ")";
                $arr = $this->db->query(
                    "SELECT d.id,d.name,d.contact_num,td.created_on as createdon from tbl_assigned_drivers td LEFT JOIN tb_truck_drivers d ON d.id = td.driver_id WHERE td.vehicle_id in $vs AND td.status ='1' GROUP BY d.id ORDER BY td.created_on ASC"
                )->result_array();
                foreach ($arr as $ar1) {
                    $primary_driver = $ar1["id"];
                    if (!in_array($ar1["id"], $chk)) {
                        $chk[] = $ar1["id"];
                        $data["drivers"][] = $ar1;
                    }
                }
                $arr2 = $this->db->query(
                    "SELECT d.id,d.name,d.contact_num,td.createdon as createdon from tb_vehicles_drivers td LEFT JOIN tb_truck_drivers d ON d.id = td.driver_id WHERE vehicle_id in $vs AND td.status ='1' AND td.driver_id != '$primary_driver' GROUP BY d.id ORDER BY td.createdon ASC"
                )->result_array();
                foreach ($arr2 as $ar) {
                    if (!in_array($ar["id"], $chk)) {
                        $chk[] = $ar["id"];
                        $data["drivers"][] = $ar;
                    }
                }
            }
            $data["primary_driver"] = $primary_driver;
        }
        if (strlen($stop_id) > 0 && strlen($pickup_id) > 0 && strlen($order_id) == 0) {
            $sql = $this->db->query(
                "SELECT ts.id,ts.latitude,ts.longitude,ts.loc_name,ts.stop_id,ts.stop_type,sm.status_name,ts.status_code,convertToClientTZ(ts.createdon,'" . $curtz . "') as createdon,e.address,e.order_id,e.pickup,e.drop from tb_stop_status ts LEFT JOIN tb_status_master sm ON sm.id=ts.status_id LEFT JOIN tb_employee e ON e.shift_id = ts.shipment_id AND (e.stop_id=ts.stop_id OR e.drop_stopid=ts.stop_id) WHERE ts.shipment_id = '$shift_id' GROUP BY ts.id ORDER BY ts.createdon ASC"
            );
            $sql1 = $this->db->query(
                "SELECT ts.id,ts.latitude,ts.longitude,ts.stop_id,ts.stop_type,dt.type_name,ts.createdby,ts.createdon,ts.imgpath,e.address,e.order_id,e.pickup,e.drop from tb_pod_uploads ts LEFT JOIN tb_document_types dt ON dt.id=ts.doc_type LEFT JOIN tb_employee e ON e.id = ts.stop_detail_id WHERE ts.stop_detail_id = $pickup_id AND ts.shipment_id = $shift_id AND ts.stop_id = $stop_id GROUP BY ts.id ORDER BY ts.createdon ASC"
            );
            $emp = $this->db->query(
                "SELECT plat,plng,dlat,dlng,stop_id FROM tb_employee where id = $pickup_id AND (stop_id= $stop_id OR drop_stopid = $stop_id)"
            );
            if ($emp->num_rows() > 0) {
                if ($emp->row()->stop_id = $stop_id) {
                    $dlat = $emp->row()->plat;
                    $dlng = $emp->row()->plng;
                } else {
                    $dlat = $emp->row()->dlat;
                    $dlng = $emp->row()->dlng;
                }
                if ($trip_id == 0) {
                    if ($emp->num_rows() > 0) {
                        if ($emp->row()->stop_id = $stop_id) {
                            $slat = $emp->row()->plat;
                            $slng = $emp->row()->plng;
                        }
                    }
                }
            }
        } elseif (strlen($order_id) > 0) {
            $sql = $this->db->query(
                "SELECT ts.id,ts.latitude,ts.longitude,ts.loc_name,ts.stop_id,ts.stop_type,sm.status_name,ts.status_code,ts.comment,convertToClientTZ(ts.createdon, ?) as createdon,e.address,o.order_id,e.pickup,e.drop from tb_stop_status ts LEFT JOIN tb_orders o ON o.id=ts.order_id LEFT JOIN tb_status_master sm ON sm.id=ts.status_id LEFT JOIN tb_employee e ON e.order_id = o.order_id AND (e.stop_id=ts.stop_id OR e.drop_stopid=ts.stop_id) WHERE o.order_id = ? GROUP BY ts.id ORDER BY ts.createdon ASC",
                [$curtz, $order_id]
                );
                $sql1 = $this->db->query(
                "SELECT ts.id,ts.latitude,ts.longitude,ts.stop_id,ts.stop_type,dt.type_name,ts.createdby,convertToClientTZ(ts.createdon,?) as createdon,ts.imgpath,e.address,o.order_id,e.pickup,e.drop from tb_pod_uploads ts  LEFT JOIN tb_document_types dt ON dt.id=ts.doc_type LEFT JOIN tb_employee e ON e.id = ts.stop_detail_id LEFT JOIN tb_orders o ON o.id = ts.order_id  WHERE o.order_id = ? GROUP BY ts.id ORDER BY ts.createdon ASC",
                [$curtz, $order_id]
                );
        }else {
            if (strlen($stop_id) > 0) {
                $sql = $this->db->query(
                    "SELECT ts.id,ts.latitude,ts.longitude,ts.loc_name,ts.stop_id,ts.stop_type,sm.status_name,ts.status_code,convertToClientTZ(ts.createdon,'" . $curtz . "') as createdon,e.address,e.order_id,e.pickup,e.drop from tb_stop_status ts LEFT JOIN tb_status_master sm ON sm.id=ts.status_id LEFT JOIN tb_employee e ON e.shift_id = ts.shipment_id AND (e.stop_id=ts.stop_id OR e.drop_stopid=ts.stop_id) WHERE ts.shipment_id = '$shift_id' GROUP BY ts.id ORDER BY ts.createdon ASC"
                );
                $sql1 = $this->db->query(
                    "SELECT ts.id,ts.latitude,ts.longitude,ts.stop_id,ts.stop_type,dt.type_name,ts.createdby,convertToClientTZ(ts.createdon,'" . $curtz . "') as createdon,ts.imgpath,e.address,e.order_id,e.pickup,e.drop from tb_pod_uploads ts LEFT JOIN tb_document_types dt ON dt.id=ts.doc_type LEFT JOIN tb_employee e ON e.id = ts.stop_detail_id LEFT JOIN tb_shiporder_stops ss ON (ss.id=e.drop_stopid OR ss.id=e.stop_id) WHERE ts.stop_id = $stop_id AND ts.shipment_id = $shift_id GROUP BY ts.id ORDER BY ts.createdon ASC"
                );
                $stops = $this->db->query(
                    "SELECT * FROM tb_shiporder_stops WHERE shipment_id = $shift_id AND id >=$stop_id ORDER BY id ASC limit 2"
                );
                if ($stops->num_rows() > 0) {
                    $stops = $stops->result_array();
                    $slat = $stops[0]["plat"];
                    $slng = $stops[0]["plng"];
                    if ($trip_id == 0) {
                        if (count($stops) > 1) {
                            $slat = $stops[0]["plat"];
                            $slng = $stops[0]["plng"];
                            $dlat = $stops[1]["plat"];
                            $dlng = $stops[1]["plng"];
                        } else {
                            $slat = $stops[0]["plat"];
                            $slng = $stops[0]["plng"];
                        }
                    }
                }
            } else {
                $sql = $this->db->query(
                    "SELECT ts.id,ts.latitude,ts.longitude,ts.loc_name,ts.stop_id,ts.stop_type,sm.status_name,ts.status_code,ts.comment,convertToClientTZ(ts.createdon,?) as createdon,e.address,e.order_id,e.pickup,e.drop from tb_stop_status ts LEFT JOIN tb_status_master sm ON sm.id=ts.status_id LEFT JOIN tb_employee e ON e.shift_id = ts.shipment_id AND (e.stop_id=ts.stop_id OR e.drop_stopid=ts.stop_id) WHERE ts.shipment_id = ? GROUP BY ts.id ORDER BY ts.createdon ASC",
                    [$curtz, $shift_id]
                );
                $sql1 = $this->db->query(
                    "SELECT ts.id,ts.latitude,ts.longitude,ts.stop_id,ts.stop_type,dt.type_name,ts.createdby,convertToClientTZ(ts.createdon,?) as createdon,ts.imgpath,e.address,o.order_id,e.pickup,e.drop from tb_pod_uploads ts LEFT JOIN tb_document_types dt ON dt.id=ts.doc_type LEFT JOIN tb_employee e ON e.id = ts.stop_detail_id LEFT JOIN tb_orders o ON o.id = ts.order_id  WHERE ts.shipment_id = ? GROUP BY ts.id ORDER BY ts.createdon ASC",
                    [$curtz, $shift_id]
                );
            }
        }
        if ($sql1->num_rows() > 0) {
            $i = 1;
            foreach ($sql1->result_array() as $eachRow) {
                $base64DocumentData = getBase64DocumentData($eachRow['imgpath']);
                $pods[] = [
                    'sno' => $i,
                    'location' => @getLocationName($eachRow['latitude'], $eachRow['longitude']),
                    'type_name' => $eachRow['type_name'],
                    'driver_name' => @getDrivernameById($eachRow['createdby'])["name"],
                    'stop_id' => $eachRow['stop_id'],
                    'stop_type' => $eachRow['stop_type'],
                    'createdon' => date("d M,y h:i A", strtotime($eachRow['createdon'])),
                    'extention' => $base64DocumentData['extention'],
                    'file_content' => $base64DocumentData['file_content'],
                ];
                $i++;
            }
        }
        $history = $sql->num_rows() >0 ? $sql->result_array() : [];
        $data["history"] = $this->tripcreatefromorders->getCustomStatusForOrders($history,$shift_id, $curtz);
        $data["pods"] = $pods ?? [];
        $data["slat"] = @$slat;
        $data["slng"] = @$slng;
        $data["dlat"] = @$dlat;
        $data["dlng"] = @$dlng;

        return response()->json([
            'status' => 'success',
            'message' => 'Order history retrieved successfully.',
            'data' => $data
        ], 200);
    }
}
