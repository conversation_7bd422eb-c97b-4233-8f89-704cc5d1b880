<?php

namespace App\Http\Controllers;

use App\Services\OrderList\OrderProcessor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class ExcelImportOrdersController extends Controller
{
    protected $orderProcessor;

    public function __construct(OrderProcessor $orderProcessor)
    {
        $this->orderProcessor = $orderProcessor;
    }

    public function orderCost(Request $request)
    {
        try {
            $request->validate([
                'import_costfile' => 'required|file|mimes:xls,xlsx',
            ]);

            $user = Auth::user();
            $userId = $user->id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;
            $currency = $user->timezone['currency'] ?? config('app.currency', 'USD');

            $file = $request->file('import_costfile');
            $result = $this->orderProcessor->processOrderCostExcel($file, $userId, $orgId, $beValue, $currency);

            return response()->json([
                'status' => 'success',
                'message' => 'Charges uploaded successfully',
                'data' => $result
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('OrderCost Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('OrderCost Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error processing order costs',
                'data' => []
            ], 500);
        }
    }
}