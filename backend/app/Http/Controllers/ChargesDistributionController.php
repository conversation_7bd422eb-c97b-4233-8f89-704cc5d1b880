<?php

namespace App\Http\Controllers;


use App\Models\Charge;
use App\Models\ChargeCode;
use App\Models\Revenue;
use App\Models\SxPartyMembers;
use App\Models\VatCategory;
use App\Models\CountryMaster;
use App\Models\order;
use App\Models\CargoDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ChargesDistributionController extends Controller
{
    public function index(Request $request)
    {
        try {
            $userId = Auth::user()->id ?? 0;
            $orgId = Auth::user()->org_id ?? 0;
            $beValue = Auth::user()->be_value ?? 0;
            $sessionCurrency = Auth::user()->timezone['currency'] ?? 'USD';

            $data = [
                'vatcategory' => [],
                'chargecodes' => [],
                'currencies' => [],
                'session_currency' => $sessionCurrency,
            ];

            $currencies = CountryMaster::where('status', '1')
                ->pluck('currency')
                ->filter()
                ->values()
                ->toArray();
            $data['currencies'] = $currencies;

            $vatCategories = VatCategory::where([
                'org_id' => $orgId,
                'status' => '1'
            ])->select('id', 'description', 'vat_category', 'vat_percentage')
                ->get();

            foreach ($vatCategories as $res) {
                $desc = "{$res->description} ({$res->vat_category}-{$res->vat_percentage})";
                $data['vatcategory'][] = [
                    'val' => $res->id,
                    'desc' => $desc,
                ];
            }

            $chargeCodes = ChargeCode::where('status', '1')
                ->select('id', 'charge_code')
                ->get();

            foreach ($chargeCodes as $res) {
                $data['chargecodes'][] = [
                    'charge_id' => $res->id,
                    'charge_code' => $res->charge_code,
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Data retrieved successfully',
                'data' => $data,
            ], 200);
        } catch (\Exception $e) {
            Log::error('ChargesDistribution Index Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving data',
                'data' => [],
            ], 500);
        }
    }

    public function applyLogic(Request $request)
    {
        try {
            $post = $request->all();
            $userId = Auth::user()->id ?? 0;
            $orgId = Auth::user()->org_id ?? 0;
            $partyType = $post['party_type'] ?? '';
            $partyCode = $post['party_code'] ?? '';
            $amountDistribute = (float) ($post['amount_distribute'] ?? 0);
            $chargeCodeId = $post['charge_code'] ?? '';
            $distributionCurrency = $post['distribution_currency'] ?? '';
            $distributionBasement = $post['distribution_basement'] ?? '';
            $vatCategoryId = $post['vat_category'] ?? '';
            $orderIds = isset($post['order_ids']) ? explode(',', $post['order_ids']) : [];
            $status = 0;

            // Determine type based on party_type
            $type = $partyType === 'Customer' ? '0' : '1';

            // Get party name
            $partyName = SxPartyMembers::where([
                'code' => $partyCode,
                'org_id' => $orgId,
                'status' => '1'
            ])->value('name') ?? '';

            // Determine UOM based on distribution_basement
            $selectUom = match ($distributionBasement) {
                'QUANTITY' => 'order_id, SUM(quantity) as total_uom',
                'VOLUME' => 'order_id, SUM(second_volume) as total_uom',
                'WEIGHT' => 'order_id, SUM(second_weight) as total_uom',
                'ACTUAL VOLUME' => 'order_id, SUM(volume) as total_uom',
                'ACTUAL WEIGHT' => 'order_id, SUM(weight) as total_uom',
                'VOLUMETRIC WEIGHT' => 'order_id, SUM(volumetric_weight) as total_uom',
                'LDM' => 'order_id, SUM(ldm) as total_uom',
                default => '',
            };

            // Initialize orders array
            $orders = [];
            foreach ($orderIds as $orderId) {
                $orders[$orderId] = [
                    'order_id' => $orderId,
                    'order_uom' => 0,
                ];
            }

            // Get charge description
            $chargeDescription = ChargeCode::where('id', $chargeCodeId)
                ->value('description') ?? '';

            // Get VAT category details
            $vatDetails = VatCategory::where('id', $vatCategoryId)
                ->select('vat_category', 'vat_percentage')
                ->first();
            $vatCategory = $vatDetails->vat_category ?? '';
            $vatPercentage = $vatDetails->vat_percentage ?? 0;

            // Fetch cargo UOM or goods value
            $totalUom = 0;
            if ($selectUom && !empty($orderIds)) {
                $cargoUoms = CargoDetail::whereIn('order_id', $orderIds)
                    ->where('status', '1')
                    ->selectRaw($selectUom)
                    ->groupBy('order_id')
                    ->get();

                foreach ($cargoUoms as $cargo) {
                    $index = $cargo->order_id;
                    $orders[$index]['order_uom'] = (float) $cargo->total_uom;
                    $totalUom += (float) $cargo->total_uom;
                }
            } elseif ($distributionBasement === 'GOODS VALUE') {
                $goodsValues = Order::whereIn('id', $orderIds)
                    ->select('id', 'goods_value')
                    ->get();

                foreach ($goodsValues as $goods) {
                    $index = $goods->id;
                    $orders[$index]['order_uom'] = (float) $goods->goods_value;
                    $totalUom += (float) $goods->goods_value;
                }
            }

            // Default invoice dates
            $invoiceDate = '0000-00-00 00:00:00';
            $invCreatedDate = '0000-00-00 00:00:00';
            $invReceivedDate = '0000-00-00 00:00:00';

            // Distribute charges
            if (!empty($orderIds)) {
                foreach ($orderIds as $orderId) {
                    $eachOrderUom = $orders[$orderId]['order_uom'] ?? 0;
                    $vatAmount = 0;
                    $amountWithoutVat = 0;
                    $totalAmount = 0;

                    if ($totalUom > 0 && $eachOrderUom > 0 && $amountDistribute > 0) {
                        $amountWithoutVat = ($eachOrderUom / $totalUom) * $amountDistribute;
                        $amountWithoutVat = round($amountWithoutVat, 4);

                        if ($vatPercentage > 0 && $amountWithoutVat > 0) {
                            $vatAmount = ($amountWithoutVat * $vatPercentage) / 100;
                        }
                        $vatAmount = round($vatAmount, 4);
                        $totalAmount = $amountWithoutVat + $vatAmount;

                        // Create revenue
                        $revenue = Revenue::create([
                            'order_id' => $orderId,
                            'type' => $type,
                            'recipient_role' => $partyType,
                            'recipient_code' => $partyCode,
                            'recipient_name' => $partyName,
                            'invoice_date' => $invoiceDate,
                            'invoice_creation_date' => $invCreatedDate,
                            'invoice_receivdon_date' => $invReceivedDate,
                            'amount' => $totalAmount,
                            'currency' => $distributionCurrency,
                            'status' => '1',
                            'user_id' => $userId,
                            'org_id' => $orgId,
                            'exchange_rate' => 0,
                            'invoice_status' => '0',
                        ]);

                        // Create charge
                        if ($revenue->id) {
                            $charge = Charge::create([
                                'revenue_id' => $revenue->id,
                                'charge_code' => $chargeCodeId,
                                'description' => $chargeDescription,
                                'quantity_unit' => '1',
                                'value' => '1',
                                'rate_id' => '1',
                                'amount' => $amountWithoutVat,
                                'currency' => $distributionCurrency,
                                'local_amount' => $amountWithoutVat,
                                'local_currency' => $distributionCurrency,
                                'local_vat_amount' => $vatAmount,
                                'local_total_amount' => $totalAmount,
                                'vat_percentage' => $vatPercentage,
                                'vat_amount' => $vatAmount,
                                'total_amount' => $totalAmount,
                                'cat_id' => $vatCategoryId,
                                'cat_val' => $vatCategory,
                                'status' => '1',
                                'user_id' => $userId,
                                'org_id' => $orgId,
                                'source_created' => 'MI - Manual Input',
                            ]);

                            if ($charge->id) {
                                $status = 1;
                            }
                        }
                    }
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => $status ? 'Charges distributed successfully' : 'No charges distributed',
                'data' => ['status' => $status],
            ], 200);
        } catch (\Exception $e) {
            Log::error('ApplyLogic Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error distributing charges',
                'data' => ['status' => 0],
            ], 500);
        }
    }
}
