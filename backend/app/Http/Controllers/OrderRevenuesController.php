<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderReference;
use App\Models\SxPartyMembers;
use App\Models\User;
use App\Models\VatMaster;
use App\Models\Bill;
use App\Models\Charge;
use App\Models\Tender;
use App\Models\OrderVas;
use App\Models\VasMaster;
use App\Models\ChargeCode;
use App\Models\VatCategory;
use App\Models\Revenue;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

use App\Services\OrderList\OrderProcessor;
use App\Services\TripCreateFromOrders;
use App\Services\RateManagement;

use Carbon\Carbon;
use Exception;

class OrderRevenuesController extends Controller
{

    protected $rateManagement;
    protected $orderProcessor;
    protected $tripCreateFromOrders;

    public function __construct(OrderProcessor $orderProcessor, TripCreateFromOrders $tripCreateFromOrders, RateManagement $rateManagement)
    {
        $this->orderProcessor = $orderProcessor;
        $this->tripCreateFromOrders = $tripCreateFromOrders;
        $this->rateManagement = $rateManagement;
    }

    public function checkOrderExceededOrNot(Request $request)
    {
        $orderId = $request->input('order_id', '0');
        $creditUtilizedAmount = 0;
        $creditLimitAmount = 0;
        $holdType = 0;

        if ($orderId !== '0') {
            $order = Order::where('id', $orderId)
                ->select('user_id', 'customer_id', 'hold_type', 'credit_approval')
                ->first();

            if ($order) {
                $holdType = $order->hold_type;
                $userId = $order->user_id;
                $creditApproval = $order->credit_approval;

                if ($creditApproval == '0' && in_array($holdType, ['2', '3'])) {
                    $customerId = $order->customer_id;
                    if ($customerId && $customerId !== '0') {
                        $party = SxPartyMembers::where('id', $customerId)
                            ->select('credit_limit_amount', 'credit_utilized_amount')
                            ->first();

                        if ($party) {
                            $creditLimitAmount = $party->credit_limit_amount;
                            $creditUtilizedAmount = $party->credit_utilized_amount;
                        }
                    }

                    if ($creditLimitAmount > $creditUtilizedAmount) {
                        $holdType = '0';
                    }
                }
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Order credit check completed successfully',
            'data' => ['holdType' => $holdType]
        ], 200);
    }

    public function getOrderVasDetails(Request $request)
    {
        $user = Auth::user();
        $orgId = $user->org_id ?? $request->input('org_id', 0);
        $orderId = $request->input('order_id', '0');
        $result = [];

        if ($orderId !== '0' && $orderId !== '') {
            $vasRecords = OrderVas::where(['order_id' => $orderId, 'status' => 1])
                ->select('id', 'vas_id', 'quantity')
                ->get();

            if ($vasRecords->isNotEmpty()) {
                foreach ($vasRecords as $res) {
                    $vasId = $res->vas_id;
                    $vasName = $vasId;
                    $vasRowId = $vasId;

                    $vasDetails = VasMaster::where('id', $vasId)
                        ->when($orgId === 'RUKN', function ($query) use ($orgId) {
                            return $query->where('org_id', $orgId);
                        }, function ($query) use ($orgId) {
                            return $query->where('org_id', '!=', $orgId);
                        })
                        ->select('id', 'vas_id', 'vas_name')
                        ->first();

                    if ($vasDetails) {
                        $vasName = $vasDetails->vas_name;
                        $vasId = $vasDetails->vas_id;
                        $vasRowId = $vasDetails->id;
                    }

                    $vasNameQuoted = '"' . $vasName . '"';
                    $action = "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'> <a class='dropdown-toggle' data-toggle='dropdown' href='#''><span class='icon  tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" .
                        "<li><a id='bEdit' type='button' class='btn btn-sm btn-default'  onclick='editvasorderedetails(this," . $res->id . "," . $vasRowId . "," . $res->quantity . "," . $vasNameQuoted . ")'><span class='glyphicon glyphicon-pencil' ></span>Edit</a></li>" .
                        "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deletevasdetailsbyorder(" . $res->id . ")'><span class='glyphicon glyphicon-trash' > </span>Remove</a></li>" .
                        "<li><a id='bAcep' type='button' class='btn btn-sm btn-default' style='display:none;' onclick='rowAcep(this);'><span class='glyphicon glyphicon-ok' > </span>Update</a></li>" .
                        "<li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='vasrowadd(this);'><span class='glyphicon glyphicon-plus' > </span>Add Row</a></li>";

                    $result[] = [
                        'id' => $res->id,
                        'vasId' => $vasId,
                        'vasName' => $vasName,
                        'quantity' => $res->quantity,
                        'action' => $action
                    ];
                }
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'VAS details retrieved successfully',
            'data' => $result
        ], 200);
    }

    public function addVasOrderDetails(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'vas_order_id' => 'required|integer|min:1',
                'vas_id' => 'required|integer|min:1',
                'vas_quantity' => 'required|integer|min:1',
                'vasrow_id' => 'nullable|integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => ['resultCode' => 0]
                ], 422);
            }

            $user = Auth::user();
            $user_id = $user->id ?? $request->input('user_id', 0);
            if (!$user_id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User authentication required',
                    'data' => ['resultCode' => 0]
                ], 401);
            }

            $order_id = $request->input('vas_order_id');
            $vas_id = $request->input('vas_id');
            $quantity = $request->input('vas_quantity', 1);
            $row_id = $request->input('vasrow_id', 0) ?: 0;

            // Begin transaction
            DB::beginTransaction();

            if ($row_id == 0) {
                // Check for existing VAS record
                $existingVas = $this->orderProcessor->newGetTableRowData(
                    ['vas_id' => $vas_id, 'order_id' => $order_id, 'status' => 1],
                    ['id', 'quantity'],
                    OrderVas::class
                );

                if ($existingVas) {
                    $total_quantity = $quantity + $existingVas['quantity'];
                    $this->orderProcessor->updateTableData(
                        OrderVas::class,
                        ['quantity' => $total_quantity],
                        ['id' => $existingVas['id']]
                    );
                } else {
                    $this->orderProcessor->insertTableData(OrderVas::class, [
                        'order_id' => $order_id,
                        'vas_id' => $vas_id,
                        'quantity' => $quantity,
                        'user_id' => $user_id,
                        'status' => 1,
                        'createdon' => now()
                    ]);
                }
            } else {
                $this->orderProcessor->updateTableData(
                    OrderVas::class,
                    ['vas_id' => $vas_id, 'quantity' => $quantity],
                    ['id' => $row_id]
                );
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'VAS order details added successfully',
                'data' => ['resultCode' => 1]
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add VAS order details: ' . $e->getMessage(),
                'data' => ['resultCode' => 0]
            ], 500);
        }
    }

    public function deleteVasDetailsByOrder(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'vas_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => ['resultCode' => 0]
                ], 422);
            }

            $row_id = $request->input('vas_id');

            $orderVas = OrderVas::where('id', $row_id)->where('status', 1)->first();

            if (!$orderVas) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'VAS record not found or already inactive',
                    'data' => ['resultCode' => 0]
                ], 404);
            }

            $this->orderProcessor->updateTableData(
                OrderVas::class,
                ['status' => 0],
                ['id' => $row_id]
            );

            return response()->json([
                'status' => 'success',
                'message' => 'VAS details deleted successfully',
                'data' => ['resultCode' => 1]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete VAS details: ' . $e->getMessage(),
                'data' => ['resultCode' => 0]
            ], 500);
        }
    }

    public function getOrderRevenueParties(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'nullable|integer|min:1',
                'rev_ids' => 'nullable|array',
                'rev_ids.*' => 'integer|min:1',
                'type' => 'nullable|string|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $order_id = $request->input('order_id', 0);
            $rev_ids = $request->input('rev_ids', []);
            $type = $request->input('type', '');

            $revenues = [];

            if ($order_id && $order_id !== '0') {
                $revenues = $this->orderProcessor->getTableData(
                    ['order_id' => $order_id, 'status' => 1, 'type' => '0'],
                    ['*'],
                    Revenue::class
                );
            } elseif (!empty($rev_ids)) {
                $revenues = Revenue::whereIn('id', $rev_ids)->get()->toArray();
            }

            if (empty($revenues)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No revenue records found',
                    'data' => []
                ], 404);
            }

            $result = [];
            foreach ($revenues as $res) {
                $invoice_status = $res['invoice_status'];
                $bill_id = $res['bill_id'];
                $bill_status = 0;

                if ($bill_id > 0) {
                    $bill = Bill::where('id', $bill_id)->first();
                    $bill_status = $bill ? $bill->status : 0;
                }

                $filetransfer_button = '';
                if ($res['recipient_role'] === 'Internal BU' && $res['invoice_status'] == 1) {
                    $filetransfer_button = "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='sendfiletoacon({$res['id']},0)'><span class='glyphicon glyphicon-pencil'></span> Send File to ACON</a></li>";
                }

                $action_buttons = '';
                if ($bill_status == 0 || $bill_status == 1) {
                    if ($res['recipient_role'] !== 'Internal BU' || $res['invoice_status'] <= 1) {
                        $action_buttons = "<li><a id='bEdit' type='button' class='btn btn-sm btn-default' onclick='rowEditrevenue(this,{$res['id']})'><span class='glyphicon glyphicon-pencil'></span> Edit</a></li><li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deleteorderevenue({$res['id']})'><span class='glyphicon glyphicon-trash'></span> Remove</a></li><li><a id='bAcep' type='button' class='btn btn-sm btn-default' style='display:none;' onclick='rowAcep(this);'><span class='glyphicon glyphicon-ok'></span> Update</a></li>" . $filetransfer_button;
                    }
                }

                $action = "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'> <a class='dropdown-toggle' data-toggle='dropdown' href='#'><span class='icon tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" . $action_buttons . "<li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='revenuerowAdd(this);'><span class='glyphicon glyphicon-plus'></span> Add revenue</a></li><li><a id='innerpacking' type='button' class='btn btn-sm btn-default' title='Add charges' onclick='getchargesbyrevenue(this,{$res['id']})'><span class='fa fa-archive'></span> Get charges</a></li></ul>";

                $inv_status = '';
                if ($bill_status > 0) {
                    $inv_status = $bill_status == 1 ? 'Ready for Invoice' : 'Billed';
                } elseif (in_array($res['invoice_status'], [1, 2])) {
                    $inv_status = 'Ready for Invoice';
                }

                $invoice_creation_date = $res['invoice_creation_date'] && $res['invoice_creation_date'] !== '0000-00-00 00:00:00' ? $res['invoice_creation_date'] : '';
                $invoice_receivdon_date = $res['invoice_receivdon_date'] && $res['invoice_receivdon_date'] !== '0000-00-00 00:00:00' ? $res['invoice_receivdon_date'] : '';
                $invoice_date = $res['invoice_date'] && $res['invoice_date'] !== '0000-00-00 00:00:00' ? $res['invoice_date'] : '';
                $bu_jfr = $res['bu_jfr'] == 0 ? '' : $res['bu_jfr'];

                $result[] = [
                    'id' => $res['id'],
                    'orderId' => $res['order_id'],
                    'recipientRole' => $res['recipient_role'],
                    'recipientCode' => $res['recipient_code'],
                    'recipientName' => $res['recipient_name'],
                    'debtorJfr' => $res['debtor_jfr'],
                    'amount' => $res['amount'],
                    'currency' => $res['currency'],
                    'foreignCurrency' => $res['foreign_currency'],
                    'exchangeRate' => $res['exchange_rate'],
                    'invoiceNumber' => $res['invoice_number'],
                    'creditNoteNumber' => $res['credit_note_number'],
                    'invoiceDate' => $invoice_date,
                    'invoiceCreationDate' => $invoice_creation_date,
                    'invoiceReceivdonDate' => $invoice_receivdon_date,
                    'invoiceStatus' => $inv_status,
                    'action' => $action,
                    'accrualAmount' => $res['accrual_amount'],
                    'actualAmount' => $res['actual_amount'],
                    'foreignAccrualAmount' => $res['accrual_foreign_amount'],
                    'buJfr' => $bu_jfr
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Revenue parties retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve revenue parties: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function getCostByOrder(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'nullable|integer|min:1',
                'cost_ids' => 'nullable|array',
                'cost_ids.*' => 'integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $order_id = $request->input('order_id', 0);
            $cost_ids = $request->input('cost_ids', []);

            $revenues = [];

            if ($order_id && $order_id !== '0') {
                $revenues = $this->orderProcessor->getTableData(
                    ['order_id' => $order_id, 'status' => 1, 'type' => '1'],
                    ['*'],
                    Revenue::class
                );
            } elseif (!empty($cost_ids)) {
                $revenues = Revenue::whereIn('id', $cost_ids)->get()->toArray();
            }

            if (empty($revenues)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No cost records found',
                    'data' => []
                ], 404);
            }

            $result = [];
            foreach ($revenues as $res) {
                $invoice_status = $res['invoice_status'];
                $bill_id = $res['bill_id'];
                $bill_status = 0;

                if ($bill_id > 0) {
                    $bill = $this->orderProcessor->newGetTableRowData(
                        ['id' => $bill_id],
                        ['status'],
                        Bill::class
                    );
                    $bill_status = $bill['status'] ?? 0;
                }

                $accrual_button = '';
                if ($invoice_status == 1) {
                    $accrual_button = $res['recipient_role'] === 'Internal BU'
                        ? "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='sendfiletoacon({$res['id']},0)'><span class='glyphicon glyphicon-pencil'></span> Send File to ACON</a></li>"
                        : "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='sendfiletoacon({$res['id']},1)'><span class='glyphicon glyphicon-pencil'></span> Send Accrual</a></li>";
                }

                $action_buttons = '';
                if ($invoice_status != 2) {
                    if ($res['recipient_role'] !== 'Internal BU' || $invoice_status <= 1) {
                        $action_buttons = "<li><a id='bEdit' type='button' class='btn btn-sm btn-default' onclick='rowEditcost(this,{$res['id']})'><span class='glyphicon glyphicon-pencil'></span> Edit</a></li><li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deleteordecost({$res['id']})'><span class='glyphicon glyphicon-trash'></span> Remove</a></li><li><a id='bAcep' type='button' class='btn btn-sm btn-default' style='display:none;' onclick='rowAcep(this);'><span class='glyphicon glyphicon-ok'></span> Update</a></li>" . $accrual_button;
                    }
                }

                $action = "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'> <a class='dropdown-toggle' data-toggle='dropdown' href='#'><span class='icon tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" . $action_buttons . "<li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='costrowAdd(this);'><span class='glyphicon glyphicon-plus'></span> Add Cost Details</a></li><li><a id='innerpacking' type='button' class='btn btn-sm btn-default' title='Add charges' onclick='getchargesbyrevenue(this,{$res['id']})'><span class='fa fa-archive'></span> Get charges</a></li></ul></li></ul>";

                $inv_status = '';
                if ($bill_status > 0) {
                    $inv_status = $bill_status == 1 ? 'Ready for Invoice' : 'Invoiced';
                } elseif (in_array($res['invoice_status'], [1, 2])) {
                    $inv_status = $res['invoice_status'] == 1 ? 'Ready for Invoice' : 'Invoiced';
                }

                $invoice_creation_date = $res['invoice_creation_date'] && $res['invoice_creation_date'] !== '0000-00-00 00:00:00' ? $res['invoice_creation_date'] : '';
                $invoice_receivdon_date = $res['invoice_receivdon_date'] && $res['invoice_receivdon_date'] !== '0000-00-00 00:00:00' ? $res['invoice_receivdon_date'] : '';
                $invoice_date = $res['invoice_date'] && $res['invoice_date'] !== '0000-00-00 00:00:00' ? $res['invoice_date'] : '';
                $bu_jfr = empty($res['bu_jfr']) || $res['bu_jfr'] == 0 ? '' : $res['bu_jfr'];

                $result[] = [
                    'id' => $res['id'],
                    'orderId' => $res['order_id'],
                    'recipientRole' => $res['recipient_role'],
                    'recipientCode' => $res['recipient_code'],
                    'recipientName' => $res['recipient_name'],
                    'debtorJfr' => $res['debtor_jfr'],
                    'amount' => $res['amount'],
                    'currency' => $res['currency'],
                    'foreignCurrency' => $res['foreign_currency'],
                    'exchangeRate' => $res['exchange_rate'],
                    'invoiceNumber' => $res['invoice_number'],
                    'creditNoteNumber' => $res['credit_note_number'],
                    'invoiceDate' => $invoice_date,
                    'invoiceCreationDate' => $invoice_creation_date,
                    'invoiceReceivdonDate' => $invoice_receivdon_date,
                    'invoiceStatus' => $inv_status,
                    'action' => $action,
                    'accrualAmount' => $res['accrual_amount'],
                    'actualAmount' => $res['actual_amount'],
                    'foreignAccrualAmount' => $res['accrual_foreign_amount'],
                    'buJfr' => $bu_jfr
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Cost records retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve cost records: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function getAllCostRevenues(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'nullable|integer|min:1',
                'rev_id' => 'nullable|string',
                'cost_id' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $user = Auth::user();
            $org_id = $user->org_id ?? $request->input('org_id', 0);
            $order_id = $request->input('order_id', 0);
            $rev_id = $request->input('rev_id', '');
            $cost_id = $request->input('cost_id', '');

            $revenues = [];
            $revenue_currency = '';
            $total_revenue = 0;
            $total_breakup_rev = 0;

            if ($order_id && $order_id !== '0') {
                $revenues = $this->orderProcessor->getTableData(
                    ['order_id' => $order_id, 'status' => 1, 'type' => '0'],
                    ['id', 'recipient_role', 'recipient_name', 'amount', 'currency'],
                    Revenue::class
                );
            } elseif ($rev_id) {
                $rev_row_ids = array_filter(explode(',', $rev_id), 'is_numeric');
                if (!empty($rev_row_ids)) {
                    $revenues = Revenue::whereIn('id', $rev_row_ids)
                        ->where('status', 1)
                        ->get(['id', 'recipient_role', 'recipient_name', 'amount', 'currency'])
                        ->toArray();
                }
            }

            $revenues_list = $revenues_list_html = [];
            if (!empty($revenues)) {
                foreach ($revenues as $res) {
                    $revenue_currency = $res['currency'];
                    $total_charge = 0;

                    if ($org_id === 'RUKN') {
                        $charge = $this->orderProcessor->newGetTableRowData(
                            ['revenue_id' => $res['id'], 'status' => 1],
                            [DB::raw('SUM(amount) as amount')],
                            Charge::class
                        );
                        $total_charge = $charge['amount'] ?? 0;
                        $total_revenue += $total_charge;
                    } else {
                        $total_revenue += $res['amount'];
                    }

                    $total_breakup_rev += $res['amount'];

                    $revenues_list[] = [
                        'id' => $res['id'],
                        'recipientRole' => $res['recipient_role'],
                        'name' => $res['recipient_name'],
                        'amount' => $res['amount'],
                        'currency' => $res['currency']
                    ];

                    $revenues_list_html[] = "<li class='revenuelist-modal' id='revenue_{$res['id']}' onclick='getcostlistbyrevenue({$res['id']})' data-modalnav='{$res['currency']}'><label>{$res['recipient_name']}</label> <span class='amount'>" . number_format((float)$res['amount'], 2) . " {$res['currency']}</span></li>";
                }
            }

            $costs = [];
            $cost_currency = '';
            $total_cost = 0;
            $total_breakup_cost = 0;

            if ($order_id && $order_id !== '0') {
                $costs = $this->orderProcessor->getTableData(
                    ['order_id' => $order_id, 'status' => 1, 'type' => '1'],
                    ['id', 'recipient_role', 'recipient_name', 'amount', 'currency'],
                    Revenue::class
                );
            } elseif ($cost_id) {
                $cost_row_ids = array_filter(explode(',', $cost_id), 'is_numeric');
                if (!empty($cost_row_ids)) {
                    $costs = Revenue::whereIn('id', $cost_row_ids)
                        ->where('status', 1)
                        ->get(['id', 'recipient_role', 'recipient_name', 'amount', 'currency'])
                        ->toArray();
                }
            }

            $costs_list_html = [];
            if (!empty($costs)) {
                foreach ($costs as $res) {
                    $amount = $res['amount'] ?? 0.00;
                    $cost_currency = $res['currency'];
                    $total_charge_cost = 0;

                    if ($org_id === 'RUKN') {
                        $charge = $this->orderProcessor->newGetTableRowData(
                            ['revenue_id' => $res['id'], 'status' => 1],
                            [DB::raw('SUM(amount) as amount')],
                            Charge::class
                        );
                        $total_charge_cost = $charge['amount'] ?? 0;
                        $total_cost += $total_charge_cost;
                    } else {
                        $total_cost += $amount;
                    }

                    $total_breakup_cost += $amount;

                    $costs_list_html[] = "<li class='costlist-modal' id='cost_{$res['id']}' onclick='getcostlistbycost({$res['id']})' data-modalnav='dtdc'><label>{$res['recipient_name']}</label> <span class='amount'>" . number_format((float)$amount, 2) . " {$res['currency']}</span></li>";
                }
            }

            $profit = 0;
            if ($total_revenue > 0) {
                $profit = $total_revenue - $total_cost;
                $profit = $org_id === 'VNKN' ? round($profit) : round($profit, 2);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Cost and revenue records retrieved successfully',
                'data' => [
                    'revenues' => $revenues_list,
                    'revenueCurrency' => $revenue_currency,
                    'totalRevenue' => $total_revenue,
                    'revenuesList' => $revenues_list_html,
                    'costs' => $costs_list_html,
                    'costCurrency' => $cost_currency,
                    'totalCost' => $total_cost,
                    'totalBreakupRev' => $total_breakup_rev,
                    'totalBreakupCost' => $total_breakup_cost,
                    'profit' => $profit
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve cost and revenue records: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function getAllChargesById(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $user = Auth::user();
            $org_id = $user->org_id ?? $request->input('org_id', 0);
            $id = $request->input('id');

            $charges = $this->orderProcessor->getTableData(
                ['revenue_id' => $id, 'status' => 1],
                ['description', 'amount', 'currency', 'vat_percentage', 'vat_amount'],
                Charge::class
            );

            $list = [];
            if (!empty($charges)) {
                foreach ($charges as $res) {
                    if ($res['amount'] > 0) {
                        $list[] = "<li><label>" . htmlspecialchars($res['description']) . "</label> <span class='amount'>" . number_format((float)$res['amount'], 2) . " {$res['currency']}</span></li>";
                    }
                    if ($res['vat_percentage'] > 0 && $res['vat_amount'] > 0) {
                        $vat_name = $org_id === 'SGKN' ? 'GST' : 'VAT';
                        $list[] = "<li><label>{$vat_name} {$res['vat_percentage']}%</label> <span class='amount'>" . number_format((float)$res['vat_amount'], 2) . " {$res['currency']}</span></li>";
                    }
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Charges retrieved successfully',
                'data' => $list
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve charges: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function getBuJfrFromPq(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => ''
                ], 422);
            }

            $order_id = $request->input('order_id');

            $ref_value = '';
            $reference = $this->orderProcessor->newGetTableRowData(
                ['order_id' => $order_id, 'reference_id' => 'PQ', 'status' => 1],
                ['ref_value'],
                OrderReference::class
            );

            if (!empty($reference)) {
                $ref_value = $reference['ref_value'] ?? '';
            }

            return response()->json([
                'status' => 'success',
                'message' => 'BU JFR retrieved successfully',
                'data' => $ref_value
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve BU JFR: ' . $e->getMessage(),
                'data' => ''
            ], 500);
        }
    }

    public function viewRoleTypeList(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'type' => 'required|string|in:Customer,Carrier,Overseas OL,Internal BU,tender',
                'screen_type' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => []
                ], 422);
            }

            $user = Auth::user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized: User not authenticated',
            //         'data' => []
            //     ], 401);
            // }

            $type = $request->input('type') === 'Vendor' ? 'Carrier' : $request->input('type');
            $screen_type = $request->input('screen_type', '');
            $org_id = $user->org_id ?? $request->input('org_id', 0);

            $data = [];

            if ($type === 'tender') {
                $tenders = $this->getTenderData();
                foreach ($tenders as $res) {
                    $check = "<input class='rolelist-tender' type='radio' name='selectrole' id='rolelist_{$res['id']}' value='{$res['id']}' onchange='selectrolebytenderid({$res['id']})'>";
                    $data[] = [
                        'check' => $check,
                        'id' => $res['id'],
                        'code' => $res['tender_id'],
                        'name' => $res['name'],
                        'emailId' => $res['emailid'],
                        'orgId' => $res['org_id'],
                        'beValue' => $res['be_value'],
                        'country' => $res['country_code'],
                        'street' => '',
                        'city' => ''
                    ];
                }
            } else {
                $query = SxPartyMemberS::select([
                    'sx_party_members.id',
                    'sx_party_members.name',
                    'sx_party_members.email',
                    'sx_party_members.code',
                    'sx_party_members.org_id',
                    'sx_party_members.be_value',
                    'sx_party_members.country',
                    'sx_party_members.location_id',
                    'sx_party_members.street'
                ])
                    ->leftJoin('sx_party_types', 'sx_party_types.id', '=', 'sx_party_members.party_type')
                    ->whereNotNull('sx_party_members.acon_debitor_code')
                    ->where('sx_party_members.org_id', $org_id);

                if ($type === 'Customer') {
                    /* $query->selectRaw('sx_party_members.id AS customer_id')
                        ->join('sx_party_members', function ($join) use ($org_id) {
                            $join->on('sx_party_members.partner_id', '=', 'sx_party_members.id')
                                ->where('sx_party_members.org_id', '=', $org_id);
                        }); */
                    $query->selectRaw('sx_party_members.id AS customer_id')
                        ->where('sx_party_members.category_type', 'Customer');
                } elseif ($type === 'Overseas OL') {
                    $query->where('sx_party_members.category_type', 'Overseas OL');
                } elseif ($type === 'Internal BU') {
                    $query->where('sx_party_members.category_type', 'KN Office');
                } else {
                    $query->where('sx_party_types.type_name', 'LIKE', "%{$type}%");
                }

                $roles = $query->get()->toArray();

                foreach ($roles as $res) {
                    $check = '';
                    if ($screen_type !== 'charges_distribution') {
                        if ($type === 'Customer') {
                            $check = "<input class='rolelist' type='radio' name='selectrole' id='rolelist_{$res['id']}' value='{$res['customer_id']}' onchange='selectrolebyid({$res['id']})'>";
                        } elseif ($type === 'Carrier') {
                            $check = "<input class='vendorlist' type='radio' name='selectvendor' id='vendorlist_{$res['id']}' value='{$res['code']}' onchange='selectvendorbyid({$res['id']})'>";
                        } elseif (in_array($type, ['Overseas OL', 'Internal BU'])) {
                            $check = "<input class='twopartieslist' type='radio' name='selectparties' id='twopartieslist_{$res['id']}' value='{$res['code']}' onchange='selectpartiesbyid({$res['id']})'>";
                        }
                    } else {
                        if (in_array($type, ['Customer', 'Carrier'])) {
                            $value = $type === 'Customer' ? $res['customer_id'] : $res['code'];
                            $check = "<input class='rolelist' type='radio' name='selectrole' id='rolelist_{$res['id']}' value='{$value}' onchange='selectrolecode({$res['id']})'>";
                        }
                    }

                    $data[] = [
                        'check' => $check,
                        'id' => $res['id'],
                        'code' => $res['code'],
                        'name' => $res['name'],
                        'emailId' => $res['email'],
                        'orgId' => $res['org_id'],
                        'beValue' => $res['be_value'],
                        'country' => $res['country'],
                        'street' => $res['street'],
                        'city' => $res['location_id']
                    ];
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Role type list retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve role type list: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    protected function getTenderData($id = null)
    {
        $query = Tender::select([
            'tenders.id',
            'tenders.tender_id',
            'tenders.booking_id',
            'tenders.tender_type',
            'tenders.tender_status',
            'tenders.org_id',
            'tenders.be_value',
            'users.emailid',
            'users.name',
            'users.country_code',
            'tenders.pickup'
        ])
            ->leftJoin('users', 'tenders.user_id', '=', 'users.id')
            ->where('tenders.org_id', Auth::user()->org_id ?? 0)
            ->where('tenders.status', 1)
            ->where('users.status', 'Active')
            ->whereNotIn('tenders.tender_status', ['Awarded'])
            ->orderBy('tenders.id', 'DESC');

        if ($id) {
            $query->where('tenders.id', $id);
            return $query->first() ? $query->first()->toArray() : [];
        }

        return $query->get()->toArray();
    }

    public function saveRevenue(Request $request, $revenue_id = null)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'revenue_orderid' => 'nullable|integer|min:0',
                'roletype_rebenue' => 'required|string|max:100',
                'revenue_bujfr' => 'nullable|string|max:100',
                'revenue_codeid' => 'required|string|max:100',
                'revenue_name' => 'required|string|max:100',
                'revenue_debtor' => 'nullable|string|max:100',
                'invoice_number' => 'nullable|string|max:100',
                'credit_note_number' => 'nullable|string|max:100',
                'invoice_date' => 'nullable|date_format:Y-m-d',
                'inv_createddate' => 'nullable|date_format:Y-m-d',
                'inv_receiveddate' => 'nullable|date_format:Y-m-d',
                'revenue_amount' => 'nullable|numeric|min:0',
                'revenue_currency' => 'nullable|string|max:10',
                'revenue_foreigncurcy' => 'nullable|string|max:10',
                'exchange_rate' => 'nullable|numeric|min:0',
                'invoice_status' => 'nullable|integer|in:0,1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $user = Auth::user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized: User not authenticated',
            //         'data' => null
            //     ], 401);
            // }

            $order_id = $request->input('revenue_orderid', 0);
            $role = $request->input('roletype_rebenue');
            $bu_jfr = $request->input('revenue_bujfr', '0');
            $code = $request->input('revenue_codeid');
            $name = $request->input('revenue_name');
            $debtor_jfr = $request->input('revenue_debtor', '');
            $invoice_number = $request->input('invoice_number', '');
            $credit_note_number = $request->input('credit_note_number', '');
            $invoice_date = $request->input('invoice_date') ? Carbon::createFromFormat('Y-m-d', $request->input('invoice_date'))->startOfDay() : null;
            $inv_createddate = $request->input('inv_createddate') ? Carbon::createFromFormat('Y-m-d', $request->input('inv_createddate'))->startOfDay() : null;
            $inv_receiveddate = $request->input('inv_receiveddate') ? Carbon::createFromFormat('Y-m-d', $request->input('inv_receiveddate'))->startOfDay() : null;
            $amount = $request->input('revenue_amount', 0.0000);
            $currency = $request->input('revenue_currency', '');
            $foreign_currency = $request->input('revenue_foreigncurcy', '');
            $exchange_rate = $request->input('exchange_rate', 0);
            $invoice_status = $request->input('invoice_status', 0);
            $user_id = $user->id;
            $org_id = $user->org_id ?? $request->input('org_id', '');

            $charge_amount = round($amount, 4);

            $revenue_data = [
                'order_id' => $order_id,
                'type' => '0',
                'recipient_role' => $role,
                'recipient_code' => $code,
                'recipient_name' => $name,
                'amount' => $charge_amount,
                'currency' => $currency,
                'foreign_currency' => $foreign_currency,
                'exchange_rate' => $exchange_rate,
                'invoice_number' => $invoice_number,
                'credit_note_number' => $credit_note_number,
                'invoice_date' => $invoice_date,
                'invoice_creation_date' => $inv_createddate,
                'invoice_receivdon_date' => $inv_receiveddate,
                'invoice_status' => $invoice_status,
                'status' => 1,
                'user_id' => $user_id,
                'bu_jfr' => $bu_jfr,
                'updated_at' => now()
            ];

            if (!$revenue_id) {
                $revenue_data['created_at'] = now();
                $revenue_id = $this->orderProcessor->insertTableData(Revenue::class, $revenue_data);
            } else {
                $existing_invoice_status = $this->orderProcessor->newGetTableRowData(
                    ['id' => $revenue_id],
                    ['invoice_status'],
                    Revenue::class
                )['invoice_status'] ?? 0;

                $this->orderProcessor->updateTableData(Revenue::class, $revenue_data, ['id' => $revenue_id]);

                if ($existing_invoice_status == 0 && $invoice_status == 1) {
                    $debtor_jfr = $this->getDebitorCodeRevenue($order_id, $revenue_id);
                }

                if ($debtor_jfr) {
                    $this->orderProcessor->updateTableData(Revenue::class, [
                        'invoice_status' => $invoice_status,
                        'debtor_jfr' => $debtor_jfr
                    ], ['id' => $revenue_id]);
                }
            }

            if ($revenue_id && $revenue_id != 0) {
                $this->checkOrderSingleRevenueCurrency($revenue_id);
                $this->getTotalAmountOfRevenue($revenue_id);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Revenue saved successfully',
                'data' => $order_id != 0 ? $order_id : $revenue_id
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to save revenue: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    private function getDebitorCodeRevenue($order_id, $revenue_id)
    {
        $debtor_jfr = '';
        $user = Auth::user();
        $user_id = $user->id;
        $org_id = $user->org_id ?? $this->request->input('org_id', '');
        $exchange_rate = $this->checkExchangeRateExistsOrNot($order_id, $revenue_id);

        if ($exchange_rate > 0 && $order_id) {
            $user_data = $this->orderProcessor->newnewGetTableRowData(
                ['id' => $user_id],
                ['country_code', 'org_id'],
                User::class
            );

            $country_code = $user_data['country_code'] ?? '';
            $org_id = $user_data['org_id'] ?? $org_id;
            $cdate = now()->toDateTimeString();
            $user_timezone = $user->timezone ?? 'UTC';
            $cdate = Carbon::createFromFormat('Y-m-d H:i:s', $cdate, $user_timezone)->setTimezone('UTC')->toDateTimeString();

            $order_data = $this->orderProcessor->newnewGetTableRowData(
                ['id' => $order_id],
                ['shift_id', 'trip_id'],
                Order::class
            );

            $trip_id = $order_data['trip_id'] ?? 0;
            $shift_id = $order_data['shift_id'] ?? 0;

            // if ($this->checkAccessConditions('RELATED_2_SG_MY', $org_id)) {
            //     $info = [
            //         'country_code' => $country_code,
            //         'user_id' => $user_id,
            //         'revenue_id' => $revenue_id,
            //         'order_id' => $order_id,
            //         'trip_id' => $shift_id,
            //         'sts_createdon' => $cdate
            //     ];
            //     $debtor_jfr = $this->generateDebtorJfrCodeSgmyOrders($info);
            // } else {
            $info = [
                'country_code' => $country_code,
                'user_id' => $user_id,
                'revenue_id' => $revenue_id,
                'order_id' => $order_id,
                'trip_id' => $trip_id,
                'sts_createdon' => $cdate
            ];
            if ($trip_id > 0) {
                $debtor_jfr = now()->format('YmdHis');
            }
            // }
        }

        return $debtor_jfr;
    }

    private function checkExchangeRateExistsOrNot($order_id, $rev_id)
    {
        $exchange_rate = 0;
        $org_id = Auth::user()->org_id ?? $this->request->input('org_id', '');
        $user_currency = Auth::user()->currency ?? $this->request->input('currency', '');

        if ($rev_id > 0) {
            $info = [
                'org_id' => $org_id,
                'user_currency' => $user_currency,
                'order_id' => $order_id,
                'rev_id' => $rev_id
            ];
            $exchange_rate = $this->checkOtherCurrencyExistsOrNot($info);
        }

        return $exchange_rate;
    }

    private function checkOtherCurrencyExistsOrNot(array $info)
    {
        $exchange_rate = 1;
        $rev_id = $info['rev_id'] ?? 0;
        $org_id = $info['org_id'] ?? Auth::user()->org_id ?? '';
        $user_currency = $info['user_currency'] ?? '';

        if ($rev_id > 0) {
            $revenue = $this->orderProcessor->newnewGetTableRowData(
                ['id' => $rev_id, 'status' => 1],
                ['id', 'exchange_rate', 'foreign_currency'],
                Revenue::class
            );

            if ($revenue) {
                $exchange_rate = $revenue['exchange_rate'] ?? 1;
                $foreign_currency = $revenue['foreign_currency'] ?? '';

                if (!$foreign_currency) {
                    $exchange_rate = 1;
                } elseif (!$exchange_rate) {
                    $exchange_rate = 0;
                }
            }
        }

        return $exchange_rate;
    }

    private function checkOrderSingleRevenueCurrency($revenue_id)
    {
        $user = Auth::user();
        $org_id = $user->org_id ?? $this->request->input('org_id', '');
        $user_currency = $user->currency ?? $this->request->input('currency', '');

        $revenue = $this->orderProcessor->newnewGetTableRowData(
            ['id' => $revenue_id, 'invoice_status' => 0, 'status' => 1],
            ['id', 'exchange_rate', 'foreign_currency'],
            Revenue::class
        );

        if ($revenue) {
            $foreign_currency = $revenue['foreign_currency'] ?? '';
            $exchange_amount = $revenue['exchange_rate'] ?? 0;

            $charges = Charge::where('revenue_id', $revenue_id)
                ->where('status', 1)
                ->where('currency', '!=', $org_id === 'PLKN' ? 'PLN' : $user_currency)
                ->get(['id', 'amount', 'currency']);

            foreach ($charges as $charge) {
                $charge_row_id = $charge->id;
                $charge_amount = $charge->amount;
                $charge_currency = $charge->currency;

                if ($foreign_currency === $charge_currency && $exchange_amount && $exchange_amount !== '0') {
                    $user_amount = $charge_amount * $exchange_amount;
                    $charge_amount = $org_id === 'VNKN' ? round($user_amount) : round($user_amount, 2);

                    $this->orderProcessor->updateTableData(Charge::class, [
                        'local_amount' => $charge_amount,
                        'local_currency' => $org_id === 'PLKN' ? 'PLN' : $user_currency
                    ], ['id' => $charge_row_id]);

                    $this->updateTotalAmountForRev($revenue_id);
                }
            }
        }
    }

    private function updateTotalAmountForRev($revenue_id)
    {
        $user = Auth::user();
        $session_currency = $user->currency ?? 'USD';
        $org_id = $user->org_id ?? 0;

        $total_amount = 0;
        if ($revenue_id) {
            $charges = $this->orderProcessor->getTableData(
                ['revenue_id' => $revenue_id, 'status' => 1],
                ['amount', 'currency', 'local_amount', 'local_currency', 'vat_percentage', 'vat_amount', 'local_vat_amount'],
                Charge::class
            );

            foreach ($charges as $res) {
                $charge_amount = $res['amount'];
                $vat_amount = $res['vat_amount'];
                if ($res['currency'] !== $session_currency) {
                    if (is_numeric($res['local_amount']) && $res['local_amount'] > 0) {
                        $charge_amount = $res['local_amount'];
                    }
                    if (is_numeric($res['local_vat_amount']) && $res['local_vat_amount'] > 0) {
                        $vat_amount = $res['local_vat_amount'];
                    }
                }
                if ($res['vat_percentage'] && $vat_amount > 0) {
                    $total_amount += $vat_amount;
                }
                $total_amount += $charge_amount;
            }

            $revenue_data = $this->orderProcessor->newGetTableRowData(
                ['id' => $revenue_id],
                ['amount', 'currency', 'exchange_rate', 'foreign_currency'],
                Revenue::class
            );

            if (!empty($revenue_data)) {
                $charge_amount = round($total_amount, 4);
                if ($org_id === 'VNKN' && (!$revenue_data['foreign_currency'] || $revenue_data['exchange_rate'] > 0)) {
                    $charge_amount = round($total_amount);
                }

                $update_data = [
                    'amount' => $charge_amount,
                    'currency' => $session_currency
                ];
                if ($org_id === 'PLKN') {
                    $update_data['foreign_currency'] = 'EUR';
                }

                Revenue::where('id', $revenue_id)->update($update_data);
            }
        }
    }

    private function getTotalAmountOfRevenue($revenue_id)
    {
        $total_amount = 0;
        $user = Auth::user();
        $session_currency = $user->currency ?? 'USD';
        $org_id = $user->org_id ?? 0;

        $revenue_data = $this->orderProcessor->newGetTableRowData(
            ['id' => $revenue_id],
            ['order_id', 'exchange_rate'],
            Revenue::class
        );
        $order_id = $revenue_data['order_id'] ?? 0;
        $exchange_rate = $revenue_data['exchange_rate'] ?? 0;

        $foreign_currency = Charge::where('revenue_id', $revenue_id)
            ->where('currency', '!=', $session_currency)
            ->value('currency') ?? '';

        if ($foreign_currency) {
            $info = [
                'exchange_rate_id' => 0,
                'from_currency' => $foreign_currency,
                'to_currency' => $session_currency,
                'user_id' => $user->id,
                'order_id' => $order_id,
                'revenue_id' => $revenue_id
            ];
            $exchange_rate_amount = $exchange_rate > 0 ? $exchange_rate : $this->orderProcessor->getExchangeAmountFromCurrency($info);

            if ($exchange_rate_amount > 0) {
                $this->updateLocalAmountByExchangeRate($revenue_id, $exchange_rate_amount, $foreign_currency);
            } else {
                Revenue::where('id', $revenue_id)->update(['foreign_currency' => $foreign_currency]);
            }
        }

        $this->updateTotalAmountForRev($revenue_id);
        $this->updateUtilizedAmountForOrder($order_id);

        return $total_amount;
    }

    private function updateLocalAmountByExchangeRate($revenue_id, $exchange_rate_amount, $foreign_currency)
    {
        $user = Auth::user();
        $local_currency = $user->currency ?? 'USD';
        $org_id = $user->org_id ?? '';

        if ($revenue_id > 0) {
            Revenue::where('id', $revenue_id)->update([
                'exchange_rate' => $exchange_rate_amount,
                'foreign_currency' => $foreign_currency
            ]);

            if ($foreign_currency) {
                $charges = $this->orderProcessor->getTableData(
                    ['revenue_id' => $revenue_id, 'currency' => $foreign_currency, 'status' => 1],
                    ['id', 'amount', 'local_amount', 'vat_percentage', 'vat_amount'],
                    Charge::class
                );

                foreach ($charges as $charge) {
                    $update_data = [];
                    if ($charge['local_amount'] <= 0) {
                        $final_local_amount = $charge['amount'] * $exchange_rate_amount;
                        $update_data['local_amount'] = $org_id === 'VNKN' ? round($final_local_amount) : $final_local_amount;
                        $update_data['local_currency'] = $local_currency;

                        $local_vat_amount = 0;
                        if ($charge['vat_percentage'] > 0 && $charge['vat_amount'] > 0) {
                            $local_vat_amount = $charge['vat_amount'] * $exchange_rate_amount;
                            $update_data['local_vat_amount'] = $local_vat_amount;
                        }
                        $update_data['local_total_amount'] = $org_id === 'VNKN' ? round($local_vat_amount + $update_data['local_amount']) : $local_vat_amount + $update_data['local_amount'];

                        if (!empty($update_data)) {
                            Charge::where('id', $charge['id'])->update($update_data);
                        }
                    }
                }
            }
        }
    }

    private function updateUtilizedAmountForOrder($order_id)
    {
        $user = Auth::user();
        $session_currency = $user->currency ?? 'USD';
        $user_id = $user->id;
        $org_id = $user->org_id ?? '';
        $be_value = $user->be_value ?? '';

        $credit_limit = 0;
        $credit_limit_amount = 0;
        $credit_utilized_amount = 0;
        $total_amount = 0;
        $party_master_id = 0;

        if ($order_id) {
            $order_data = $this->orderProcessor->newGetTableRowData(
                ['id' => $order_id],
                ['customer_id', 'user_id'],
                Order::class
            );

            if (!empty($order_data)) {
                $customer_id = $order_data['customer_id'] ?? 0;
                if ($customer_id) {
                    $party_data = SxPartyMembers::join('customers', function ($join) use ($user_id) {
                        $join->on('customers.code', '=', 'sx_party_members.code')
                            ->where('customers.user_id', '=', $user_id);
                    })
                        ->where('customers.id', $customer_id)
                        ->where('sx_party_members.user_id', $user_id)
                        ->select([
                            'sx_party_members.id',
                            'sx_party_members.credit_limit',
                            'sx_party_members.credit_limit_amount',
                            'sx_party_members.credit_currency',
                            'sx_party_members.credit_utilized_amount'
                        ])
                        ->first();

                    if ($party_data) {
                        $party_master_id = $party_data->id;
                        $credit_limit = $party_data->credit_limit;
                        $credit_limit_amount = $party_data->credit_limit_amount;
                        $credit_currency = $party_data->credit_currency;
                        $credit_utilized_amount = $party_data->credit_utilized_amount;
                    }
                }

                if ($credit_currency && $credit_currency !== $session_currency) {
                    $info = [
                        'exchange_rate_id' => 0,
                        'from_currency' => $credit_currency,
                        'to_currency' => $session_currency,
                        'user_id' => $user_id,
                        'order_id' => $order_id,
                        'org_id' => $org_id,
                        'be_value' => $be_value
                    ];
                    $exchange_rate_amount = $this->orderProcessor->getExchangeAmountFromCurrency($info);
                } else {
                    $exchange_rate_amount = 1;
                }

                if ($credit_limit == 1) {
                    $order_ids = $this->orderProcessor->getTableData(
                        ['customer_id' => $customer_id],
                        ['id'],
                        Order::class
                    );

                    $limit_orders = array_column($order_ids, 'id');
                    foreach ($limit_orders as $order_row_id) {
                        $revenues = Revenue::where('order_id', $order_row_id)
                            ->where('status', 1)
                            ->pluck('id');

                        foreach ($revenues as $revenue_row_id) {
                            $charges = $this->orderProcessor->getTableData(
                                ['revenue_id' => $revenue_row_id],
                                ['amount', 'currency', 'local_amount', 'local_currency'],
                                Charge::class
                            );

                            foreach ($charges as $charge) {
                                $charge_amount = $charge['amount'];
                                if ($charge['currency'] !== $session_currency) {
                                    $info = [
                                        'exchange_rate_id' => 0,
                                        'from_currency' => $charge['currency'],
                                        'to_currency' => $session_currency,
                                        'user_id' => $user_id,
                                        'order_id' => $order_row_id,
                                        'org_id' => $org_id,
                                        'be_value' => $be_value
                                    ];
                                    $exchange_rate = $this->orderProcessor->getExchangeAmountFromCurrency($info);
                                    if ($exchange_rate > 0) {
                                        $charge_amount = $exchange_rate * $charge_amount;
                                    }
                                }
                                $total_amount += $charge_amount;
                            }
                        }
                    }

                    $total_utilized_amount = $total_amount;
                    if ($credit_currency && $session_currency !== $credit_currency) {
                        $info = [
                            'exchange_rate_id' => 0,
                            'from_currency' => $session_currency,
                            'to_currency' => $credit_currency,
                            'user_id' => $user_id,
                            'order_id' => 0,
                            'org_id' => $org_id,
                            'be_value' => $be_value
                        ];
                        $exchange_rate = $this->orderProcessor->getExchangeAmountFromCurrency($info);
                        if ($exchange_rate > 0) {
                            $total_utilized_amount = $total_amount * $exchange_rate;
                        }
                    }

                    SxPartyMembers::where('id', $party_master_id)->update(['credit_utilized_amount' => $total_utilized_amount]);

                    foreach ($limit_orders as $order_row_id) {
                        $current_hold_type = 0;
                        $order_data = $this->orderProcessor->newGetTableRowData(
                            ['id' => $order_row_id, 'status' => 1],
                            ['hold_type'],
                            Order::class
                        );
                        $prev_hold_type = $order_data['hold_type'] ?? 0;

                        if ($total_utilized_amount > $credit_limit_amount) {
                            $current_hold_type = $prev_hold_type ? ($prev_hold_type == 1 ? 3 : ($prev_hold_type == 2 ? 2 : 3)) : 2;
                        } elseif ($total_utilized_amount < $credit_limit_amount && $prev_hold_type) {
                            $current_hold_type = $prev_hold_type == 1 ? 1 : ($prev_hold_type == 2 ? 0 : 1);
                        }

                        if ($current_hold_type !== $prev_hold_type) {
                            Order::where('id', $order_row_id)
                                ->where('status', 1)
                                ->where('trip_id', 0)
                                ->update(['hold_type' => $current_hold_type]);
                        }
                    }
                }
            }
        }
    }

    public function getChargesForRevenue(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'revenue_id' => 'required|integer|min:1|exists:reveneus,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $revenue_id = $request->input('revenue_id');
            $charges = [];

            $revenue_data = $this->orderProcessor->newGetTableRowData(
                ['id' => $revenue_id],
                ['type', 'recipient_role', 'foreign_currency', 'invoice_status', 'bill_id'],
                Revenue::class
            );

            $inv_status = $revenue_data['invoice_status'] ?? 0;
            $recipient_role = $revenue_data['recipient_role'] ?? '';
            $foreign_currency = $revenue_data['foreign_currency'] ?? '';
            $bill_id = $revenue_data['bill_id'] ?? 0;
            $type = $revenue_data['type'] ?? 0;

            $bill_status = 1;
            if ($bill_id > 0) {
                $bill_data = $this->orderProcessor->newGetTableRowData(
                    ['id' => $bill_id],
                    ['status'],
                    Bill::class
                );
                $bill_status = $bill_data['status'] ?? 1;
            }

            $charge_data = $this->getChargesForRevenueModel($revenue_id);

            foreach ($charge_data as $res) {
                $cat_id = $res['cat_id'] ?? 0;
                $vat_desc = '';

                if ($cat_id > 0) {
                    $vat_data = $this->orderProcessor->newGetTableRowData(
                        ['id' => $cat_id, 'status' => 1],
                        ['description'],
                        VatCategory::class
                    );
                    $vat_desc = $vat_data['description'] ?? '';
                }

                $local_vat_amount = is_numeric($res['local_vat_amount']) ? $res['local_vat_amount'] : '';
                $local_total_amount = is_numeric($res['local_total_amount']) ? $res['local_total_amount'] : '';

                $charges[] = [
                    'revenueId' => $res['revenue_id'],
                    'chargeId' => $res['id'],
                    'chargeCode' => $res['chargecode'],
                    'description' => $res['description'],
                    'quantityUnit' => $res['quantity_unit'],
                    'value' => $res['value'],
                    'rateId' => $res['rate_id'],
                    'amount' => $res['amount'],
                    'currency' => $res['currency'],
                    'localAmount' => $res['local_amount'],
                    'localCurrency' => $res['local_currency'],
                    'action' => '',
                    'chargecodeId' => $res['charge_code'],
                    'invStatus' => $inv_status,
                    'recipientRole' => $recipient_role,
                    'foreignCurrency' => $foreign_currency,
                    'vatPercentage' => $res['vat_percentage'],
                    'vatAmount' => $res['vat_amount'],
                    'totalAmount' => $res['total_amount'],
                    'catId' => $cat_id,
                    'catVal' => $res['cat_val'],
                    'billStatus' => $bill_status,
                    'vatDesc' => $vat_desc,
                    'type' => $type,
                    'sourceCreated' => $res['source_created'],
                    'localVatAmount' => $local_vat_amount,
                    'localTotalAmount' => $local_total_amount
                ];
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Charges retrieved successfully',
                'data' => $charges
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve charges: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    private function getChargesForRevenueModel($revenue_id)
    {
        return Charge::select('charges.*', 'charge_codes.charge_code as chargecode')
            ->leftJoin('charge_codes', 'charges.charge_code', '=', 'charge_codes.id')
            ->where('charges.revenue_id', $revenue_id)
            ->where('charges.status', 1)
            ->get()
            ->toArray();
    }

    public function getChargeDescAndVat(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'chargecode_id' => 'required|integer|min:1|exists:charge_codes,id',
                'revenue_id' => 'nullable|integer|min:1|exists:reveneus,id',
                'shipper_city' => 'nullable|string|max:100',
                'shipper_state' => 'nullable|string|max:100',
                'shipper_country' => 'nullable|string|max:100',
                'consignee_city' => 'nullable|string|max:100',
                'consignee_state' => 'nullable|string|max:100',
                'consignee_country' => 'nullable|string|max:100',
                'org_id' => 'nullable|string|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $user = Auth::user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized: User not authenticated',
            //         'data' => null
            //     ], 401);
            // }

            $chargecode_id = $request->input('chargecode_id', 0);
            $revenue_id = $request->input('revenue_id', 0);
            $shipper_city = $request->input('shipper_city', '');
            $shipper_state = $request->input('shipper_state', '');
            $shipper_country = $request->input('shipper_country', '');
            $consignee_city = $request->input('consignee_city', '');
            $consignee_state = $request->input('consignee_state', '');
            $consignee_country = $request->input('consignee_country', '');
            $user_id = $user->id;
            $org_id = $user->org_id ?? $request->input('org_id', '');

            $source = [
                '1' => trim($shipper_country),
                '2' => trim($shipper_state),
                '3' => trim($shipper_city)
            ];
            $destination = [
                '1' => trim($consignee_country),
                '2' => trim($consignee_state),
                '3' => trim($consignee_city)
            ];

            $result = [
                'vatPercentage' => '',
                'catId' => '',
                'catVal' => '',
                'desc' => ''
            ];

            // Get charge code description
            if ($chargecode_id) {
                $charge_code_data = $this->orderProcessor->newGetTableRowData(
                    ['id' => $chargecode_id, 'status' => 1],
                    ['description'],
                    ChargeCode::class
                );
                $result['desc'] = $charge_code_data['description'] ?? '';
            }

            // Get recipient code and type from revenue
            $custcode = 0;
            $type = 0;
            if ($revenue_id) {
                $revenue_data = $this->orderProcessor->newGetTableRowData(
                    ['id' => $revenue_id, 'status' => 1],
                    ['recipient_code', 'type'],
                    Revenue::class
                );
                $custcode = $revenue_data['recipient_code'] ?? 0;
                $type = $revenue_data['type'] ?? 0;
            }

            // Get VAT details
            $data = [
                'custcode' => $custcode,
                'chargecode_id' => $chargecode_id,
                'user_id' => $user_id,
                'org_id' => $org_id,
                'type' => $type
            ];

            $vat_details = $this->getVatDetails($data);

            if (!empty($vat_details)) {
                foreach ($vat_details as $res) {
                    $source_geo = $res['source_geo'];
                    $destination_geo = $res['destination_geo'];
                    $source_country = trim($res['source_country']);
                    $destination_country = trim($res['destination_country']);

                    if (strcasecmp($source[$source_geo], $source_country) === 0 && strcasecmp($destination[$destination_geo], $destination_country) === 0) {
                        $final_vat = $res['vat'];
                        $result['vatPercentage'] = $org_id === 'VNKN' ? round($final_vat) : round($final_vat, 2);
                        $result['catId'] = $res['cat_id'];
                        $result['catVal'] = $res['cat_val'];
                    }
                }
            } else {
                $vat_generic = $this->getVatGeneric($data);
                foreach ($vat_generic as $res) {
                    $source_geo = $res['source_geo'];
                    $destination_geo = $res['destination_geo'];
                    $source_country = trim($res['source_country']);
                    $destination_country = trim($res['destination_country']);

                    if (strcasecmp($source[$source_geo], $source_country) === 0 && strcasecmp($destination[$destination_geo], $destination_country) === 0) {
                        $final_vat = $res['vat'];
                        $result['vatPercentage'] = $org_id === 'VNKN' ? round($final_vat) : round($final_vat, 2);
                        $result['catId'] = $res['cat_id'];
                        $result['catVal'] = $res['cat_val'];
                    }
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Charge description and VAT details retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve charge description and VAT: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    private function getVatDetails(array $data)
    {
        $query = VatMaster::select([
            'vat_master.name',
            'vat_master.cat_id',
            'vat_master.cat_val',
            'lane_vat.charge_id',
            'lane_vat.vat',
            'charge_codes.charge_code',
            'lanes.source_geo',
            'lanes.source_country',
            'lanes.destination_geo',
            'lanes.destination_country'
        ])
            ->from('vat_master')
            ->leftJoin('lanes', 'lanes.vatid', '=', 'vat_master.id')
            ->leftJoin('lane_vat', 'lane_vat.lane_id', '=', 'lanes.id')
            ->leftJoin('charge_codes', 'charge_codes.id', '=', 'lane_vat.charge_id')
            ->leftJoin('sx_party_members', function ($join) use ($data) {
                $join->on('sx_party_members.customeridentifier', '=', $data['type'] == 0 ? 'vat_master.customeridentifier' : 'vat_master.vendoridentifier');
            })
            ->where('sx_party_members.code', $data['custcode'])
            ->where('charge_codes.id', $data['chargecode_id'])
            ->where('vat_master.org_id', $data['org_id'])
            ->where('vat_master.status', 1)
            ->where('lanes.status', 1)
            ->where('lane_vat.status', 1)
            ->where('sx_party_members.user_id', $data['user_id'])
            ->where('sx_party_members.status', 1);

        return $query->get()->toArray();
    }

    private function getVatGeneric(array $data)
    {
        $query = VatMaster::select([
            'vat_master.name',
            'vat_master.cat_id',
            'vat_master.cat_val',
            'lane_vat.charge_id',
            'lane_vat.vat',
            'charge_codes.charge_code',
            'lanes.source_geo',
            'lanes.source_country',
            'lanes.destination_geo',
            'lanes.destination_country'
        ])
            ->from('vat_master')
            ->leftJoin('lanes', 'lanes.vatid', '=', 'vat_master.id')
            ->leftJoin('lane_vat', 'lane_vat.lane_id', '=', 'lanes.id')
            ->leftJoin('charge_codes', 'charge_codes.id', '=', 'lane_vat.charge_id')
            ->where('charge_codes.id', $data['chargecode_id'])
            ->where('vat_master.org_id', $data['org_id'])
            ->where('vat_master.status', 1)
            ->where('lanes.status', 1)
            ->where('lane_vat.status', 1);

        return $query->get()->toArray();
    }

    public function getVatCategoryDetails(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'vat_category' => 'required|integer|min:1|exists:vat_category,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $cat_id = $request->input('vat_category', 0);
            $data = [
                'catId' => 0,
                'vatPercentage' => 0,
                'catVal' => ''
            ];

            if ($cat_id > 0) {
                $vat_category_data = $this->orderProcessor->newGetTableRowData(
                    ['id' => $cat_id, 'status' => 1],
                    ['vat_category', 'vat_percentage'],
                    VatCategory::class
                );

                if (!empty($vat_category_data)) {
                    $data = [
                        'catId' => $cat_id,
                        'vatPercentage' => $vat_category_data['vat_percentage'],
                        'catVal' => $vat_category_data['vat_category']
                    ];
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'VAT category details retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve VAT category details: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function saveCharge(Request $request, $charge_id = null)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'charge_code' => 'required|integer|exists:charge_codes,id',
                'charge_description' => 'nullable|string|max:255',
                'charge_qty' => 'nullable|numeric|min:1',
                'charge_value' => 'nullable|numeric|min:0',
                'charge_rateid' => 'nullable|integer|min:1',
                'charge_amount' => 'required|numeric|min:0',
                'charge_currency' => 'required|string|max:3',
                'vat_percentage' => 'nullable|numeric|min:0',
                'vat_amount' => 'nullable|numeric|min:0',
                'total_amount' => 'nullable|numeric|min:0',
                'local_amount' => 'nullable|numeric|min:0',
                'local_currency' => 'nullable|string|max:3',
                'charge_revenue_id' => 'required|integer|exists:reveneus,id',
                'cat_id' => 'nullable|integer|exists:vat_category,id',
                'cat_val' => 'nullable|string|max:100',
                'vat_category' => 'nullable|integer|exists:vat_category,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $user = Auth::user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized: User not authenticated',
            //         'data' => null
            //     ], 401);
            // }

            $org_id = $user->org_id ?? $request->input('org_id', '');
            $session_currency = $user->currency ?? 'USD';
            if ($org_id === 'PLKN') {
                $session_currency = 'PLN';
            }

            $charge_code = $request->input('charge_code');
            $description = $request->input('charge_description', '');
            $quantity_unit = $request->input('charge_qty', 1);
            $value = $request->input('charge_value', '');
            $rate_id = $request->input('charge_rateid', 1);
            $amount = $request->input('charge_amount', 0);
            $currency = $request->input('charge_currency');
            $vat_percentage = $request->input('vat_percentage', 0);
            $vat_amount = $request->input('vat_amount', 0);
            $total_amount = $request->input('total_amount', 0);
            $local_amount = $request->input('local_amount', 0);
            $local_currency = $request->input('local_currency', '');
            $charge_revenue_id = $request->input('charge_revenue_id');
            $cat_id = $request->input('cat_id', 0);
            $cat_val = $request->input('cat_val', '');
            $vat_category = $request->input('vat_category', 0);

            // Ensure numeric values
            $amount = is_numeric($amount) ? floatval($amount) : 0;
            $vat_percentage = is_numeric($vat_percentage) ? floatval($vat_percentage) : 0;
            $vat_amount = is_numeric($vat_amount) ? floatval($vat_amount) : 0;
            $total_amount = is_numeric($total_amount) ? floatval($total_amount) : 0;
            $local_amount = is_numeric($local_amount) ? floatval($local_amount) : 0;
            $quantity_unit = is_numeric($quantity_unit) ? floatval($quantity_unit) : 1;
            $rate_id = is_numeric($rate_id) ? intval($rate_id) : 1;

            // Set value if empty
            if (empty($value) && $quantity_unit == 1) {
                $value = $amount;
            }

            // Round amounts
            $charge_amount = round($amount, 4);
            $charge_totalamount = round($total_amount, 4);
            $charge_vatamount = round($vat_amount, 4);

            if ($org_id === 'VNKN' && $session_currency === $currency) {
                $charge_amount = round($amount);
                $charge_totalamount = round($total_amount);
            }

            $charge_data = [
                'revenue_id' => $charge_revenue_id,
                'charge_code' => $charge_code,
                'description' => $description,
                'quantity_unit' => $quantity_unit,
                'value' => $value,
                'rate_id' => $rate_id,
                'amount' => $charge_amount,
                'currency' => $currency,
                'vat_percentage' => $vat_percentage,
                'vat_amount' => $charge_vatamount,
                'total_amount' => $charge_totalamount,
                'cat_id' => $vat_category,
                'cat_val' => $cat_val,
                'source_created' => 'MI - Manual Input'
            ];

            if ($session_currency === $currency) {
                $charge_data['local_currency'] = $currency;
                $charge_data['local_vat_amount'] = $charge_vatamount;
                $charge_data['local_total_amount'] = $charge_totalamount;
                $charge_data['local_amount'] = $org_id === 'VNKN' ? round($amount) : round($amount, 4);
            } else {
                $charge_data['local_amount'] = 0;
                $charge_data['local_vat_amount'] = 0;
                $charge_data['local_total_amount'] = 0;
                $charge_data['local_currency'] = $local_currency;
            }

            if ($charge_id) {
                Charge::where('id', $charge_id)->update($charge_data);
            } else {
                $charge_data['amount'] = round($amount, 4);
                $charge_data['local_amount'] = 0;
                $charge_data['local_vat_amount'] = 0;
                $charge_data['local_total_amount'] = 0;
                if ($session_currency === $currency) {
                    $charge_data['local_vat_amount'] = $charge_vatamount;
                    $charge_data['local_total_amount'] = $charge_totalamount;
                    $charge_data['local_amount'] = $org_id === 'VNKN' ? round($amount) : round($amount, 4);
                }
                $charge_data['created_at'] = Carbon::now();
                $charge_data['status'] = 1;
                $charge_data['user_id'] = $user->id;
                $charge_id = Charge::create($charge_data)->id;
            }

            $this->getTotalAmountOfRevenue($charge_revenue_id);

            return response()->json([
                'status' => 'success',
                'message' => 'Charge ' . ($charge_id ? 'updated' : 'created') . ' successfully',
                'data' => ['chargeRevenueId' => $charge_revenue_id]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to save charge: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function deleteOrderRevenue(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer|min:1|exists:reveneus,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $user = Auth::user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized: User not authenticated',
            //         'data' => null
            //     ], 401);
            // }

            $id = $request->input('id');
            $order_id = 0;

            $revenue_data = $this->orderProcessor->newGetTableRowData(
                ['id' => $id],
                ['id', 'order_id'],
                Revenue::class
            );

            if (!empty($revenue_data)) {
                $order_id = $revenue_data['order_id'] ?? 0;
                Revenue::where('id', $id)->update(['status' => 0]);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Revenue deleted successfully',
                'data' => ['orderId' => $order_id]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete revenue: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function getRateCalendar(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1|exists:orders,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $user = Auth::user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized: User not authenticated',
            //         'data' => null
            //     ], 401);
            // }

            $order_id = $request->input('order_id');
            $dates = [];

            // Generate array of day numbers for the next 15 days
            for ($i = 0; $i <= 15; $i++) {
                $dates[] = Carbon::today()->addDays($i)->format('d');
            }

            $data = [
                'dates' => $dates,
                'orderId' => $order_id
            ];

            return response()->json([
                'status' => 'success',
                'message' => 'Rate calendar retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve rate calendar: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function addCostByCalendar(Request $request)
    {
        try {
            // Validate request data
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|min:1|exists:orders,id',
                'amount' => 'required|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed: ' . $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            $user = Auth::user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized: User not authenticated',
            //         'data' => null
            //     ], 401);
            // }

            $order_id = $request->input('order_id');
            $amount = $request->input('amount');
            $user_id = $user->id;
            $org_id = $user->org_id ?? $request->input('org_id', '');
            $currency = $user->currency ?? 'USD';

            // Determine where condition for SxPartyMember query
            $where_condition = $org_id === 'RUKN' ? ['sx_party_members.org_id' => $org_id] : ['sx_party_members.user_id' => $user_id];

            // Get Carrier details
            $carrier = SxPartyMembers::select('sx_party_members.name', 'sx_party_members.code')
                ->join('sx_party_types', 'sx_party_types.id', '=', 'sx_party_members.Party_type_id')
                ->where('sx_party_types.name', 'like', '%Carrier%')
                ->where($where_condition)
                ->whereNotNull('sx_party_members.acon_debitor_code')
                ->orderBy('sx_party_members.id', 'desc')
                ->first();

            $customer_name = $carrier ? $carrier->name : 'Carrier';
            $customer_code = $carrier ? $carrier->code : '0';
            $charge_amount = round(floatval($amount), 2);

            // Start database transaction
            DB::beginTransaction();

            // Insert revenue
            $revenue_data = [
                'type' => 1,
                'order_id' => $order_id,
                'recipient_role' => 'Carrier',
                'recipient_code' => $customer_code,
                'recipient_name' => $customer_name,
                'debtor_jfr' => '',
                'amount' => $charge_amount,
                'currency' => $currency,
                'status' => 1,
                'user_id' => $user_id,
                'created_at' => Carbon::now()
            ];

            $revenue = Revenue::create($revenue_data);
            $revenue_id = $revenue->id;

            // Insert charge
            $charge_data = [
                'revenue_id' => $revenue_id,
                'charge_code' => 38, // Hardcoded as per original logic
                'description' => 'Freight Charges',
                'quantity_unit' => 1,
                'value' => 1,
                'rate_id' => 1,
                'amount' => $charge_amount,
                'currency' => $currency,
                'status' => 1,
                'user_id' => $user_id,
                'local_amount' => $charge_amount,
                'local_currency' => $currency,
                'created_at' => Carbon::now()
            ];

            $charge = Charge::create($charge_data);

            if ($charge) {
                DB::commit();
                return response()->json([
                    'status' => 'success',
                    'message' => 'Cost added successfully',
                    'data' => ['revenueId' => $revenue_id]
                ], 200);
            } else {
                DB::rollBack();
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to add charge',
                    'data' => null
                ], 500);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add cost: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    public function getVehicleDetailsByCarrierInfo(Request $request)
    {
        try {
            $validated = $request->validate([
                'carrierId' => 'nullable|integer|min:0',
                'vehicleType' => 'nullable|integer|min:0',
            ]);

            $carrierId = $request->input('carrierId', 0);
            $vehicleType = $request->input('vehicleType', 0);
            $user = Auth::user();
            $userId = $user->id ?? 0;
            $orgId = $user->org_id ?? 0;
            $beValue = $user->be_value ?? 0;

            $data = $this->orderProcessor->getVehicleDetailsByCarrierInfo($carrierId, $vehicleType);

            return response()->json([
                'status' => 'success',
                'message' => 'Vehicle details retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('GetVehicleDetailsByCarrierInfo Validation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'data' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('GetVehicleDetailsByCarrierInfo Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving vehicle details',
                'data' => []
            ], 500);
        }
    }

    public function makeSelectedAsInvoice(Request $request)
    {
        ini_set('max_execution_time', 300);

        try {
            $post = $request->all();
            $fromDate = $post['fromdate'] ?? now()->format('Y-m-d H:i:s');
            $orderIds = $post['order_ids'] ?? [];
            $userId = Auth::user()->id ?? 0;
            $timezone = Auth::user()->timezone['timezone'] ?? config('app.timezone', 'UTC');

            if (empty($orderIds)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No order IDs provided',
                    'data' => []
                ], 422);
            }

            $info = [
                'order_ids' => $orderIds,
                'curtz' => $timezone,
                'user_id' => $userId,
            ];

            $this->rateManagement->checkOrderRevenueCurrency($orderIds);
            $result = $this->makeSelectedOrdersAsInvoice($info);

            return response()->json([
                'status' => 'success',
                'message' => 'Orders marked as invoiced successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            Log::error('MakeSelectedAsInvoice Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error processing invoice selection',
                'data' => []
            ], 500);
        }
    }

    protected function makeSelectedOrdersAsInvoice(array $info): array
    {
        $orderIds = array_column($info['order_ids'], 'ids');
        $userId = $info['user_id'];
        $timezone = $info['curtz'];
        $updated = [];

        foreach ($orderIds as $orderId) {
            $revenues = Revenue::where([
                'order_id' => $orderId,
                'invoice_status' => '0',
                'status' => '1'
            ])->get();

            foreach ($revenues as $revenue) {
                $revenue->update([
                    'invoice_status' => '1',
                    'updated_at' => Carbon::now($timezone),
                    'user_id' => $userId,
                ]);
                $updated[] = [
                    'order_id' => $orderId,
                    'revenue_id' => $revenue->id,
                    'status' => 'invoiced',
                ];
            }
        }

        return $updated;
    }

    public function validateConsolidation(Request $request)
    {
        try {
            $orderIds = $request->input('order_ids', []);
            $status = 0;
            $branches = [];

            if (!empty($orderIds)) {
                $orders = Order::whereIn('id', $orderIds)
                    ->where('status', '!=', 0)
                    ->select('id', 'shift_id', 'be_value')
                    ->get();

                if ($orders->isNotEmpty()) {
                    foreach ($orders as $data) {
                        if ($data->shift_id == 0) {
                            $status = 1;
                        }
                        if (!in_array($data->be_value, $branches)) {
                            $branches[] = $data->be_value;
                        }
                    }

                    if (count($branches) > 1) {
                        $status = 2;
                    }
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Consolidation validated successfully',
                'data' => ['status' => $status],
            ], 200);
        } catch (\Exception $e) {
            Log::error('ValidateConsolidation Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error validating consolidation',
                'data' => ['status' => 0],
            ], 500);
        }
    }
    
}
