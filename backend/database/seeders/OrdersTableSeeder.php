<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class OrdersTableSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $currentDateTime = Carbon::now();

        $orders = [
            [
                'id' => 1,
                'order_id' => 'ORD123456',
                'pickup_datetime' => '2025-07-26 10:00:00',
                'pickup_endtime' => '12:00 PM',
                'delivery_datetime' => '2025-07-27 14:00:00',
                'drop_endtime' => '4:00 PM',
                'pickup_company' => 'ABC Logistics',
                'delivery_company' => 'XYZ Retail',
                'pickup_country' => 'USA',
                'delivery_country' => 'USA',
                'pickup_city' => 'New York',
                'delivery_city' => 'Los Angeles',
                'pickup_pincode' => '10001',
                'delivery_pincode' => '90001',
                'delivery_district_pincode' => '90002',
                'delivery_area_pincode' => '90003',
                'pickup_address1' => '123 Main St',
                'delivery_address1' => '456 Market St',
                'pickup_address2' => 'Suite 101',
                'delivery_address2' => 'Floor 2',
                'quantity' => 100.50,
                'weight' => 500.75,
                'gross_weight_uom' => 'kg',
                'chargeable_weight' => 510.00,
                'chargeable_weight_uom' => 'kg',
                'volume' => 2.50,
                'goods_value' => 1500.00,
                'currency' => 'USD',
                'transport_mode' => 'TL',
                'dedicated_vehicle' => 1,
                'vehicle_type' => 'Truck',
                'user_id' => 1,
                'sub_uid' => 2,
                'customer_id' => 101,
                'vendor_id' => 201,
                'customer_name' => 'John Doe',
                'customer_code' => 'CUST001',
                'customer_area' => 'Downtown',
                'customer_phone' => '+1234567890',
                'customer_email' => '<EMAIL>',
                'customer_comments' => 'Fragile items, handle with care',
                'vendor_name' => 'Jane Smith',
                'vendor_code' => 'VEND001',
                'vendor_area' => 'Industrial Area',
                'vendor_phone' => '+0987654321',
                'vendor_email' => '<EMAIL>',
                'vendor_comments' => 'Deliver after 2 PM',
                'plat' => '40.7128',
                'plng' => '-74.0060',
                'dlat' => '34.0522',
                'dlng' => '-118.2437',
                'wlatitude' => '40.7128',
                'wlongitude' => '-74.0060',
                'org_id' => 1,
                'be_value' => 1000,
                'product' => 'Electronics',
                'shipment_id' => 501,
                'pickup_custid' => 'PCUST001',
                'pickup_partyid' => 'PPARTY001',
                'drop_custid' => 'DCUST001',
                'drop_partyid' => 'DPARTY001',
                'status' => 1,
                'order_status' => 'Pending',
                'is_created' => 1,
                'trip_sts' => 0,
                'shift_id' => 1,
                'trip_id' => 1001,
                'uniqid_of_package' => 'PKG123456',
                'created_at' => $currentDateTime,
                'updated_at' => $currentDateTime,
                'category_id' => 1,
                'parent_id' => 0,
                'gba_rule' => 0,
                'shipper_id' => 'SHIP001',
                'consignee_id' => 'CONS001',
                'created_source' => 1,
                'shipmentid' => 'SHIPID001',
                'logicalreceiver' => 'REC001',
                'physicalreceiver' => 'PHYREC001',
                'physicalsender' => 'PHYSEND001',
                'logicalsender' => 'LOGSEND001',
                'modeoftransport' => 'Road',
                'hold_type' => 0,
                'cargo_approval' => 0,
                'credit_approval' => 0,
                'resolution_id' => 0,
                'assigned_to' => 1,
                'Stoppage' => 'STOP001',
                'external_order_id' => 'EXTORD001',
                'sequence_number' => 1,
                'destination_branch' => 'BRANCH001',
                'order_status_category' => 1,
                'shipment_type' => 1,
                'region' => 1,
                'payment_type' => 'Prepaid',
                'third_party_post' => 'POST001',
                'order_trips' => 'TRIP001',
            ],
        ];

        DB::table('orders')->insert($orders);
    }
}