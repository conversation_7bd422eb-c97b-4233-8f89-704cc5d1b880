<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $currentDateTime = Carbon::now();

        DB::table('sx_super_admin')->upsert(
            [
                [
                    'id'          => 1,
                    'username'    => 'shipmentx',
                    'password'    => 'eebda9b6e78492c26af4a60bf3649d12',
                    'org_id'      => 0,
                    'first_name'  => null,
                    'last_name'   => null,
                    'email'       => null,
                    'mobile'      => null,
                    'status'      => 1,
                    'created_at'  => '2022-07-12 16:48:51',
                    'updated_at'  => '2022-07-12 22:18:51',
                ],
                [
                    'id' => 2,
                    'username' => 'RamCo_Admin',
                    'password' => 'c2144df9d5fa1b97c5b6525980ee5e20',
                    'org_id' => 1,
                    'first_name' => 'RamCo',
                    'last_name' => 'Admin',
                    'email' => '<EMAIL>',
                    'mobile' => '9966314178',
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
            ],
            ['id'],  // unique key(s) for upsert
            ['username', 'password', 'org_id', 'first_name', 'last_name', 'email', 'mobile', 'status', 'created_at', 'updated_at']
        );

        DB::table('sx_modules')->upsert(
            [
                [
                    'id' => 1,
                    'module_id' => 'SHXM0001',
                    'module_name' => 'Dashboard',
                    'description' => 'User Dashboard',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'dashboard',
                    'deleted_at' => null,
                    'menu_name' => 'Dashboard',
                    'status' => 1,
                    'menu_sequence' => 1,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 2,
                    'module_id' => 'SHXM0002',
                    'module_name' => 'Booking',
                    'description' => 'Booking',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'booking',
                    'deleted_at' => null,
                    'menu_name' => 'Booking',
                    'status' => 1,
                    'menu_sequence' => 2,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 3,
                    'module_id' => 'SHXM0003',
                    'module_name' => 'Trip',
                    'description' => 'Trip',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'trip',
                    'deleted_at' => null,
                    'menu_name' => 'Trip',
                    'status' => 1,
                    'menu_sequence' => 3,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 4,
                    'module_id' => 'SHXM0004',
                    'module_name' => 'XBorder',
                    'description' => 'XBorder',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'xborder',
                    'deleted_at' => null,
                    'menu_name' => 'XBorder',
                    'status' => 1,
                    'menu_sequence' => 4,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 5,
                    'module_id' => 'SHXM0005',
                    'module_name' => 'Visibility',
                    'description' => 'Visibility',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'visibility',
                    'deleted_at' => null,
                    'menu_name' => 'Visibility',
                    'status' => 1,
                    'menu_sequence' => 5,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 6,
                    'module_id' => 'SHXM0006',
                    'module_name' => 'Billing',
                    'description' => 'Billing',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'billing',
                    'deleted_at' => null,
                    'menu_name' => 'Billing',
                    'status' => 1,
                    'menu_sequence' => 6,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 7,
                    'module_id' => 'SHXM0007',
                    'module_name' => 'Masters',
                    'description' => 'Masters',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'masters',
                    'deleted_at' => null,
                    'menu_name' => 'Masters',
                    'status' => 1,
                    'menu_sequence' => 7,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 8,
                    'module_id' => 'SHXM0008',
                    'module_name' => 'Reports',
                    'description' => 'Reports',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'reports',
                    'deleted_at' => null,
                    'menu_name' => 'Reports',
                    'status' => 1,
                    'menu_sequence' => 8,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 9,
                    'module_id' => 'SHXM0009',
                    'module_name' => 'Tender',
                    'description' => 'Tender',
                    'created_by' => 0,
                    'updated_by' => 0,
                    'controller_name' => 'tender',
                    'deleted_at' => null,
                    'menu_name' => 'Tender',
                    'status' => 1,
                    'menu_sequence' => 9,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
            ],
            ['id'], // unique key
            ['module_id', 'module_name', 'description', 'created_by', 'updated_by', 'controller_name', 'deleted_at', 'menu_name', 'status', 'menu_sequence', 'created_at', 'updated_at']
        );

        DB::table('sx_module_features')->upsert(
            [
                [
                    'id' => 1,
                    'feature_name' => 'Dashboard',
                    'description' => 'Dashboard',
                    'module_id' => 1,
                    'method_name' => 'dashboard',
                    'deleted_at' => null,
                    'menu_name' => 'Dashboard',
                    'status' => 1,
                    'menu_sequence' => 1,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 2,
                    'feature_name' => 'FleetView',
                    'description' => 'Fleet View',
                    'module_id' => 1,
                    'method_name' => 'fleetview',
                    'deleted_at' => null,
                    'menu_name' => 'Fleet View',
                    'status' => 1,
                    'menu_sequence' => 2,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 3,
                    'feature_name' => 'Wrokbench',
                    'description' => 'Wrokbench',
                    'module_id' => 1,
                    'method_name' => 'wrokbench',
                    'deleted_at' => null,
                    'menu_name' => 'Wrokbench',
                    'status' => 1,
                    'menu_sequence' => 3,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 4,
                    'feature_name' => 'Maintenance',
                    'description' => 'Maintenance',
                    'module_id' => 1,
                    'method_name' => 'maintenance',
                    'deleted_at' => null,
                    'menu_name' => 'Maintenance',
                    'status' => 1,
                    'menu_sequence' => 4,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 5,
                    'feature_name' => 'Orders',
                    'description' => 'Orders',
                    'module_id' => 2,
                    'method_name' => 'orders',
                    'deleted_at' => null,
                    'menu_name' => 'Orders',
                    'status' => 1,
                    'menu_sequence' => 5,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 6,
                    'feature_name' => 'BulkUpdate',
                    'description' => 'Bulk Update',
                    'module_id' => 2,
                    'method_name' => 'bulkupdate',
                    'deleted_at' => null,
                    'menu_name' => 'Bulk Update',
                    'status' => 1,
                    'menu_sequence' => 6,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 7,
                    'feature_name' => 'DocuemntControl',
                    'description' => 'Docuemnt Control',
                    'module_id' => 2,
                    'method_name' => 'docuemntcontrol',
                    'deleted_at' => null,
                    'menu_name' => 'Docuemnt Control',
                    'status' => 1,
                    'menu_sequence' => 7,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 8,
                    'feature_name' => 'BulkUpdate',
                    'description' => 'Bulk Update',
                    'module_id' => 2,
                    'method_name' => 'bulkupdate',
                    'deleted_at' => null,
                    'menu_name' => 'Bulk Update',
                    'status' => 1,
                    'menu_sequence' => 8,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 9,
                    'feature_name' => 'Palletizer',
                    'description' => 'Palletizer',
                    'module_id' => 2,
                    'method_name' => 'palletizer',
                    'deleted_at' => null,
                    'menu_name' => 'Palletizer',
                    'status' => 1,
                    'menu_sequence' => 9,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 10,
                    'feature_name' => 'Claims',
                    'description' => 'Claims',
                    'module_id' => 2,
                    'method_name' => 'claims',
                    'deleted_at' => null,
                    'menu_name' => 'Claims',
                    'status' => 1,
                    'menu_sequence' => 10,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 11,
                    'feature_name' => 'Ports',
                    'description' => 'Ports',
                    'module_id' => 2,
                    'method_name' => 'ports',
                    'deleted_at' => null,
                    'menu_name' => 'Ports',
                    'status' => 1,
                    'menu_sequence' => 11,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 12,
                    'feature_name' => 'ShipmentPlan',
                    'description' => 'Shipment Plan',
                    'module_id' => 3,
                    'method_name' => 'shipmentplan',
                    'deleted_at' => null,
                    'menu_name' => 'Shipment Plan',
                    'status' => 1,
                    'menu_sequence' => 12,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 13,
                    'feature_name' => 'Routing',
                    'description' => 'Routing',
                    'module_id' => 3,
                    'method_name' => 'routing',
                    'deleted_at' => null,
                    'menu_name' => 'Routing',
                    'status' => 1,
                    'menu_sequence' => 13,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 14,
                    'feature_name' => 'Trips',
                    'description' => 'Trips',
                    'module_id' => 3,
                    'method_name' => 'trips',
                    'deleted_at' => null,
                    'menu_name' => 'Trips',
                    'status' => 1,
                    'menu_sequence' => 14,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 15,
                    'feature_name' => 'TripTemplate',
                    'description' => 'Trip Template',
                    'module_id' => 3,
                    'method_name' => 'triptemplate',
                    'deleted_at' => null,
                    'menu_name' => 'Trip Template',
                    'status' => 1,
                    'menu_sequence' => 15,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 16,
                    'feature_name' => 'TripExpense',
                    'description' => 'Trip Expense',
                    'module_id' => 3,
                    'method_name' => 'tripexpense',
                    'deleted_at' => null,
                    'menu_name' => 'Trip Expense',
                    'status' => 1,
                    'menu_sequence' => 16,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 17,
                    'feature_name' => 'ReturnTrucks',
                    'description' => 'Return Trucks',
                    'module_id' => 3,
                    'method_name' => 'returntrucks',
                    'deleted_at' => null,
                    'menu_name' => 'Return Trucks',
                    'status' => 1,
                    'menu_sequence' => 17,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 18,
                    'feature_name' => 'XBorderOrders',
                    'description' => 'XBorder Orders',
                    'module_id' => 4,
                    'method_name' => 'xborderorders',
                    'deleted_at' => null,
                    'menu_name' => 'XBorder Orders',
                    'status' => 1,
                    'menu_sequence' => 18,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 19,
                    'feature_name' => 'XBorderTrips',
                    'description' => 'XBorder Trips',
                    'module_id' => 4,
                    'method_name' => 'xbordertrips',
                    'deleted_at' => null,
                    'menu_name' => 'XBorder Trips',
                    'status' => 1,
                    'menu_sequence' => 19,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 20,
                    'feature_name' => 'ActiveOrders',
                    'description' => 'Active Orders',
                    'module_id' => 5,
                    'method_name' => 'activeorders',
                    'deleted_at' => null,
                    'menu_name' => 'Active Orders',
                    'status' => 1,
                    'menu_sequence' => 20,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 21,
                    'feature_name' => 'PendingOrders',
                    'description' => 'Pending Orders',
                    'module_id' => 5,
                    'method_name' => 'pendingorders',
                    'deleted_at' => null,
                    'menu_name' => 'Pending Orders',
                    'status' => 1,
                    'menu_sequence' => 21,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 22,
                    'feature_name' => 'CompletedOrders',
                    'description' => 'Completed Orders',
                    'module_id' => 5,
                    'method_name' => 'completedorders',
                    'deleted_at' => null,
                    'menu_name' => 'Completed Orders',
                    'status' => 1,
                    'menu_sequence' => 22,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 23,
                    'feature_name' => 'ByMilestone',
                    'description' => 'By Milestone',
                    'module_id' => 5,
                    'method_name' => 'bymilestone',
                    'deleted_at' => null,
                    'menu_name' => 'By Milestone',
                    'status' => 1,
                    'menu_sequence' => 23,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 24,
                    'feature_name' => 'Billing',
                    'description' => 'Billing',
                    'module_id' => 6,
                    'method_name' => 'billing',
                    'deleted_at' => null,
                    'menu_name' => 'Billing',
                    'status' => 1,
                    'menu_sequence' => 24,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 25,
                    'feature_name' => 'VATMaster',
                    'description' => 'VAT Master',
                    'module_id' => 6,
                    'method_name' => 'vatmaster',
                    'deleted_at' => null,
                    'menu_name' => 'VAT Master',
                    'status' => 1,
                    'menu_sequence' => 25,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 26,
                    'feature_name' => 'RateService',
                    'description' => 'Rate Service',
                    'module_id' => 6,
                    'method_name' => 'rateservice',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Service',
                    'status' => 1,
                    'menu_sequence' => 26,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 27,
                    'feature_name' => 'RateOffering',
                    'description' => 'Rate Offering',
                    'module_id' => 6,
                    'method_name' => 'rateoffering',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Offering',
                    'status' => 1,
                    'menu_sequence' => 27,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 28,
                    'feature_name' => 'RateRecord',
                    'description' => 'Rate Record',
                    'module_id' => 6,
                    'method_name' => 'raterecord',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Record',
                    'status' => 1,
                    'menu_sequence' => 28,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 29,
                    'feature_name' => 'RatePreference',
                    'description' => 'Rate Preference',
                    'module_id' => 6,
                    'method_name' => 'ratepreference',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Preference',
                    'status' => 1,
                    'menu_sequence' => 29,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 30,
                    'feature_name' => 'Tiers',
                    'description' => 'Tiers',
                    'module_id' => 6,
                    'method_name' => 'tiers',
                    'deleted_at' => null,
                    'menu_name' => 'Tiers',
                    'status' => 1,
                    'menu_sequence' => 30,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 31,
                    'feature_name' => 'RateCalendar',
                    'description' => 'Rate Calendar',
                    'module_id' => 6,
                    'method_name' => 'ratecalendar',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Calendar',
                    'status' => 1,
                    'menu_sequence' => 31,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 32,
                    'feature_name' => 'ExchangeRate',
                    'description' => 'Exchange Rate',
                    'module_id' => 6,
                    'method_name' => 'exchangerate',
                    'deleted_at' => null,
                    'menu_name' => 'Exchange Rate',
                    'status' => 1,
                    'menu_sequence' => 32,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 33,
                    'feature_name' => 'ConversionFactor',
                    'description' => 'Conversion Factor',
                    'module_id' => 6,
                    'method_name' => 'conversionfactor',
                    'deleted_at' => null,
                    'menu_name' => 'Conversion Factor',
                    'status' => 1,
                    'menu_sequence' => 33,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 34,
                    'feature_name' => 'FuelSurcharge',
                    'description' => 'Fuel Surcharge',
                    'module_id' => 6,
                    'method_name' => 'fuelsurcharge',
                    'deleted_at' => null,
                    'menu_name' => 'Fuel Surcharge',
                    'status' => 1,
                    'menu_sequence' => 34,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 35,
                    'feature_name' => 'CostAudit',
                    'description' => 'Cost Audit',
                    'module_id' => 6,
                    'method_name' => 'costaudit',
                    'deleted_at' => null,
                    'menu_name' => 'Cost Audit',
                    'status' => 1,
                    'menu_sequence' => 35,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 36,
                    'feature_name' => 'Consolidation',
                    'description' => 'Consolidation',
                    'module_id' => 6,
                    'method_name' => 'consolidation',
                    'deleted_at' => null,
                    'menu_name' => 'Consolidation',
                    'status' => 1,
                    'menu_sequence' => 36,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 37,
                    'feature_name' => 'QuickRates',
                    'description' => 'Quick Rates',
                    'module_id' => 6,
                    'method_name' => 'quickrates',
                    'deleted_at' => null,
                    'menu_name' => 'Quick Rates',
                    'status' => 1,
                    'menu_sequence' => 37,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 38,
                    'feature_name' => 'CommunicationManagement',
                    'description' => 'Communication Management',
                    'module_id' => 7,
                    'method_name' => 'communicationmanagement',
                    'deleted_at' => null,
                    'menu_name' => 'Communication Management',
                    'status' => 1,
                    'menu_sequence' => 38,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 39,
                    'feature_name' => 'Fleet',
                    'description' => 'Fleet',
                    'module_id' => 7,
                    'method_name' => '[{"id":"Drivers"},{"id":"Vehicles"},{"id":"Order Type"},{"id":"Cost Center"},{"id":"Vehicle Type"}]',
                    'deleted_at' => null,
                    'menu_name' => 'Fleet',
                    'status' => 1,
                    'menu_sequence' => 39,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 40,
                    'feature_name' => 'BusinessPartners',
                    'description' => 'Business Partners',
                    'module_id' => 7,
                    'method_name' => 'businesspartners',
                    'deleted_at' => null,
                    'menu_name' => 'Business Partners',
                    'status' => 1,
                    'menu_sequence' => 40,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 41,
                    'feature_name' => 'TrafficCode',
                    'description' => 'Traffic Code',
                    'module_id' => 7,
                    'method_name' => 'trafficcode',
                    'deleted_at' => null,
                    'menu_name' => 'Traffic Code',
                    'status' => 1,
                    'menu_sequence' => 41,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 42,
                    'feature_name' => 'StatusMaster',
                    'description' => 'Status Master',
                    'module_id' => 7,
                    'method_name' => 'statusmaster',
                    'deleted_at' => null,
                    'menu_name' => 'Status Master',
                    'status' => 1,
                    'menu_sequence' => 42,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 43,
                    'feature_name' => 'LaneMaster',
                    'description' => 'Lane Master',
                    'module_id' => 7,
                    'method_name' => 'lanemaster',
                    'deleted_at' => null,
                    'menu_name' => 'Lane Master',
                    'status' => 1,
                    'menu_sequence' => 43,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 44,
                    'feature_name' => 'Profiles',
                    'description' => 'Profiles',
                    'module_id' => 7,
                    'method_name' => '[{"id":"Customer Profile"},{"id":"Vendor Profile"},{"id":"Vehicle Profile"}]',
                    'deleted_at' => null,
                    'menu_name' => 'Profiles',
                    'status' => 1,
                    'menu_sequence' => 44,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 45,
                    'feature_name' => 'Pincodes',
                    'description' => 'Pincodes',
                    'module_id' => 7,
                    'method_name' => '[{"id":"Pincodes"},{"id":"Preferred State"},{"id":"Trip Allocation Ratio"}]',
                    'deleted_at' => null,
                    'menu_name' => 'Pincodes',
                    'status' => 1,
                    'menu_sequence' => 45,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 46,
                    'feature_name' => 'AllocationRules',
                    'description' => 'Allocation Rules',
                    'module_id' => 7,
                    'method_name' => 'allocationrules',
                    'deleted_at' => null,
                    'menu_name' => 'Allocation Rules',
                    'status' => 1,
                    'menu_sequence' => 46,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 47,
                    'feature_name' => 'ShipmentTypes',
                    'description' => 'Shipment Types',
                    'module_id' => 7,
                    'method_name' => 'shipmenttypes',
                    'deleted_at' => null,
                    'menu_name' => 'Shipment Types',
                    'status' => 1,
                    'menu_sequence' => 47,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 48,
                    'feature_name' => 'Regions',
                    'description' => 'Regions',
                    'module_id' => 7,
                    'method_name' => 'regions',
                    'deleted_at' => null,
                    'menu_name' => 'Regions',
                    'status' => 1,
                    'menu_sequence' => 48,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 49,
                    'feature_name' => 'TripReports',
                    'description' => 'Trip Reports',
                    'module_id' => 8,
                    'method_name' => 'tripreports',
                    'deleted_at' => null,
                    'menu_name' => 'Trip Reports',
                    'status' => 1,
                    'menu_sequence' => 49,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 50,
                    'feature_name' => 'SLA/Occupancy',
                    'description' => 'SLA / Occupancy',
                    'module_id' => 8,
                    'method_name' => 'slaoccupancy',
                    'deleted_at' => null,
                    'menu_name' => 'SLA/Occupancy',
                    'status' => 1,
                    'menu_sequence' => 50,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 51,
                    'feature_name' => 'Shipment/StopLevel',
                    'description' => 'Shipment / Stop Level',
                    'module_id' => 8,
                    'method_name' => 'shipmentstoplevel',
                    'deleted_at' => null,
                    'menu_name' => 'Shipment/Stop Level',
                    'status' => 1,
                    'menu_sequence' => 51,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 52,
                    'feature_name' => 'KMReports',
                    'description' => 'KM Reports',
                    'module_id' => 8,
                    'method_name' => 'kmreports',
                    'deleted_at' => null,
                    'menu_name' => 'KM Reports',
                    'status' => 1,
                    'menu_sequence' => 52,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 53,
                    'feature_name' => 'SpeedometerGraph',
                    'description' => 'Speedometer Graph',
                    'module_id' => 8,
                    'method_name' => 'speedometergraph',
                    'deleted_at' => null,
                    'menu_name' => 'Speedometer Graph',
                    'status' => 1,
                    'menu_sequence' => 53,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 54,
                    'feature_name' => 'SpeedReport',
                    'description' => 'Speed Report',
                    'module_id' => 8,
                    'method_name' => 'speedreport',
                    'deleted_at' => null,
                    'menu_name' => 'Speed Report',
                    'status' => 1,
                    'menu_sequence' => 54,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 55,
                    'feature_name' => 'SMSReport',
                    'description' => 'SMS Report',
                    'module_id' => 8,
                    'method_name' => 'smsreport',
                    'deleted_at' => null,
                    'menu_name' => 'SMS Report',
                    'status' => 1,
                    'menu_sequence' => 55,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 56,
                    'feature_name' => 'OperationalReports',
                    'description' => 'Operational Reports',
                    'module_id' => 8,
                    'method_name' => 'operationalreports',
                    'deleted_at' => null,
                    'menu_name' => 'Operational Reports',
                    'status' => 1,
                    'menu_sequence' => 56,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 57,
                    'feature_name' => 'FinancialReports',
                    'description' => 'Financial Reports',
                    'module_id' => 8,
                    'method_name' => 'financialreports',
                    'deleted_at' => null,
                    'menu_name' => 'Financial Reports',
                    'status' => 1,
                    'menu_sequence' => 57,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 58,
                    'feature_name' => 'CustomerDailyReport',
                    'description' => 'Customer Daily Report',
                    'module_id' => 8,
                    'method_name' => 'customerdailyreport',
                    'deleted_at' => null,
                    'menu_name' => 'Customer Daily Report',
                    'status' => 1,
                    'menu_sequence' => 58,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 59,
                    'feature_name' => 'AllRoundBigReport',
                    'description' => 'All Round Big Report',
                    'module_id' => 8,
                    'method_name' => 'allroundbigreport',
                    'deleted_at' => null,
                    'menu_name' => 'All Round Big Report',
                    'status' => 1,
                    'menu_sequence' => 59,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 60,
                    'feature_name' => 'BillingControlReport',
                    'description' => 'Billing Control Report',
                    'module_id' => 8,
                    'method_name' => 'billingcontrolreport',
                    'deleted_at' => null,
                    'menu_name' => 'Billing Control Report',
                    'status' => 1,
                    'menu_sequence' => 60,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 61,
                    'feature_name' => 'EDILog',
                    'description' => 'EDI Log',
                    'module_id' => 8,
                    'method_name' => 'edilog',
                    'deleted_at' => null,
                    'menu_name' => 'EDI Log',
                    'status' => 1,
                    'menu_sequence' => 61,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 62,
                    'feature_name' => 'TrackReport',
                    'description' => 'Track Report',
                    'module_id' => 8,
                    'method_name' => 'trackreport',
                    'deleted_at' => null,
                    'menu_name' => 'Track Report',
                    'status' => 1,
                    'menu_sequence' => 62,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 63,
                    'feature_name' => 'MISReport',
                    'description' => 'MIS Report',
                    'module_id' => 8,
                    'method_name' => 'misreport',
                    'deleted_at' => null,
                    'menu_name' => 'MIS Report',
                    'status' => 1,
                    'menu_sequence' => 63,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 64,
                    'feature_name' => 'NDRReport',
                    'description' => 'NDR Report',
                    'module_id' => 8,
                    'method_name' => 'ndrreport',
                    'deleted_at' => null,
                    'menu_name' => 'NDR Report',
                    'status' => 1,
                    'menu_sequence' => 64,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 65,
                    'feature_name' => 'CustomizedReport',
                    'description' => 'Customized Report',
                    'module_id' => 8,
                    'method_name' => 'customizedreport',
                    'deleted_at' => null,
                    'menu_name' => 'Customized Report',
                    'status' => 1,
                    'menu_sequence' => 65,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 66,
                    'feature_name' => 'TransitStatusReport',
                    'description' => 'Transit Status Report',
                    'module_id' => 8,
                    'method_name' => 'transitstatusreport',
                    'deleted_at' => null,
                    'menu_name' => 'Transit Status Report',
                    'status' => 1,
                    'menu_sequence' => 66,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 67,
                    'feature_name' => 'KPIOneDay',
                    'description' => 'KPI One Day',
                    'module_id' => 8,
                    'method_name' => 'kpioneday',
                    'deleted_at' => null,
                    'menu_name' => 'KPI One Day',
                    'status' => 1,
                    'menu_sequence' => 67,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 68,
                    'feature_name' => 'ArchivedDocument',
                    'description' => 'Archived Document',
                    'module_id' => 8,
                    'method_name' => 'archiveddocument',
                    'deleted_at' => null,
                    'menu_name' => 'Archived Document',
                    'status' => 1,
                    'menu_sequence' => 68,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 69,
                    'feature_name' => 'TATKPIReport',
                    'description' => 'TAT KPI Report',
                    'module_id' => 8,
                    'method_name' => 'tatkpireport',
                    'deleted_at' => null,
                    'menu_name' => 'TAT KPI Report',
                    'status' => 1,
                    'menu_sequence' => 69,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 70,
                    'feature_name' => 'CarrierKPIReport',
                    'description' => 'Carrier KPI Report',
                    'module_id' => 8,
                    'method_name' => 'carrierkpireport',
                    'deleted_at' => null,
                    'menu_name' => 'Carrier KPI Report',
                    'status' => 1,
                    'menu_sequence' => 70,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 71,
                    'feature_name' => 'OrdersReport',
                    'description' => 'Orders Report',
                    'module_id' => 8,
                    'method_name' => 'ordersreport',
                    'deleted_at' => null,
                    'menu_name' => 'Orders Report',
                    'status' => 1,
                    'menu_sequence' => 71,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 72,
                    'feature_name' => 'SALogKPIReport',
                    'description' => 'SALog KPI Report',
                    'module_id' => 8,
                    'method_name' => 'salogkpireport',
                    'deleted_at' => null,
                    'menu_name' => 'SALog KPI Report',
                    'status' => 1,
                    'menu_sequence' => 72,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 73,
                    'feature_name' => 'GeoFence',
                    'description' => 'Geo Fence',
                    'module_id' => 8,
                    'method_name' => 'geofence',
                    'deleted_at' => null,
                    'menu_name' => 'Geo Fence',
                    'status' => 1,
                    'menu_sequence' => 73,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 74,
                    'feature_name' => 'ShipmentTender',
                    'description' => 'Shipment Tender',
                    'module_id' => 9,
                    'method_name' => 'shipmenttender',
                    'deleted_at' => null,
                    'menu_name' => 'Shipment Tender',
                    'status' => 1,
                    'menu_sequence' => 74,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 75,
                    'feature_name' => 'CarrierRates',
                    'description' => 'Carrier Rates',
                    'module_id' => 9,
                    'method_name' => 'carrierrates',
                    'deleted_at' => null,
                    'menu_name' => 'Carrier Rates',
                    'status' => 1,
                    'menu_sequence' => 75,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ]
            ],
            ['id'], // primary/unique key
            [
                'feature_name',
                'description',
                'module_id',
                'method_name',
                'deleted_at',
                'menu_name',
                'status',
                'menu_sequence',
                'created_at',
                'updated_at'
            ]
        );

        DB::table('sx_previlege_types')->insert([
            [
                'type_name'   => 'User',
                'description' => 'User',
                'status'      => 1,
                'deleted_at'  => null,
                'created_at'  => '2022-07-12 17:41:34',
                'updated_at'  => '2022-07-12 23:11:34',
            ],
            [
                'type_name'   => 'Party',
                'description' => 'Party',
                'status'      => 1,
                'deleted_at'  => null,
                'created_at'  => '2022-07-12 17:41:34',
                'updated_at'  => '2022-07-12 23:11:34',
            ],
        ]);

        DB::table('sx_languages')->insert([
            [
                'lang' => 'English',
                'status' => 1,
                'created_at' => '2022-07-12 17:49:49',
                'updated_at' => '2022-07-12 23:19:49',
                'deleted_at' => null,
            ],
            [
                'lang' => 'Hindi',
                'status' => 1,
                'created_at' => '2022-07-12 17:49:49',
                'updated_at' => '2022-07-12 23:19:49',
                'deleted_at' => null,
            ],
        ]);

        DB::table('sx_themes')->updateOrInsert(
            ['id' => 1],
            [
                'theme_id' => 'Default',
                'theme_name' => 'Default',
                'theme_description' => 'Default',
                'deleted_at' => null,
                'theme_image' => '',
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ]
        );


        DB::table('sx_organization')->upsert(
            [
                [
                    'id'                   => 1,
                    'org_id'               => 'RamCo',
                    'org_name'             => 'RamCo',
                    'org_description'      => 'RamCo',
                    'total_users'          => 100,
                    'concurrent_users'     => 100,
                    'buffer_users'         => 100,
                    'effective_from'       => '2025-07-08 00:00:00',
                    'effective_to'         => '2025-07-31 00:00:00',
                    'buffer_days'          => 100,
                    'alert_contact_admin'  => '9966314178',
                    'alert_contact_legal'  => '9966314178',
                    'alter_contact_sales'  => '9966314178',
                    'auth_name'            => 'RCREDDY K',
                    'contact_num'          => '9966314178',
                    'email_id'             => '<EMAIL>',
                    'address'              => 'Visakhapatnam',
                    'deleted_at'           => null,
                    'logo'                 => 'ramco.jpg',
                    'status'               => 1,
                    'created_at'           => '2025-07-08 02:36:25',
                    'updated_at'           => '2025-07-08 02:36:25',
                ],
            ],
            ['id'],  // primary key for UPSERT
            ['org_id','org_name','org_description','total_users','concurrent_users',
             'buffer_users','effective_from','effective_to','buffer_days',
             'alert_contact_admin','alert_contact_legal','alter_contact_sales',
             'auth_name','contact_num','email_id','address','deleted_at','logo',
             'status','updated_at']
        );

        DB::table('sx_organization_modules')->upsert(
            [
                [
                    'id'            => 1,
                    'org_id'        => 1,
                    'module_id'     => 9,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 2,
                    'org_id'        => 1,
                    'module_id'     => 8,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 3,
                    'org_id'        => 1,
                    'module_id'     => 7,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 4,
                    'org_id'        => 1,
                    'module_id'     => 6,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 5,
                    'org_id'        => 1,
                    'module_id'     => 5,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 6,
                    'org_id'        => 1,
                    'module_id'     => 4,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 7,
                    'org_id'        => 1,
                    'module_id'     => 3,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 8,
                    'org_id'        => 1,
                    'module_id'     => 2,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 9,
                    'org_id'        => 1,
                    'module_id'     => 1,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
            ],
            ['id'],  // unique key for UPSERT
            ['org_id','module_id','created_by','updated_by','deleted_at',
             'module_status','status','created_at','updated_at']
        );

        DB::table('sx_org_module_features')->upsert(
            [
                [
                    'id' => 1,
                    'org_id' => 1,
                    'feature_id' => 48,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 2,
                    'org_id' => 1,
                    'feature_id' => 43,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 3,
                    'org_id' => 1,
                    'feature_id' => 42,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 4,
                    'org_id' => 1,
                    'feature_id' => 41,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 5,
                    'org_id' => 1,
                    'feature_id' => 40,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 6,
                    'org_id' => 1,
                    'feature_id' => 39,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 7,
                    'org_id' => 1,
                    'feature_id' => 47,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 8,
                    'org_id' => 1,
                    'feature_id' => 48,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 9,
                    'org_id' => 1,
                    'feature_id' => 47,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 10,
                    'org_id' => 1,
                    'feature_id' => 46,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 11,
                    'org_id' => 1,
                    'feature_id' => 45,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 12,
                    'org_id' => 1,
                    'feature_id' => 40,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 13,
                    'org_id' => 1,
                    'feature_id' => 39,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 14,
                    'org_id' => 1,
                    'feature_id' => 38,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 15,
                    'org_id' => 1,
                    'feature_id' => 44,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 16,
                    'org_id' => 1,
                    'feature_id' => 43,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 17,
                    'org_id' => 1,
                    'feature_id' => 42,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 18,
                    'org_id' => 1,
                    'feature_id' => 41,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 19,
                    'org_id' => 1,
                    'feature_id' => 75,
                    'module_id' => 9,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 20,
                    'org_id' => 1,
                    'feature_id' => 74,
                    'module_id' => 9,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 21,
                    'org_id' => 1,
                    'feature_id' => 11,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 22,
                    'org_id' => 1,
                    'feature_id' => 10,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 23,
                    'org_id' => 1,
                    'feature_id' => 9,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 24,
                    'org_id' => 1,
                    'feature_id' => 8,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 25,
                    'org_id' => 1,
                    'feature_id' => 7,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 26,
                    'org_id' => 1,
                    'feature_id' => 6,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 27,
                    'org_id' => 1,
                    'feature_id' => 5,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
            ],
            ['id'],  // unique key for upsert
            ['org_id','feature_id','module_id','deleted_at','status','created_at','updated_at']
        );
        
        $partyTypes = [
            [
                'id' => 1,
                'description' => 'Customer',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Customer',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 2,
                'description' => 'Carrier',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Carrier',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 3,
                'description' => 'Shipper',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Shipper',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 4,
                'description' => 'Consignee',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Consignee',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 5,
                'description' => 'Consignor',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Consignor',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 6,
                'description' => 'Customs',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Customs',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 7,
                'description' => 'Freight Payer',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Freight Payer',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_party_types')->upsert(
            $partyTypes,
            ['id'], // Unique key
            ['description', 'status', 'deleted_at', 'type_name', 'org_id', 'be_value', 'created_at', 'updated_at']
        );

        DB::table('address_types')->upsert(
            [
                [
                    'id' => 1,
                    'address_type_name' => 'Residential Address',
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'address_type_name' => 'Physical Address',
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 3,
                    'address_type_name' => 'Permanent Address',
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['address_type_name', 'user_id', 'org_id', 'be_value', 'status', 'created_at', 'updated_at']
        );

        DB::table('document_types')->upsert(
            [
                [
                    'id' => 1,
                    'document_id' => 1,
                    'type_name' => 'Document',
                    'org_id' => 1,
                    'be_value' => null,
                    'country_code' => null,
                    'user_id' => 0,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'document_id' => 2,
                    'type_name' => 'Signature',
                    'org_id' => 1,
                    'be_value' => null,
                    'country_code' => null,
                    'user_id' => 0,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['document_id', 'type_name', 'org_id', 'be_value', 'country_code', 'user_id', 'status', 'created_at', 'updated_at']
        );

        DB::table('handling_units')->upsert(
            [
                [
                    'id' => 1,
                    'unit_name' => 'Pallets',
                    'unit_code' => '0',
                    'description' => 'Pallets',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'unit_name' => 'Boxes',
                    'unit_code' => '0',
                    'description' => 'Boxes',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 3,
                    'unit_name' => 'TAPES',
                    'unit_code' => '0',
                    'description' => 'TAPES',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 4,
                    'unit_name' => 'BAGS',
                    'unit_code' => '0',
                    'description' => 'BAGS',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['unit_name', 'unit_code', 'description', 'org_id', 'be_value', 'user_id', 'status', 'created_at', 'updated_at']
        );

        DB::table('order_types')->upsert(
            [
                [
                    'id' => 1,
                    'type_name' => 'Normal',
                    'description' => 'Normal',
                    'org_id' => 1,
                    'be_value' => null,
                    'user_id' => 0,
                    'customer_id' => 0,
                    'ordtype_code' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'type_name' => 'Urgent',
                    'description' => 'Urgent',
                    'org_id' => 1,
                    'be_value' => null,
                    'user_id' => 0,
                    'customer_id' => 0,
                    'ordtype_code' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['type_name', 'description', 'org_id', 'be_value', 'user_id', 'customer_id', 'ordtype_code', 'status', 'created_at', 'updated_at']
        );

        DB::table('status_master')->upsert(
            [
                [
                    'id' => 1,
                    'status_name' => 'Pending',
                    'description' => 'Pending',
                    'status_type' => 'Pending',
                    'status_code' => 'Pending',
                    'customer_id' => null,
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'status_name' => 'Success',
                    'description' => 'Active',
                    'status_type' => 'Active',
                    'status_code' => 'Active',
                    'customer_id' => null,
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                // Add more rows as needed
            ],
            ['id'],  // unique key(s) for upsert
            [
                'status_name', 'description', 'status_type', 'status_code',
                'customer_id', 'user_id', 'org_id', 'be_value', 'status',
                'created_at', 'updated_at'
            ]
        );

        DB::table('transportmode')->upsert(
            [
                [
                    'id' => 1,
                    'code' => 'AIR-TRANS',
                    'name' => 'AIR TRANSPORT',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'code' => 'SEA-FCL',
                    'name' => 'SEA-FCL',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 3,
                    'code' => 'SEA-LCL',
                    'name' => 'SEA-LCL',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 4,
                    'code' => 'AIR-FCL',
                    'name' => 'AIR-FCL',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 5,
                    'code' => 'AIR-LCL',
                    'name' => 'AIR-LCL',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 6,
                    'code' => 'COURIER',
                    'name' => 'COURIER',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 7,
                    'code' => 'FTL',
                    'name' => 'Full Truck Load',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['code', 'name', 'user_id', 'org_id', 'be_value', 'status', 'created_at', 'updated_at']
        );

        DB::table('trucktypes')->upsert(
            [
                [
                    'id' => 1,
                    'trucktype' => '10 wheeler heavy vehicle',
                    'description' => '10 wheeler heavy vehicle',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'iconimg' => null,
                    'createdby' => 0,
                    'updatedby' => 0,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'trucktype' => '6 wheeler heavy vehicle',
                    'description' => 'Container',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'iconimg' => null,
                    'createdby' => 0,
                    'updatedby' => 0,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['trucktype', 'description', 'org_id', 'be_value', 'user_id', 'status', 'iconimg', 'createdby', 'updatedby', 'created_at', 'updated_at']
        );

        DB::table('sx_business_entity')->upsert(
            [
                [
                    'id'                => 1,
                    'org_id'            => 1,
                    'entity_id'         => 'RAMCMP',
                    'entity_name'       => 'RAMCMP',
                    'entity_description'=> 'RAMCMP',
                    'created_by'        => 0,
                    'updated_by'        => 0,
                    'deleted_at'        => null,
                    'legal_entity'      => 1,
                    'balancing_entity'  => 1,
                    'status'            => 1,
                    'created_at'        => $currentDateTime,
                    'updated_at'        => null,
                ],
                [
                    'id'                => 2,
                    'org_id'            => 1,
                    'entity_id'         => 'RAMBRNCH',
                    'entity_name'       => 'RAMBRNCH',
                    'entity_description'=> 'RAMBRNCH',
                    'created_by'        => 0,
                    'updated_by'        => 0,
                    'deleted_at'        => null,
                    'legal_entity'      => 1,
                    'balancing_entity'  => 1,
                    'status'            => 1,
                    'created_at'        => $currentDateTime,
                    'updated_at'        => null,
                ],
                [
                    'id'                => 3,
                    'org_id'            => 1,
                    'entity_id'         => 'RAMDEPT',
                    'entity_name'       => 'RAMDEPT',
                    'entity_description'=> 'RAMDEPT',
                    'created_by'        => 0,
                    'updated_by'        => 0,
                    'deleted_at'        => null,
                    'legal_entity'      => 1,
                    'balancing_entity'  => 1,
                    'status'            => 1,
                    'created_at'        => $currentDateTime,
                    'updated_at'        => null,
                ],
            ],
            ['id'],   // unique key for upsert
            ['org_id','entity_id','entity_name','entity_description','created_by',
             'updated_by','deleted_at','legal_entity','balancing_entity','status',
             'created_at','updated_at']
        );

        DB::table('sx_business_entity_value')->upsert(
            [
                [
                    'id'                       => 1,
                    'org_id'                   => 1,
                    'entity_id'                => 1,
                    'structure_id'             => 1,
                    'created_by'               => 0,
                    'updated_by'               => 0,
                    'deleted_at'               => null,
                    'entity_value'             => 'India',
                    'entity_value_id'          => 'SHXENV00000001',
                    'street'                   => 'Visakhapatnam',
                    'city'                     => 'Visakhapatnam',
                    'state'                    => 'Andhra Pradesh',
                    'country'                  => 'India',
                    'email_id'                 => '<EMAIL>',
                    'phone'                    => '9966314178',
                    'fax'                      => '56746545647',
                    'zipcode'                  => '530017',
                    'status'                   => 1,
                    'parent_entity_value_id'   => 0,
                    'created_at'               => $currentDateTime,
                    'updated_at'               => null,
                ],
                [
                    'id'                       => 2,
                    'org_id'                   => 1,
                    'entity_id'                => 2,
                    'structure_id'             => 1,
                    'created_by'               => 0,
                    'updated_by'               => 0,
                    'deleted_at'               => null,
                    'entity_value'             => 'Visakhapatnam',
                    'entity_value_id'          => 'SHXENV00000002',
                    'street'                   => 'Visakhapatnam',
                    'city'                     => 'Visakhapatnam',
                    'state'                    => 'Andhra Pradesh',
                    'country'                  => 'India',
                    'email_id'                 => '<EMAIL>',
                    'phone'                    => '9966314178',
                    'fax'                      => '8765766683',
                    'zipcode'                  => '530017',
                    'status'                   => 1,
                    'parent_entity_value_id'   => 1,
                    'created_at'               => $currentDateTime,
                    'updated_at'               => null,
                ],
                [
                    'id'                       => 3,
                    'org_id'                   => 1,
                    'entity_id'                => 3,
                    'structure_id'             => 1,
                    'created_by'               => 0,
                    'updated_by'               => 0,
                    'deleted_at'               => null,
                    'entity_value'             => 'Logistics',
                    'entity_value_id'          => 'SHXENV00000003',
                    'street'                   => 'Visakhapatnam',
                    'city'                     => 'Visakhapatnam',
                    'state'                    => 'Andhra Pradesh',
                    'country'                  => 'India',
                    'email_id'                 => '<EMAIL>',
                    'phone'                    => '9966314178',
                    'fax'                      => '567465456',
                    'zipcode'                  => '530017',
                    'status'                   => 1,
                    'parent_entity_value_id'   => 2,
                    'created_at'               => $currentDateTime,
                    'updated_at'               => null,
                ],
            ],
            ['id'],  // unique key for upsert
            [
                'org_id','entity_id','structure_id','created_by','updated_by',
                'deleted_at','entity_value','entity_value_id','street','city',
                'state','country','email_id','phone','fax','zipcode','status',
                'parent_entity_value_id','created_at','updated_at'
            ]
        );

        $previlleges = [
            [
                'id' => 1,
                'previllege_id' => 'RAMMG',
                'previllege_name' => 'Manager',
                'previllege_description' => 'company level manager role',
                'deleted_at' => null,
                'previlege_type' => 1,
                'party_type' => null,
                'business_entity' => 1,
                'org_id' => 1,
                'structure_id' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_previlleges')->upsert(
            $previlleges,
            ['id'], 
            ['previllege_id', 'previllege_name', 'previllege_description', 'deleted_at', 'previlege_type', 'party_type', 'business_entity', 'org_id', 'structure_id', 'status', 'created_at', 'updated_at']
        );


        $previllegeModules = [
            [
                'id' => 4,
                'previllege_id' => 1,
                'module_id' => 9,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 5,
                'previllege_id' => 1,
                'module_id' => 8,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 6,
                'previllege_id' => 1,
                'module_id' => 7,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 7,
                'previllege_id' => 1,
                'module_id' => 6,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 8,
                'previllege_id' => 1,
                'module_id' => 5,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 9,
                'previllege_id' => 1,
                'module_id' => 4,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 1,
                'previllege_id' => 1,
                'module_id' => 3,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 2,
                'previllege_id' => 1,
                'module_id' => 2,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 3,
                'previllege_id' => 1,
                'module_id' => 1,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_previllege_modules')->upsert(
            $previllegeModules,
            ['id'],
            ['previllege_id', 'module_id', 'deleted_at', 'status', 'created_at', 'updated_at']
        );  

        $accessRules = [
            [
                'id' => 1,
                'previllege_id' => 1,
                'previllege_module_id' => 3,
                'feature_id' => 74,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 2,
                'previllege_id' => 1,
                'previllege_module_id' => 3,
                'feature_id' => 75,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 3,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 41,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 4,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 42,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 5,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 43,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 6,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 44,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 7,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 38,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 8,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 39,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 9,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 40,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 10,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 45,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 11,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 46,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 12,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 47,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 13,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 48,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 14,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 5,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 15,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 6,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 16,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 7,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 17,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 8,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 18,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 9,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 19,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 10,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 20,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 11,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_previlege_access_rules')->upsert(
            $accessRules,
            ['id'], 
            ['previllege_id', 'previllege_module_id', 'feature_id', 'created_by', 'updated_by', 'deleted_at', 'add_access', 'modify_access', 'delete_access', 'view_access', 'status', 'created_at', 'updated_at']
        );

        $structureSequences = [
            [
                'id' => 1,
                'structure_id' => 1,
                'parent_business_entity' => 1,
                'child_business_entity' => 2,
                'created_by' => 0,
                'updated_by' => 0,
                'org_id' => 1,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 2,
                'structure_id' => 1,
                'parent_business_entity' => 2,
                'child_business_entity' => 3,
                'created_by' => 0,
                'updated_by' => 0,
                'org_id' => 1,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_structure_sequence')->upsert(
            $structureSequences,
            ['id'],
            ['structure_id', 'parent_business_entity', 'child_business_entity', 'created_by', 'updated_by', 'org_id', 'deleted_at', 'status', 'created_at', 'updated_at']
        );

        $users = [
            [
                'id' => 1,
                'employee_id' => 'RAMA001',
                'employee_name' => 'RAMA001',
                'username' => '<EMAIL>',
                'password' => '$2y$12$NeJ6jnndaomkpUU266dznOQiKgthEEUyuQ8ukgwI7X4QYv57IpYUa',
                'effective_fromdate' => '2025-07-08',
                'effective_enddate' => '2025-07-31',
                'contact_num' => '9966314178',
                'theme_id' => 1,
                'currency' => 'USD',
                'date_zone' => null,
                'number_format' => '1,22',
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'languages' => '["1"]',
                'date_format' => 'd/m/Y',
                'default_org_id' => 1,
                'country_code' => null,
                'default_currency' => null,
                'lat' => null,
                'lng' => null,
                'status' => 1,
                'emailid' => null,
                'refresh_token' => null,
                'geolocation' => null,
                'logo' => null,
                'created_at' => '2025-07-08 02:53:05',
                'updated_at' => '2025-07-08 02:53:05',
            ],
        ];

        DB::table('sx_users')->upsert(
            $users,
            ['id'],
            ['employee_id', 'employee_name', 'username', 'password', 'effective_fromdate', 'effective_enddate', 'contact_num', 'theme_id', 'currency', 'date_zone', 'number_format', 'created_by', 'updated_by', 'deleted_at', 'languages', 'date_format', 'default_org_id', 'country_code', 'default_currency', 'lat', 'lng', 'status', 'emailid', 'refresh_token', 'geolocation', 'logo', 'created_at', 'updated_at']
        );
        
        $userOrganizations = [
            [
                'id' => 1,
                'org_id' => 1,
                'structure_id' => 1,
                'entity_id' => 1,
                'entity_value_id' => 1,
                'roles' => '["1"]',
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'user_id' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_user_organizations')->upsert(
            $userOrganizations,
            ['id'],
            ['org_id', 'structure_id', 'entity_id', 'entity_value_id', 'roles', 'created_by', 'updated_by', 'deleted_at', 'user_id', 'status', 'created_at', 'updated_at']
        );

        $orderCreationSourceMasters =
            [
                [
                    'source_id' => 1,
                    'source_name' => 'DRIVER COLLECT',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 2,
                    'source_name' => 'CUST QUOTE',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 3,
                    'source_name' => 'QUICKBOOK',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 4,
                    'source_name' => 'GENERAL BOOKING / EXCEL UPLOAD',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 5,
                    'source_name' => 'SALOG',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 6,
                    'source_name' => 'SHIPMENT',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 7,
                    'source_name' => 'NZ EDI',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 8,
                    'source_name' => 'SHIPPEO',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 9,
                    'source_name' => 'QUOTE MODULE',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 10,
                    'source_name' => 'KNLOGIN',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 12,
                    'source_name' => 'MACADAM',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 13,
                    'source_name' => 'AMAZON EDI',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 14,
                    'source_name' => 'AU ASN EDI',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-10 11:49:54'),
                    'updated_at' => Carbon::parse('2022-01-10 11:49:54'),
                ],
                [
                    'source_id' => 15,
                    'source_name' => 'SWIFLOG',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 16,
                    'source_name' => 'SWIFLOG RETURN',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 17,
                    'source_name' => 'OPENBOXES',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 18,
                    'source_name' => 'ROADLOG',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
                [
                    'source_id' => 1000,
                    'source_name' => 'API TRIPMANAGEMENT',
                    'status' => 1,
                    'org_id' => 1,
                    'be_value' => 1,
                    'user_id' => 1,
                    'created_at' => Carbon::parse('2022-01-03 11:36:40'),
                    'updated_at' => Carbon::parse('2022-01-03 11:36:40'),
                ],
            ];
            DB::table('order_creation_source_masters')->upsert(
            $orderCreationSourceMasters,
            ['source_id'],
            ['source_name', 'status', 'org_id', 'be_value', 'user_id', 'created_at', 'updated_at']
        );

        DB::table('orders')->upsert(
            [
                [
                    'id' => 1,
                    'order_id' => 'ORD123456',
                    'pickup_datetime' => '2025-07-26 10:00:00',
                    'pickup_endtime' => '12:00 PM',
                    'delivery_datetime' => '2025-07-27 14:00:00',
                    'drop_endtime' => '4:00 PM',
                    'pickup_company' => 'ABC Logistics',
                    'delivery_company' => 'XYZ Retail',
                    'pickup_country' => 'USA',
                    'delivery_country' => 'USA',
                    'pickup_city' => 'New York',
                    'delivery_city' => 'Los Angeles',
                    'pickup_pincode' => '10001',
                    'delivery_pincode' => '90001',
                    'delivery_district_pincode' => '90002',
                    'delivery_area_pincode' => '90003',
                    'pickup_address1' => '123 Main St',
                    'delivery_address1' => '456 Market St',
                    'pickup_address2' => 'Suite 101',
                    'delivery_address2' => 'Floor 2',
                    'quantity' => 100.50,
                    'weight' => 500.75,
                    'gross_weight_uom' => 'kg',
                    'chargeable_weight' => 510.00,
                    'chargeable_weight_uom' => 'kg',
                    'volume' => 2.50,
                    'goods_value' => 1500.00,
                    'currency' => 'USD',
                    'transport_mode' => 'TL',
                    'dedicated_vehicle' => 1,
                    'vehicle_type' => 'Truck',
                    'user_id' => 1,
                    'sub_uid' => 2,
                    'customer_id' => 101,
                    'vendor_id' => 201,
                    'customer_name' => 'John Doe',
                    'customer_code' => 'CUST001',
                    'customer_area' => 'Downtown',
                    'customer_phone' => '+1234567890',
                    'customer_email' => '<EMAIL>',
                    'customer_comments' => 'Fragile items, handle with care',
                    'vendor_name' => 'Jane Smith',
                    'vendor_code' => 'VEND001',
                    'vendor_area' => 'Industrial Area',
                    'vendor_phone' => '+0987654321',
                    'vendor_email' => '<EMAIL>',
                    'vendor_comments' => 'Deliver after 2 PM',
                    'plat' => '40.7128',
                    'plng' => '-74.0060',
                    'dlat' => '34.0522',
                    'dlng' => '-118.2437',
                    'wlatitude' => '40.7128',
                    'wlongitude' => '-74.0060',
                    'org_id' => 1,
                    'be_value' => 1000,
                    'product' => 'Electronics',
                    'shipment_id' => 501,
                    'pickup_custid' => 'PCUST001',
                    'pickup_partyid' => 'PPARTY001',
                    'drop_custid' => 'DCUST001',
                    'drop_partyid' => 'DPARTY001',
                    'status' => 1,
                    'order_status' => 'Pending',
                    'is_created' => 1,
                    'trip_sts' => 0,
                    'shift_id' => 1,
                    'trip_id' => 1001,
                    'uniqid_of_package' => 'PKG123456',
                    'created_at' => $currentDateTime,
                    'updated_at' => $currentDateTime,
                    'category_id' => 1,
                    'parent_id' => 0,
                    'gba_rule' => 0,
                    'shipper_id' => 'SHIP001',
                    'consignee_id' => 'CONS001',
                    'created_source' => 1,
                    'shipmentid' => 'SHIPID001',
                    'logicalreceiver' => 'REC001',
                    'physicalreceiver' => 'PHYREC001',
                    'physicalsender' => 'PHYSEND001',
                    'logicalsender' => 'LOGSEND001',
                    'modeoftransport' => 'Road',
                    'hold_type' => 0,
                    'cargo_approval' => 0,
                    'credit_approval' => 0,
                    'resolution_id' => 0,
                    'assigned_to' => 1,
                    'Stoppage' => 'STOP001',
                    'external_order_id' => 'EXTORD001',
                    'sequence_number' => 1,
                    'destination_branch' => 'BRANCH001',
                    'order_status_category' => 1,
                    'shipment_type' => 1,
                    'region' => 1,
                    'payment_type' => 'Prepaid',
                    'third_party_post' => 'POST001',
                    'order_trips' => 'TRIP001',
                ],
            ],
            ['id'], // unique key for upsert
            [
                'order_id', 'pickup_datetime', 'pickup_endtime', 'delivery_datetime', 'drop_endtime',
                'pickup_company', 'delivery_company', 'pickup_country', 'delivery_country', 'pickup_city',
                'delivery_city', 'pickup_pincode', 'delivery_pincode', 'delivery_district_pincode',
                'delivery_area_pincode', 'pickup_address1', 'delivery_address1', 'pickup_address2',
                'delivery_address2', 'quantity', 'weight', 'gross_weight_uom', 'chargeable_weight',
                'chargeable_weight_uom', 'volume', 'goods_value', 'currency', 'transport_mode',
                'dedicated_vehicle', 'vehicle_type', 'user_id', 'sub_uid', 'customer_id', 'vendor_id',
                'customer_name', 'customer_code', 'customer_area', 'customer_phone', 'customer_email',
                'customer_comments', 'vendor_name', 'vendor_code', 'vendor_area', 'vendor_phone',
                'vendor_email', 'vendor_comments', 'plat', 'plng', 'dlat', 'dlng', 'wlatitude',
                'wlongitude', 'org_id', 'be_value', 'product', 'shipment_id', 'pickup_custid',
                'pickup_partyid', 'drop_custid', 'drop_partyid', 'status', 'order_status',
                'is_created', 'trip_sts', 'shift_id', 'trip_id', 'uniqid_of_package', 'created_at',
                'updated_at', 'category_id', 'parent_id', 'gba_rule', 'shipper_id', 'consignee_id',
                'created_source', 'shipmentid', 'logicalreceiver', 'physicalreceiver', 'physicalsender',
                'logicalsender', 'modeoftransport', 'hold_type', 'cargo_approval', 'credit_approval',
                'resolution_id', 'assigned_to', 'Stoppage', 'external_order_id', 'sequence_number',
                'destination_branch', 'order_status_category', 'shipment_type', 'region', 'payment_type',
                'third_party_post', 'order_trips',
            ]
        );

    }   
}
