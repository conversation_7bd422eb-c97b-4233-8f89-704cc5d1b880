<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carrier_allocation_rules', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('rule_name', 100);
            $table->integer('customer_id')->nullable();
            $table->string('description', 500)->nullable();
            $table->integer('shipment_type')->default(0);
            $table->decimal('min_weight', 10, 2);
            $table->decimal('max_weight', 10, 2);
            $table->integer('regions')->default(0);
            $table->string('priority_type', 60)->nullable();
            $table->integer('org_id')->default(0);
            $table->integer('be_value')->default(0);
            $table->integer('order_id')->default(0);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carrier_allocation_rules');
    }
};
