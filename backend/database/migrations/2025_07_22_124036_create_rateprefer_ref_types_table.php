<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rateprefer_ref_types', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('rate_prefer_id');
            $table->string('ref_id', 30);
            $table->string('ref_value', 30);
            $table->string('ref_condition', 10)->comment('AND,OR');
            $table->integer('org_id')->default(0);
            $table->integer('be_value')->default(0);
            $table->integer('order_id')->default(0);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();

            // Composite index
            $table->index(['rate_prefer_id', 'ref_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rateprefer_ref_types');
    }
};
?>