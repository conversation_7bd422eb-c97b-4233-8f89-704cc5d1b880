<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('return_trucks', function (Blueprint $table) {
            $table->bigIncrements('id'); 
            $table->integer('trip_id')->nullable(); 
            $table->integer('vehicle_id')->nullable(); 
            $table->integer('driver_id')->nullable(); 
            $table->integer('vendor_id')->nullable(); 
            $table->integer('customer_id')->nullable(); 
            $table->integer('shift_leg_id')->nullable(); 
            $table->string('origin_id', 255)->nullable(); 
            $table->string('destination_id', 255)->nullable(); 
            $table->integer('routetemplate_id')->nullable(); 
            $table->integer('zone_id')->nullable(); 
            $table->string('stime', 50)->nullable(); 
            $table->string('etime', 50)->nullable(); 
            $table->string('splace', 255)->nullable(); 
            $table->string('slat', 30)->nullable(); 
            $table->string('slng', 30)->nullable(); 
            $table->string('eplace', 255)->nullable(); 
            $table->string('elat', 30)->nullable(); 
            $table->string('elng', 30)->nullable(); 
            $table->string('scity', 100)->nullable(); 
            $table->string('dcity', 100)->nullable(); 
            $table->string('empshift_start', 60)->nullable(); 
            $table->string('empshift_end', 60)->nullable(); 
            $table->tinyInteger('trip_type')->default(0)->comment('0=regular trip,1=return trip,2=empty_trip'); 
            $table->timestamp('startdate')->nullable(); 
            $table->timestamp('enddate'); 
            $table->string('shipment_name', 160); 
            $table->string('shipmentid', 60); 
            $table->string('shipment_id', 60)->default('0'); 
            $table->string('transport_mode', 30)->default('TL'); 
            $table->string('domainname', 60); 
            $table->timestamp('schedule_date'); 
            $table->string('vehicle_type', 60);
            $table->integer('user_id')->nullable();
            $table->integer('org_id')->nullable(); 
            $table->string('be_value')->nullable(); 
            $table->tinyInteger('border_type')->default(0); 
            $table->text('carrier_instructions')->nullable(); 
            $table->tinyInteger('carrier_type')->default(1)->comment('0=single,1=multiple'); 
            $table->string('txnid', 60)->nullable(); 
            $table->decimal('weight', 10, 2)->default(0.00); 
            $table->decimal('volume', 10, 2)->default(0.00); 
            $table->decimal('units', 10, 2)->default(0.00); 
            $table->integer('interchange_control_reference')->default(0); 
            $table->text('weight_capacity')->nullable(); 
            $table->text('volume_capacity')->nullable(); 
            $table->text('additional_conditions')->nullable(); 
            $table->text('temperature_regime')->nullable(); 
            $table->text('time_for_loading_penality_rate')->nullable(); 
            $table->integer('is_carrier_notified')->default(0); 
            $table->boolean('aborted')->default(false);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'shipmentid', 'customer_id', 'vendor_id', 'status'], 'user_id_idx');
            $table->index('created_at', 'created_on_idx');
            $table->index('border_type', 'border_type_idx');
            $table->index('carrier_type', 'carrier_type_idx');
            $table->index(['user_id', 'shipmentid', 'customer_id', 'vendor_id', 'border_type', 'status', 'created_at'], 'user_id_mass_idx');
            $table->index('aborted', 'aborted_idx');
            $table->index('startdate', 'startdate_idx');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('return_trucks');
    }
};