<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('shift_zones', function (Blueprint $table) {
            $table->increments('id');
            $table->string('zone_name', 160);
            $table->string('splace', 255)->nullable();
            $table->string('slat', 60)->nullable();
            $table->string('slng', 60)->nullable();
            $table->string('eplace', 255)->nullable();
            $table->string('elat', 60)->nullable();
            $table->string('elng', 60)->nullable();
            $table->integer('user_id');
            $table->mediumText('description')->nullable();
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shift_zones');
    }
};
