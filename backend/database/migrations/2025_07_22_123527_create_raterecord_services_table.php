<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('raterecord_services', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('raterecord_id')->nullable();
            $table->integer('raterecord_vas_id')->nullable();
            $table->integer('raterecord_charge_id')->nullable();
            $table->string('charge_basis', 20)->nullable();
            $table->decimal('min_amount', 15, 6)->default(0.000000);
            $table->decimal('amount', 15, 6)->nullable();
            $table->string('currency', 10)->nullable();
            $table->integer('org_id')->default(0);
            $table->integer('be_value')->default(0);
            $table->integer('order_id')->default(0);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();

            // Composite index
            $table->index(['raterecord_id', 'raterecord_vas_id', 'raterecord_charge_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('raterecord_services');
    }
};
