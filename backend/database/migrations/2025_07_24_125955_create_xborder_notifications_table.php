<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('xborder_notifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('order_ref_id'); // Renamed from order_id (SQL) to avoid conflict
            $table->string('xborder_reference', 30);
            $table->unsignedBigInteger('org_id')->default(0);
            $table->unsignedBigInteger('be_value')->default(0);
            $table->unsignedBigInteger('order_id')->default(0); // Laravel-specific order_id
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('xborder_notifications');
    }
};
