<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pod_uploads', function (Blueprint $table) {
            $table->id();
            $table->integer('trip_id')->default(0);
            $table->integer('shipment_id')->default(0);
            $table->foreignId('stop_id')->nullable();
            $table->foreignId('stop_detail_id')->nullable();
            $table->string('stop_type', 11)->default('P');
            $table->string('doc_type', 20)->nullable();
            $table->string('imgpath', 255)->nullable();
            $table->string('pdfpath', 255)->nullable();
            $table->string('latitude', 60)->nullable();
            $table->string('longitude', 60)->nullable();
            $table->integer('user_id')->default(0);
            $table->integer('createdby')->default(0);
            $table->integer('order_id')->default(0);
            $table->string('status_code', 60)->nullable();
            $table->boolean('status')->default(true);
            $table->boolean('customer_pod_trigger')->default(false);
            $table->boolean('carrier_pod_trigger')->default(false);
            $table->dateTime('createdon');
            $table->timestamp('updatedon')->useCurrent()->useCurrentOnUpdate();
            $table->string('receiver_name', 30)->nullable();
            $table->integer('filesize')->nullable();
            $table->string('hash', 36)->nullable();

            // Indexes
            $table->index('trip_id');
            $table->index('shipment_id');
            $table->index('stop_id');
            $table->index('stop_detail_id');
            $table->index('stop_type');
            $table->index('user_id');
            $table->index('order_id');
            $table->index('status');
            $table->index('createdon');
            $table->index(['trip_id', 'stop_id']);
            $table->index(['shipment_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pod_uploads');
    }
};