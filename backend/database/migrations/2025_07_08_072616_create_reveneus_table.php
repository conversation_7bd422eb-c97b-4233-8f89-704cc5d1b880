<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reveneus', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('type')->nullable()->comment('0->role,1->cost');
            $table->integer('order_id')->default(0);
            $table->string('recipient_role', 60)->nullable();
            $table->string('recipient_code', 30)->nullable();
            $table->string('recipient_name', 100)->nullable();
            $table->string('chargeable_weight', 30)->nullable();
            $table->string('debtor_jfr', 30)->nullable();
            $table->timestamp('debitor_time')->nullable();
            $table->string('bu_jfr', 20)->default('0');
            $table->string('invoice_number', 90)->nullable();
            $table->string('credit_note_number', 90)->nullable();
            $table->string('invoice_date', 60)->nullable();
            $table->string('invoice_creation_date', 60)->nullable();
            $table->string('invoice_receivdon_date', 60)->nullable();
            $table->decimal('amount', 15, 4)->default(0.0000);
            $table->decimal('accrual_amount', 10, 2)->nullable();
            $table->decimal('actual_amount', 10, 2)->nullable();
            $table->string('currency', 30)->nullable();
            $table->string('exchange_rate', 30)->nullable();
            $table->string('foreign_currency', 22)->nullable();
            $table->decimal('accrual_foreign_amount', 10, 2)->nullable();
            $table->decimal('actual_foreign_amount', 10, 2)->nullable();
            $table->tinyInteger('invoice_status')->default(0);
            $table->integer('bill_id')->default(0);
            $table->mediumText('remarks')->nullable();
            $table->integer('parent_id')->nullable();
            $table->tinyInteger('status')->default(1);
            $table->integer('user_id')->default(0);
            $table->string('leg_id', 22)->nullable();
            $table->timestamps();
            $table->string('source_created', 100)->nullable();
            $table->integer('org_id')->nullable()->default(0);
            $table->integer('be_value')->nullable()->default(0);

            // Indexes
            $table->index(['order_id', 'status', 'user_id'], 'idx_reveneus_order_id_status_user_id');
            $table->index('type', 'idx_reveneus_type');
            $table->index(['debtor_jfr', 'invoice_status', 'bill_id'], 'idx_reveneus_debtor_jfr_invoice_status_bill_id');
            $table->index('invoice_status', 'idx_reveneus_invoice_status');
            $table->index('recipient_name', 'idx_reveneus_recipient_name');
            $table->index('recipient_role', 'idx_reveneus_recipient_role');
            $table->index('bill_id', 'idx_reveneus_bill_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('reveneus');
    }
};
