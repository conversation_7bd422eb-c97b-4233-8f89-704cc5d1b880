<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_status', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('order_ref_id', 50)->nullable(); // Renamed from order_id (varchar) to avoid conflict
            $table->integer('status_id')->nullable();
            $table->string('latitude', 60)->nullable();
            $table->string('longitude', 60)->nullable();
            $table->string('status_code', 60)->nullable();
            $table->dateTime('status_date')->nullable();
            $table->string('status_type', 255)->nullable(); // tinytext mapped to string
            $table->integer('shift_id')->nullable();
            $table->string('action_user', 30);
            $table->boolean('customer_status_trigger')->default(0);
            $table->boolean('carrier_status_trigger')->default(0);
            $table->integer('org_id')->default(0);
            $table->integer('be_value')->default(0);
            $table->integer('order_id')->default(0); // Laravel-specific order_id
            $table->tinyInteger('status')->default(1); // Laravel-specific status
            $table->string('status_reason', 25)->default('From Admin');
            $table->boolean('sentknlogin')->default(0);
            $table->string('comment', 255)->nullable();
            $table->string('ship_from', 255)->nullable();
            $table->string('ship_to', 255)->nullable();
            $table->string('current_location', 255)->nullable();
            $table->string('tracking_id', 60)->nullable();
            $table->string('reference_code', 30)->nullable();
            $table->text('reference_value')->nullable();
            $table->string('item_number', 60)->nullable();
            $table->string('equipment_type', 30)->nullable();
            $table->string('sequence_no', 30)->nullable();
            $table->string('weight_uom', 30)->nullable();
            $table->string('weight_value', 30)->nullable();
            $table->integer('package_count')->default(0);
            $table->string('package_type', 30)->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['order_ref_id', 'status_id', 'status'], 'order_id');
            $table->index(['status_code', 'status_date'], 'status_code');
            $table->index('status');
            $table->index('tracking_id');
            $table->index('action_user');
            $table->index('order_ref_id', 'order_id_2');
            $table->index('status_id');
            $table->index('status_code', 'status_code_2');
            $table->index(['order_ref_id', 'status_id', 'status_code', 'tracking_id'], 'idx_order_status_grouping');
            $table->index('created_at', 'createdon');
            $table->index(['order_ref_id', 'latitude', 'longitude', 'status_code', 'status_date'], 'idx_order_id_covering');
            $table->index(['order_ref_id', 'status'], 'idx_order_status_order_id_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_status');
    }
};
?>