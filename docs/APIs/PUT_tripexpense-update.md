Trip Expense API Documentation
PUT /api/tripexpense/{id}

Description
Updates an existing trip expense record.

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: PUT
Endpoint: {base_url}/api/tripexpense/1
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | application/json         | Yes      |

Body:
{
    "org_id": 1,
    "trip_id": 1,
    "amount": 600.00,
    "category": "Fuel",
    "date": "2025-07-22",
    "description": "Updated fuel refill"
}

Success Response
{
    "status": "success",
    "message": "Trip expense updated successfully",
    "data": {
        "id": 1,
        "trip_id": 1,
        "amount": 600.00
    }
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid required fields   |
| 404  | Trip expense not found             | No matching trip expense found       |
| 500  | Failed to update trip expense      | Database or server error             |

Notes
- Updates an existing trip expense record with the provided details.
- The id in the URL refers to the trip expense ID.
- Logs errors in writable/logs/ for debugging.

Tests
Functional Tests
- [Pass] Valid payload updates trip expense (status 200)
- [Pass] Invalid JSON payload returns 422
- [Pass] Missing org_id returns 422
- [Pass] Non-existent ID returns 404
- [Pass] Invalid JWT token returns 401

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204

Manual Verification
- [Pass] Tested in Postman with valid JWT token
- [Pass] Verified updated trip expense in database
- [Pass] Tested with tampered JWT to simulate expiry