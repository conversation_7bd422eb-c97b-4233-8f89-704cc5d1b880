# POST /api/get-shipment-stops

## Description
Get Shipments Stops. This endpoint is used to retrieve stops and their details for a specific shipment.

## Authorization
- Required: No

## Request

- **Method**: POST
- **Endpoint**: `http://localhost:8000/api/get-shipment-stops`
- **Headers**:

| Key             | Value                        | Required |
|-----------------|------------------------------|----------|
| Content-Type    | application/json             | Yes      |
| Accept-Language | `en` / `th` / other supported| Optional |

### Request Body

```json
{
    "shift_veh_id": "11",
    "shift_id": "11",
    "trip_id": "11",
    "timezone": "America/New_York"
}
```

## Success Response

```json
{
    "status": 1,
    "message": "Success",
    "data": [
        {
            "id": 1,
            "name": "Stop Name",
            "stopname": "Stop Name",
            "address": "Address",
            "plat": 12.3456,
            "plng": 78.9012,
            "ship_type": "",
            "shipment_weight": 10.00,
            "shipment_volume": 5.00,
            "shipment_units": 2,
            "startdate": 1672534800,
            "enddate": 1672538400,
            "priority": 1,
            "status": "N",
            "order_id": 1,
            "cargo": "Cargo Name",
            "cargo_units": "Cargo Units",
            "detail_id": 1,
            "last_stop": 0,
            "transport_mode": "FTL"
        }
    ]
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/get-shipment-stops' \
--header 'Content-Type: application/json' \
--data '{
    "shift_veh_id": "11",
    "shift_id": "11",
    "trip_id": "11",
    "timezone": "America/New_York"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Optional request parameters should be handled gracefully if omitted.
- Ensure proper token handling for password reset and session-sensitive flows.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing head

---
