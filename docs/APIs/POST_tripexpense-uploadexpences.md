Trip Expense API Documentation
POST /api/tripexpense/uploadexpences

Description
Uploads multiple trip expense records from a file (e.g., CSV, Excel).

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: POST
Endpoint: {base_url}/api/tripexpense/uploadexpences
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | multipart/form-data      | Yes      |

Body:
{
    "org_id": 1,
    "file": "<uploaded file>"
}

Success Response
{
    "status": "success",
    "message": "Trip expenses uploaded successfully",
    "data": {
        "uploaded_count": 5,
        "failed_count": 0
    }
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid required fields   |
| 500  | Failed to upload trip expenses     | Database or server error             |

Notes
- Uploads trip expense records from a file for the authenticated user's organization.
- Expects a file input in the request body.
- Logs errors in writable/logs/ for debugging.

Tests
Functional Tests
- [Pass] Valid file upload creates trip expenses (status 200)
- [Pass] Invalid file format returns 422
- [Pass] Missing org_id returns 422
- [Pass] Invalid JWT token returns 401

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204

Manual Verification
- [Pass] Tested in Postman with valid JWT token and file upload
- [Pass] Verified new trip expenses in database
- [Pass] Tested with tampered JWT to simulate expiry