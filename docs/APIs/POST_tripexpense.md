Trip Expense API Documentation
POST /api/tripexpense

Description
Creates a new trip expense record.

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: POST
Endpoint: {base_url}/api/tripexpense
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | application/json         | Yes      |

Body:
{
    "org_id": 1,
    "trip_id": 1,
    "amount": 500.00,
    "category": "Fuel",
    "date": "2025-07-22",
    "description": "Fuel refill"
}

Success Response
{
    "status": "success",
    "message": "Trip expense created successfully",
    "data": {
        "id": 1,
        "trip_id": 1,
        "amount": 500.00
    }
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid required fields   |
| 500  | Failed to create trip expense      | Database or server error             |

Notes
- Creates a new trip expense record with the provided details.
- Logs errors in writable/logs/ for debugging.

Tests
Functional Tests
- [Pass] Valid payload creates trip expense (status 200)
- [Pass] Invalid JSON payload returns 422
- [Pass] Missing org_id returns 422
- [Pass] Invalid JWT token returns 401

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204

Manual Verification
- [Pass] Tested in Postman with valid JWT token
- [Pass] Verified new trip expense in database
- [Pass] Tested with tampered JWT to simulate expiry