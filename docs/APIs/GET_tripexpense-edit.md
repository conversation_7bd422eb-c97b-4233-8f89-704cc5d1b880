Trip Expense API Documentation
GET /api/tripexpense/edit/{id}

Description
Prepares data for editing a specific trip expense (typically used for UI rendering).

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: GET
Endpoint: {base_url}/api/tripexpense/edit/1
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | application/json         | Yes      |

Success Response
{
    "status": "success",
    "data": {
        "id": 1,
        "trip_id": 1,
        "amount": 500.00,
        "category": "Fuel",
        "date": "2025-07-22",
        "description": "Fuel refill",
        "categories": ["Fuel", "Maintenance"]
    }
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid ID                |
| 404  | Trip expense not found             | No matching trip expense found       |
| 500  | Failed to fetch edit data          | Database or server error             |

Notes
- Used to fetch existing trip expense data and available categories for editing.
- No database changes are made by this endpoint.

Tests
Functional Tests
- [Pass] Valid ID returns trip expense data (status 200)
- [Pass] Invalid ID returns 422
- [Pass] Non-existent ID returns 404
- [Pass] Invalid JWT token returns 401

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204

Manual Verification
- [Pass] Tested in Postman with valid JWT token
- [Pass] Verified response contains correct trip expense data and categories