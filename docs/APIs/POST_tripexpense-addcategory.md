Trip Expense API Documentation
POST /api/tripexpense/addcategory

Description
Adds a new expense category for trip expenses.

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: POST
Endpoint: {base_url}/api/tripexpense/addcategory
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | application/json         | Yes      |

Body:
{
    "org_id": 1,
    "category": "Maintenance",
    "description": "Vehicle maintenance costs"
}

Success Response
{
    "status": "success",
    "message": "Expense category added successfully",
    "data": {
        "id": 1,
        "category": "Maintenance"
    }
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid required fields   |
| 500  | Failed to add expense category     | Database or server error             |

Notes
- Adds a new expense category for the authenticated user's organization.
- Logs errors in writable/logs/ for debugging.

Tests
Functional Tests
- [Pass] Valid payload adds category (status 200)
- [Pass] Invalid JSON payload returns 422
- [Pass] Missing org_id returns 422
- [Pass] Invalid JWT token returns 401

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204

Manual Verification
- [Pass] Tested in Postman with valid JWT token
- [Pass] Verified new category in database
- [Pass] Tested with tampered JWT to simulate expiry