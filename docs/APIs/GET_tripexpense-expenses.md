Trip Expense API Documentation
GET /api/tripexpense/expenses

Description
Retrieves a list of expense categories or records for trip expenses.

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: GET
Endpoint: {base_url}/api/tripexpense/expenses
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | application/json         | Yes      |

Query Parameters:
| Key          | Value                  | Required | Description                  |
|--------------|-------------------------|----------|------------------------------|
| org_id       | integer                | Yes      | Organization ID              |

Success Response
{
    "status": "success",
    "data": [
        {
            "id": 1,
            "category": "Fuel",
            "description": "Fuel costs"
        }
    ]
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid org_id            |
| 500  | Failed to fetch expenses           | Database or server error             |

Notes
- Returns expense categories or records for the authenticated user's organization.
- Logs errors in writable/logs/ for debugging.

Tests
Functional Tests
- [Pass] Valid JWT token and org_id returns list of expenses (status 200)
- [Pass] Invalid or expired JWT token returns 401
- [Pass] Missing org_id returns 422
- [Pass] Empty database returns empty array (status 200)

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204 with correct headers

Manual Verification
- [Pass] Tested in Postman with valid JWT token and org_id
- [Pass] Verified response contains expected expense fields
- [Pass] Tested with tampered JWT to simulate expiry