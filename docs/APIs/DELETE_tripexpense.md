Trip Expense API Documentation
DELETE /api/tripexpense/{id}

Description
Deletes a specific trip expense record.

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: DELETE
Endpoint: {base_url}/api/tripexpense/1
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | application/json         | Yes      |

Success Response
{
    "status": "success",
    "message": "Trip expense deleted successfully"
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid ID                |
| 404  | Trip expense not found             | No matching trip expense found       |
| 500  | Failed to delete trip expense      | Database or server error             |

Notes
- The id in the URL refers to the trip expense ID.
- Typically marks the record as deleted rather than physical deletion.
- Logs errors in writable/logs/ for debugging.

Tests
Functional Tests
- [Pass] Valid ID deletes trip expense (status 200)
- [Pass] Invalid ID returns 422
- [Pass] Non-existent ID returns 404
- [Pass] Invalid JWT token returns 401

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204

Manual Verification
- [Pass] Tested in Postman with valid JWT token
- [Pass] Verified trip expense marked as deleted
- [Pass] Tested with tampered JWT to simulate expiry