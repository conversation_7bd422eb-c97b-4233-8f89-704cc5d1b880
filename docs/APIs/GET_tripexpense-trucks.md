Trip Expense API Documentation
GET /api/tripexpense/trucks

Description
Retrieves a list of trucks available for trip expenses.

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: GET
Endpoint: {base_url}/api/tripexpense/trucks
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | application/json         | Yes      |

Query Parameters:
| Key          | Value                  | Required | Description                  |
|--------------|-------------------------|----------|------------------------------|
| org_id       | integer                | Yes      | Organization ID              |

Success Response
{
    "status": "success",
    "data": [
        {
            "id": 1,
            "register_number": "ABC123",
            "status": "Active"
        }
    ]
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid org_id            |
| 500  | Failed to fetch trucks             | Database or server error             |

Notes
- Returns all active trucks for the authenticated user's organization.
- Logs errors in writable/logs/ for debugging.

Tests
Functional Tests
- [Pass] Valid JWT token and org_id returns list of trucks (status 200)
- [Pass] Invalid or expired JWT token returns 401
- [Pass] Missing org_id returns 422
- [Pass] Empty database returns empty array (status 200)

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204 with correct headers

Manual Verification
- [Pass] Tested in Postman with valid JWT token and org_id
- [Pass] Verified response contains expected truck fields
- [Pass] Tested with tampered JWT to simulate expiry