Trip Expense API Documentation
GET /api/tripexpense/{id}

Description
Retrieves details of a specific trip expense by its ID.

Authorization
Required: Yes
Type: JWT Bearer Token

Request
Method: GET
Endpoint: {base_url}/api/tripexpense/1
Headers:

| Key            | Value                    | Required |
|----------------|--------------------------|----------|
| Authorization  | Bearer <JWT Token>       | Yes      |
| Content-Type   | application/json         | Yes      |

Success Response
{
    "status": "success",
    "data": {
        "id": 1,
        "trip_id": 1,
        "amount": 500.00,
        "category": "Fuel",
        "date": "2025-07-22",
        "description": "Fuel refill"
    }
}

Error Responses
| Code | Message                            | Description                          |
|------|------------------------------------|--------------------------------------|
| 401  | Invalid or missing token           | Invalid or missing JWT token         |
| 422  | Validation error                   | Missing or invalid ID                |
| 404  | Trip expense not found             | No matching trip expense found       |
| 500  | Failed to fetch trip expense       | Database or server error             |

Notes
- The id in the URL refers to the trip expense ID.
- Ensures the trip expense is accessible to the authenticated user's organization.

Tests
Functional Tests
- [Pass] Valid ID returns trip expense (status 200)
- [Pass] Invalid ID returns 422
- [Pass] Non-existent ID returns 404
- [Pass] Invalid JWT token returns 401

CORS & Header Behavior
- [Pass] Access-Control-Allow-Origin header present
- [Pass] Preflight OPTIONS request returns 204

Manual Verification
- [Pass] Tested in Postman with valid JWT token
- [Pass] Verified response matches database record
- [Pass] Tested with tampered JWT to simulate expiry