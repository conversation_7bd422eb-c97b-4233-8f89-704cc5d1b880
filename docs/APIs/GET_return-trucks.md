# GET /api/return-trucks

## Description
Retrieves a list of return trucks with optional date filtering. Includes vehicle, driver, customer, and carrier details, as well as datatable headers and settings for frontend display.

## Authorization
- Required: Yes
- Type: JWT <PERSON>er Token

## Request

- Method: GET
- Endpoint: `http://localhost:8000/api/return-trucks`
- Headers:

| Key           | Value                        | Required |
|---------------|------------------------------|----------|
| Authorization | Bearer <token>               | Yes      |
| Content-Type  | application/json             | Yes      |

### Query Parameters (optional)
| Name             | Type   | Description                        |
|------------------|--------|------------------------------------|
| fromdate_search  | string | Filter from date (YYYY-MM-DD)      |
| todate_search    | string | Filter to date (YYYY-MM-DD)        |

## Request Body: None

## Success Response

```json
{
    "status": "success",
    "message": "Return trucks retrieved successfully",
    "data": {
        "return_trucks": [
            {
                "id": 1,
                "vehicle_register_number": "",
                "driver_name": "",
                "customer_name": "",
                "carrier_name": "",
                "splace": "Warehouse A",
                "eplace": "Warehouse B",
                "startdate": 1753410600,
                "shipmentid": "SHIP123",
                "user_id": 1,
                "org_id": 2,
                "be_value": "BE_TEST",
                "status": 1
            },
            {
                "id": 2,
                "vehicle_register_number": "",
                "driver_name": "",
                "customer_name": "",
                "carrier_name": "",
                "splace": "Warehouse A",
                "eplace": "Warehouse B",
                "startdate": 1753410600,
                "shipmentid": "SHIP123",
                "user_id": 1,
                "org_id": 2,
                "be_value": "BE_TEST",
                "status": 1
            }
        ],
        "datatable_headers": [
            "Trip No.",
            "Vehicle Number",
            "Driver Name",
            "Customer Name",
            "Carrier Name",
            "Source",
            "Destination",
            "Pickup Date",
            "Shipment ID",
            "User ID",
            "Org ID",
            "Be Value"
        ],
        "column_visibility": [
            true,
            true,
            true,
            true,
            true,
            true,
            true,
            true,
            true,
            true
        ],
        "datatable_header_sequence": [],
        "datatable_header_toggle": [],
        "datatable_header_sequence_index": [
            0,
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            8,
            9
        ],
        "postData": []
    }
}
```

### cURL Example
```sh
curl -X GET "http://localhost:8000/api/return-trucks" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json"
```

---

## Error Responses

| Code | Message              | Description                |
|------|----------------------|----------------------------|
| 401  | Unauthorized         | Invalid or expired token   |
| 500  | Server Error         | Internal server issue      |

### Error Response Example
```json
{
    "status": "error",
    "message": "Error retrieving return trucks"
}
```

---

## Notes
- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Date filters are optional; if omitted, all records are returned.
- Uses Eloquent ORM for efficient querying and relationship handling.
- Logs errors in storage/logs/ for debugging.

## Tests

### Functional Tests
- [ ] Valid JWT token returns return trucks list (status 200)
- [ ] Invalid or expired JWT token returns 401
- [ ] Filtering by date returns correct results
- [ ] No return trucks returns empty list

### CORS & Header Behavior
- [ ] Confirmed Access-Control-Allow-Origin is set correctly
- [ ] CORS preflight (OPTIONS) request responds with correct headers

### Manual Verification
- [ ] Tested using Postman or curl
- [ ] Confirmed expected structure in API response
- [ ] Edge cases tested (e.g., empty DB, missing role) 